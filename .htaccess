# 将 RewriteEngine 模式打开
RewriteEngine On

# 修改以下语句中的 /discuz 为您的论坛目录地址，如果程序放在根目录中，请将 /discuz 修改为 /
RewriteBase /dx3.5utf8new

# Rewrite 系统规则请勿修改
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^topic-(.+)\.html$ portal.php?mod=topic&topic=$1&%1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^article-([0-9]+)-([0-9]+)\.html$ portal.php?mod=view&aid=$1&page=$2&%1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^forum-(\w+)-([0-9]+)\.html$ forum.php?mod=forumdisplay&fid=$1&page=$2&%1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^thread-([0-9]+)-([0-9]+)-([0-9]+)\.html$ forum.php?mod=viewthread&tid=$1&extra=page\%3D$3&page=$2&%1

#家园其他页面
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^homespace([a-z]+)\.html$ home.php?mod=space&do=home&view=$1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^homespace([a-z]+)([0-9]+)\.html$ home.php?mod=space&do=home&view=$1&gid=$2

#日志首页伪静态和日志其他页面
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^blogspace([0-9]*)\.html$ home.php?mod=space&do=blog&page=$1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^blogspace([a-z]+)([0-9]*)\.html$ home.php?mod=space&do=blog&view=$1&page=$2

#相册首页伪静态和相册其他页面
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^albumspace([0-9]*)\.html$ home.php?mod=space&do=album&page=$1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^albumspaceallhot([0-9]*)\.html$ home.php?mod=space&do=album&view=all&order=hot&page=$1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^albumspace([a-z]+)([0-9]*)\.html$ home.php?mod=space&do=album&view=$1&page=$2

#分享首页伪静态和分享其他页面
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^sharespace([0-9]*)\.html$ home.php?mod=space&do=share&page=$1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^sharespace([a-z]+)([0-9]*)\.html$ home.php?mod=space&do=share&view=$1&page=$2

#文章频道页
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^list-([0-9]+)-([0-9]+)\.html$ portal.php?mod=list&catid=$1&page=$2

#导读页面伪静态
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^guide([a-z0-9]+)-([0-9]+)\.html$ forum.php?mod=guide&view=$1&page=$2
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^guide([a-z0-9]+)\.html$ forum.php?mod=guide&view=$1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^guide\.html$ forum.php?mod=guide


#自订url伪静态名称 方式一
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^thread-(.+)-([0-9]+)-([0-9]+)\.html$ forum.php?mod=viewthread&tid=$1&extra=page\%3D$3&page=$2&%1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^article-(.+)-([0-9]+)\.html$ portal.php?mod=view&aid=$1&page=$2&%1

#自订url伪静态名称 方式二
#RewriteCond %{QUERY_STRING} ^(.*)$
#RewriteRule ^thread/(.+)/([0-9]+)/([0-9]+)\.html$ forum.php?mod=viewthread&tid=$1&extra=page\%3D$3&page=$2&%1
#RewriteCond %{QUERY_STRING} ^(.*)$
#RewriteRule ^article/(.+)/([0-9]+)\.html$ portal.php?mod=view&aid=$1&page=$2&%1

#RewriteCond %{QUERY_STRING} ^(.*)$
#RewriteRule ^f/(\w+)-([0-9]+)\.html$ forum.php?mod=forumdisplay&fid=$1&page=$2&%1
#RewriteCond %{QUERY_STRING} ^(.*)$
#RewriteRule ^t/([0-9]+)-([0-9]+)-([0-9]+)\.html$ forum.php?mod=viewthread&tid=$1&extra=page\%3D$3&page=$2&%1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^group-([0-9]+)-([0-9]+)\.html$ forum.php?mod=group&fid=$1&page=$2&%1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^space-(username|uid)-(.+)\.html$ home.php?mod=space&$1=$2&%1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^blog-([0-9]+)-([0-9]+)\.html$ home.php?mod=space&uid=$1&do=blog&id=$2&%1

#淘帖
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^collection([0-9]*)\.html$ forum.php?mod=collection&page=$1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^collectionall([0-9]*)\.html$ forum.php?mod=collection&op=all&page=$1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^collectionmy([0-9]*)\.html$ forum.php?mod=collection&op=my&page=$1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^collectionview-([0-9]+)-([0-9]+)\.html$ forum.php?mod=collection&action=view&ctid=$1&page=$2

RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^(.*)/(fid|tid)-([0-9]+)\.html$ $1/archiver/index.php?action=$2&value=$3&%1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^(fid|tid)-([0-9]+)\.html$ archiver/index.php?action=$1&value=$2&%1

#版块最新
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^forum-lastpost-(\w+)-([0-9]+)\.html$ forum.php?mod=forumdisplay&fid=$1&orderby=lastpost&filter=lastpost&orderby=lastpost&page=$2%1
#版块最新end
#版块热门
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^forum-heat-(\w+)-([0-9]+)\.html$ forum.php?mod=forumdisplay&fid=$1&filter=heat&orderby=heats&page=$2%1
#版块热门end
#版块热贴
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^forum-hot-(\w+)-([0-9]+)\.html$ forum.php?mod=forumdisplay&fid=$1&filter=hot&page=$2%1
#版块热贴end

#只看该作者#
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^thread-author-([0-9]+)-([0-9]+)-([0-9]+)\.html$ forum.php?mod=viewthread&tid=$1&page=$2&authorid=$3
#只看该作者end#
#主题只看大图#
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^thread-album-([0-9]+)\.html$ forum.php?mod=viewthread&tid=$1&from=album
#主题只看大图end#
#主题倒序#
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^thread-ordertype-([0-9]+)-([0-9]+)-([0-9]+)\.html$ forum.php?mod=viewthread&tid=$1&extra=page%3D$3&ordertype=1&page=$2
#主题倒序end#

RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^([a-z]+[a-z0-9_]*)-([a-z0-9_\-]+)\.html$ plugin.php?id=$1:$2&%1

RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^mip/thread-([0-9]+)-([0-9]+)-([0-9]+).html$ plugin.php?id=apoyl_baidumip&mod=viewthread&tid=$1&page=$2
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^mip/forum-(\w+)-([0-9]+)\.html$ plugin.php?id=apoyl_baidumip&mod=forumdisplay&fid=$1&page=$2&%1

#tagmobile
RewriteCond %{HTTP_USER_AGENT} "(baidu.Transcoder|mini|Android|blackberry|googlebot-mobile|iemobile|Mobile|ipad|iphone|ipod|opera mobile|palmos|webos|ucweb|Windows Phone|Symbian|hpwOS)" [NC]
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^tag([0-9]+)\.html$ plugin.php?id=apoyl_telfunc&mod=tag&tagid=$1&%1
RewriteRule ^tag([0-9]+)page([0-9]+)\.html$ plugin.php?id=apoyl_telfunc&mod=tag&tagid=$1&page=$2&%1
#tag
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^tag([0-9]+)\.html$ misc.php?mod=tag&id=$1
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^tagthread([0-9]+)\.html$ misc.php?mod=tag&id=$1&type=thread
RewriteRule ^tagthread([0-9]+)page([0-9]+)\.html$ misc.php?mod=tag&id=$1&type=thread&page=$2
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^tag\.html$ misc.php?mod=tag


#主题分类
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^typeid_([0-9]+)_([0-9]+)_([0-9]+).html$ forum.php?mod=forumdisplay&fid=$1&filter=typeid&typeid=$2&page=$3

#分区版块
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^gid_([0-9]+)\.html$ forum.php?gid=$1

#首页论坛
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^forum.html$ forum.php



#群组分区版块
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^sgid_([0-9]+)\.html$ group.php?sgid=$1

#家园首页
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^home.html$ home.php

#群组首页
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^group.html$ group.php

#群组分类
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^groupgid([0-9]+)\.html$ group.php?gid=$1
RewriteRule ^groupgid([0-9]+)page([0-9]+)\.html$ group.php?gid=$1&orderby=displayorder&page=$2

#广播首页和其他页面
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^follow\.html$ home.php?mod=follow
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^follow([a-z]+)\.html$ home.php?mod=follow&view=$1

#排行榜首页
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^ranklist\.html$ misc.php?mod=ranklist
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^ranklistmember([a-z]*)\.html$ misc.php?mod=ranklist&type=member&view=$1

#分类信息
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^forum([0-9]+)sortid([0-9]+)page([0-9]+)\.html$ forum.php?mod=forumdisplay&fid=$1&filter=sortid&sortid=$2&page=$3

#RewriteCond %{QUERY_STRING} ^(.*)$
#RewriteRule ^forum([0-9]+)sortid([0-9]+)([a-z0-9=]+)page([0-9]+)\.html$ forum.php?mod=forumdisplay&fid=$1&filter=sortid&sortid=$2&searchsort=1&$3&page=$4

#门户首页
RewriteCond %{QUERY_STRING} ^(.*)$
RewriteRule ^portal.html$ portal.php



