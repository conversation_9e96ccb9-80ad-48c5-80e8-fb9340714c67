function _ajaxget(url, showid, waitid, loading, display, recall) {waitid = typeof waitid == 'undefined' || waitid === null ? showid : waitid;var x = new Ajax();x.setLoading(loading);x.setWaitId(waitid);x.display = typeof display == 'undefined' || display == null ? '' : display;x.showId = $(showid);if(url.substr(strlen(url) - 1) == '#') {url = url.substr(0, strlen(url) - 1);x.autogoto = 1;}var url = url + '&inajax=1&ajaxtarget=' + showid;x.get(url, function(s, x) {var evaled = false;if(s.indexOf('ajaxerror') != -1) {evalscript(s);evaled = true;}if(!evaled && (typeof ajaxerror == 'undefined' || !ajaxerror)) {if(x.showId) {x.showId.style.display = x.display;ajaxinnerhtml(x.showId, s);ajaxupdateevents(x.showId);if(x.autogoto) scroll(0, x.showId.offsetTop);}}ajaxerror = null;if(recall && typeof recall == 'function') {recall();} else if(recall) {eval(recall);}if(!evaled) evalscript(s);});}function _ajaxpost(formid, showid, waitid, showidclass, submitbtn, recall) {var waitid = typeof waitid == 'undefined' || waitid === null ? showid : (waitid !== '' ? waitid : '');var showidclass = !showidclass ? '' : showidclass;var ajaxframeid = 'ajaxframe';var ajaxframe = $(ajaxframeid);var curform = $(formid);var formtarget = curform.target;var handleResult = function() {var s = '';var evaled = false;showloading('none');try {s = $(ajaxframeid).contentWindow.document.XMLDocument.text;} catch(e) {try {s = $(ajaxframeid).contentWindow.document.documentElement.firstChild.wholeText;} catch(e) {try {s = $(ajaxframeid).contentWindow.document.documentElement.firstChild.nodeValue;} catch(e) {s = '内部错误，无法显示此内容';}}}if(s != '' && s.indexOf('ajaxerror') != -1) {evalscript(s);evaled = true;}if(showidclass) {if(showidclass != 'onerror') {$(showid).className = showidclass;} else {showError(s);ajaxerror = true;}}if(submitbtn) {submitbtn.disabled = false;}if(!evaled && (typeof ajaxerror == 'undefined' || !ajaxerror)) {ajaxinnerhtml($(showid), s);}ajaxerror = null;if(curform) curform.target = formtarget;if(typeof recall == 'function') {recall();} else {eval(recall);}if(!evaled) evalscript(s);ajaxframe.loading = 0;if(!BROWSER.firefox || BROWSER.safari) {$('append_parent').removeChild(ajaxframe.parentNode);} else {setTimeout(function(){$('append_parent').removeChild(ajaxframe.parentNode);},100);}};if(!ajaxframe) {var div = document.createElement('div');div.style.display = 'none';div.innerHTML = '<iframe name="' + ajaxframeid + '" id="' + ajaxframeid + '" loading="1"></iframe>';$('append_parent').appendChild(div);ajaxframe = $(ajaxframeid);} else if(ajaxframe.loading) {return false;}_attachEvent(ajaxframe, 'load', handleResult);showloading();curform.target = ajaxframeid;var action = curform.getAttribute('action');action = hostconvert(action);curform.action = action.replace(/\&inajax\=1/g, '')+'&inajax=1';curform.submit();if(submitbtn) {submitbtn.disabled = true;}doane();return false;}function _ajaxmenu(ctrlObj, timeout, cache, duration, pos, recall, idclass, contentclass) {if(!ctrlObj.getAttribute('mid')) {var ctrlid = ctrlObj.id;if(!ctrlid) {ctrlObj.id = 'ajaxid_' + Math.random();}} else {var ctrlid = ctrlObj.getAttribute('mid');if(!ctrlObj.id) {ctrlObj.id = 'ajaxid_' + Math.random();}}var menuid = ctrlid + '_menu';var menu = $(menuid);if(isUndefined(timeout)) timeout = 3000;if(isUndefined(cache)) cache = 1;if(isUndefined(pos)) pos = '43';if(isUndefined(duration)) duration = timeout > 0 ? 0 : 3;if(isUndefined(idclass)) idclass = 'p_pop';if(isUndefined(contentclass)) contentclass = 'p_opt';var func = function() {showMenu({'ctrlid':ctrlObj.id,'menuid':menuid,'duration':duration,'timeout':timeout,'pos':pos,'cache':cache,'layer':2});if(typeof recall == 'function') {recall();} else {eval(recall);}};if(menu) {if(menu.style.display == '') {hideMenu(menuid);} else {func();}}menu = document.createElement('div');menu.id = menuid;menu.style.display = 'none';menu.className = idclass;menu.innerHTML = '<div class="' + contentclass + '" id="' + menuid + '_content"></div>';$('append_parent').appendChild(menu);var url = (!isUndefined(ctrlObj.attributes['shref']) ? ctrlObj.attributes['shref'].value : (!isUndefined(ctrlObj.href) ? ctrlObj.href : ctrlObj.attributes['href'].value));url += (url.indexOf('?') != -1 ? '&' :'?') + 'ajaxmenu=1';ajaxget(url, menuid + '_content', 'ajaxwaitid', '', '', func);doane();}function _appendscript(src, text, reload, charset) {var id = hash(src + text);if(!reload && in_array(id, evalscripts)) return;if(reload && $(id)) {$(id).parentNode.removeChild($(id));}evalscripts.push(id);var scriptNode = document.createElement("script");scriptNode.type = "text/javascript";scriptNode.id = id;scriptNode.charset = charset ? charset : (BROWSER.firefox ? document.characterSet : document.charset);try {if(src) {scriptNode.src = src;scriptNode.onloadDone = false;scriptNode.onload = function () {scriptNode.onloadDone = true;JSLOADED[src] = 1;};scriptNode.onreadystatechange = function () {if((scriptNode.readyState == 'loaded' || scriptNode.readyState == 'complete') && !scriptNode.onloadDone) {scriptNode.onloadDone = true;JSLOADED[src] = 1;}};} else if(text){scriptNode.text = text;}document.getElementsByTagName('head')[0].appendChild(scriptNode);} catch(e) {}}function _ajaxupdateevents(obj, tagName) {tagName = tagName ? tagName : 'A';var objs = obj.getElementsByTagName(tagName);for(k in objs) {var o = objs[k];ajaxupdateevent(o);}}function _ajaxupdateevent(o) {if(typeof o == 'object' && o.getAttribute) {if(o.getAttribute('ajaxtarget')) {if(!o.id) o.id = Math.random();var ajaxevent = o.getAttribute('ajaxevent') ? o.getAttribute('ajaxevent') : 'click';var ajaxurl = o.getAttribute('ajaxurl') ? o.getAttribute('ajaxurl') : o.href;_attachEvent(o, ajaxevent, newfunction('ajaxget', ajaxurl, o.getAttribute('ajaxtarget'), o.getAttribute('ajaxwaitid'), o.getAttribute('ajaxloading'), o.getAttribute('ajaxdisplay')));if(o.getAttribute('ajaxfunc')) {o.getAttribute('ajaxfunc').match(/(\w+)\((.+?)\)/);_attachEvent(o, ajaxevent, newfunction(RegExp.$1, RegExp.$2));}}}}function _ajaxinnerhtml(showid, s) {if(showid.tagName != 'TBODY') {showid.innerHTML = s;} else {while(showid.firstChild) {showid.firstChild.parentNode.removeChild(showid.firstChild);}var div1 = document.createElement('DIV');div1.id = showid.id+'_div';div1.innerHTML = '<table><tbody id="'+showid.id+'_tbody">'+s+'</tbody></table>';$('append_parent').appendChild(div1);var trs = div1.getElementsByTagName('TR');var l = trs.length;for(var i=0; i<l; i++) {showid.appendChild(trs[0]);}var inputs = div1.getElementsByTagName('INPUT');var l = inputs.length;for(var i=0; i<l; i++) {showid.appendChild(inputs[0]);}div1.parentNode.removeChild(div1);}}