var SWFUpload;if (typeof(SWFUpload) === "function") {SWFUpload.queue = {};SWFUpload.prototype.initSettings = (function (oldInitSettings) {return function (userSettings) {if (typeof(oldInitSettings) === "function") {oldInitSettings.call(this, userSettings);}this.queueSettings = {};this.queueSettings.queue_cancelled_flag = false;this.queueSettings.queue_upload_count = 0;this.queueSettings.user_upload_complete_handler = this.settings.upload_complete_handler;this.queueSettings.user_upload_start_handler = this.settings.upload_start_handler;this.settings.upload_complete_handler = SWFUpload.queue.uploadCompleteHandler;this.settings.upload_start_handler = SWFUpload.queue.uploadStartHandler;this.settings.queue_complete_handler = userSettings.queue_complete_handler || null;};})(SWFUpload.prototype.initSettings);SWFUpload.prototype.startUpload = function (fileID) {this.queueSettings.queue_cancelled_flag = false;this.callFlash("StartUpload", [fileID]);};SWFUpload.prototype.cancelQueue = function () {this.queueSettings.queue_cancelled_flag = true;this.stopUpload();var stats = this.getStats();while (stats.files_queued > 0) {this.cancelUpload();stats = this.getStats();}};SWFUpload.queue.uploadStartHandler = function (file) {var returnValue;if (typeof(this.queueSettings.user_upload_start_handler) === "function") {returnValue = this.queueSettings.user_upload_start_handler.call(this, file);}returnValue = (returnValue === false) ? false : true;this.queueSettings.queue_cancelled_flag = !returnValue;return returnValue;};SWFUpload.queue.uploadCompleteHandler = function (file) {var user_upload_complete_handler = this.queueSettings.user_upload_complete_handler;var continueUpload;if (file.filestatus === SWFUpload.FILE_STATUS.COMPLETE) {this.queueSettings.queue_upload_count++;}if (typeof(user_upload_complete_handler) === "function") {continueUpload = (user_upload_complete_handler.call(this, file) === false) ? false : true;} else if (file.filestatus === SWFUpload.FILE_STATUS.QUEUED) {continueUpload = false;} else {continueUpload = true;}if (continueUpload) {var stats = this.getStats();if (stats.files_queued > 0 && this.queueSettings.queue_cancelled_flag === false) {this.startUpload();} else if (this.queueSettings.queue_cancelled_flag === false) {this.queueEvent("queue_complete_handler", [this.queueSettings.queue_upload_count]);this.queueSettings.queue_upload_count = 0;} else {this.queueSettings.queue_cancelled_flag = false;this.queueSettings.queue_upload_count = 0;}}};}