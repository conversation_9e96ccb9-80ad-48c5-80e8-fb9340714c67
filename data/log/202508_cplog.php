<?PHP exit;?>	1754015119	admin	1	127.0.0.1		GET={}; POST={};
<?PHP exit;?>	1754015120	admin	1	127.0.0.1	index	GET={}; POST={};
<?PHP exit;?>	1754015123	admin	1	127.0.0.1	plugins	GET={}; POST={};
<?PHP exit;?>	1754015129	admin	1	127.0.0.1	plugins	GET={operation=edit; pluginid=113; }; POST={};
<?PHP exit;?>	1754015136	admin	1	127.0.0.1	plugins	GET={operation=export; pluginid=113; }; POST={};
<?PHP exit;?>	1754015155	admin	1	127.0.0.1	plugins	GET={operation=export; pluginid=113; }; POST={};
<?PHP exit;?>	1754015656	admin	1	127.0.0.1	plugins	GET={operation=config; do=113; }; POST={};
<?PHP exit;?>	1754016483	admin	1	127.0.0.1	plugins	GET={}; POST={};
<?PHP exit;?>	1754016487	admin	1	127.0.0.1	plugins	GET={operation=config; do=48; }; POST={};
<?PHP exit;?>	1754016536	admin	1	127.0.0.1	plugins	GET={}; POST={};
<?PHP exit;?>	1754016537	admin	1	127.0.0.1	plugins	GET={}; POST={};
<?PHP exit;?>	1754016545	admin	1	127.0.0.1	plugins	GET={operation=config; do=83; }; POST={};
<?PHP exit;?>	**********	admin	1	127.0.0.1	plugins	GET={operation=config; do=83; varsnew={selectsms=1; keyid=LTAI5tCgBKBZLJdwHodKH329; keysecret=******************************; signature=凹凸曼; tplcode=SMS_465404402; limitsent=5; groups={0=1; }; forceavatar={}; opencardnum=1; openfrontcard=1; openbackcard=1; openhandcard=1; opengroup=1; openreg=1; openicon=1; opensendsms=1; openblog=1; aliyunintels=1; teltips=手机号; openname=1; openalbum=1; openlicense=1; opensms=1; openview=1; }; editsubmit=提交; }; POST={varsnew={selectsms=1; keyid=LTAI5tCgBKBZLJdwHodKH329; keysecret=******************************; signature=凹凸曼; tplcode=SMS_465404402; limitsent=5; groups={0=1; }; forceavatar={}; opencardnum=1; openfrontcard=1; openbackcard=1; openhandcard=1; opengroup=1; openreg=1; openicon=1; opensendsms=1; openblog=1; aliyunintels=1; teltips=手机号; openname=1; openalbum=1; openlicense=1; opensms=1; openview=1; }; editsubmit=提交; };
<?PHP exit;?>	**********	admin	1	127.0.0.1	plugins	GET={operation=config; do=83; }; POST={};
<?PHP exit;?>	**********	admin	1	127.0.0.1	members	GET={operation=search; }; POST={};
<?PHP exit;?>	**********	admin	1	127.0.0.1	members	GET={operation=add; }; POST={};
<?PHP exit;?>	**********	admin	1	127.0.0.1	members	GET={operation=add; newusername=nihao1; newpassword=nihao1; newgroupid=10; }; POST={newusername=nihao1; newpassword=nihao1; newgroupid=10; };
<?PHP exit;?>	**********	admin	1	127.0.0.1	members	GET={operation=add; }; POST={};
<?PHP exit;?>	**********	admin	1	127.0.0.1	members	GET={operation=add; newusername=nihao1; newpassword=nihao1; newemail=<EMAIL>; newgroupid=10; }; POST={newusername=nihao1; newpassword=nihao1; newemail=<EMAIL>; newgroupid=10; };
<?PHP exit;?>	**********	admin	1	127.0.0.1	members	GET={operation=add; }; POST={};
<?PHP exit;?>	**********	admin	1	127.0.0.1	members	GET={operation=add; newusername=nihao10; newpassword=nihao10; newemail=<EMAIL>; newgroupid=10; }; POST={newusername=nihao10; newpassword=nihao10; newemail=<EMAIL>; newgroupid=10; };
<?PHP exit;?>	1754016702	admin	1	127.0.0.1	members	GET={operation=add; }; POST={};
<?PHP exit;?>	1754017178	admin	1	127.0.0.1		GET={}; POST={};
<?PHP exit;?>	1754017179	admin	1	127.0.0.1	index	GET={}; POST={};
<?PHP exit;?>	1754017299	admin	1	127.0.0.1	plugins	GET={}; POST={};
<?PHP exit;?>	1754018478	admin	1	127.0.0.1	plugins	GET={operation=config; do=113; }; POST={};
<?PHP exit;?>	1754018482	admin	1	127.0.0.1	plugins	GET={operation=config; do=113; varsnew={openpc=1; forums={0=5; }; grouplyopas={0=1; }; mbwidth=100%; mbheight=100%; pcwidth=100%; pcheight=400; openftp=1; }; editsubmit=提交; }; POST={varsnew={openpc=1; forums={0=5; }; grouplyopas={0=1; }; mbwidth=100%; mbheight=100%; pcwidth=100%; pcheight=400; openftp=1; }; editsubmit=提交; };
<?PHP exit;?>	1754018514	admin	1	127.0.0.1	plugins	GET={operation=config; do=113; }; POST={};
<?PHP exit;?>	1754018791	admin	1	127.0.0.1	plugins	GET={operation=config; do=113; varsnew={openpc=1; forums={0=5; }; grouplyopas={0=1; }; mbwidth=100%; mbheight=100%; pcwidth=100%; pcheight=400; }; editsubmit=提交; }; POST={varsnew={openpc=1; forums={0=5; }; grouplyopas={0=1; }; mbwidth=100%; mbheight=100%; pcwidth=100%; pcheight=400; }; editsubmit=提交; };
<?PHP exit;?>	1754018829	admin	1	127.0.0.1	plugins	GET={operation=config; do=113; }; POST={};
<?PHP exit;?>	1754274242	admin	1	127.0.0.1		GET={}; POST={};
<?PHP exit;?>	1754274242	admin	1	127.0.0.1	index	GET={}; POST={};
<?PHP exit;?>	1754274244	admin	1	127.0.0.1	plugins	GET={}; POST={};
<?PHP exit;?>	1754274857	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; }; POST={};
<?PHP exit;?>	1754274860	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; identifier=apoyl_snsvideo; pmod=adminvideo; }; POST={};
<?PHP exit;?>	1754274861	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; identifier=apoyl_snsvideo; pmod=admin; }; POST={};
<?PHP exit;?>	1754274862	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; identifier=apoyl_snsvideo; pmod=adminvideo; }; POST={};
<?PHP exit;?>	1754274867	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; }; POST={};
<?PHP exit;?>	1754274871	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; identifier=apoyl_snsvideo; pmod=adminvideo; }; POST={};
<?PHP exit;?>	1754274916	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; identifier=apoyl_snsvideo; pmod=admincomment; }; POST={};
<?PHP exit;?>	1754274917	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; identifier=apoyl_snsvideo; pmod=adminvideo; }; POST={};
<?PHP exit;?>	1754275001	admin	1	127.0.0.1		GET={}; POST={};
<?PHP exit;?>	1754275003	admin	1	127.0.0.1	index	GET={}; POST={};
<?PHP exit;?>	1754275146	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; identifier=apoyl_snsvideo; pmod=adminvideo; }; POST={};
<?PHP exit;?>	1754275149	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; identifier=apoyl_snsvideo; pmod=admin; }; POST={};
<?PHP exit;?>	1754275149	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; }; POST={};
<?PHP exit;?>	1754275155	admin	1	127.0.0.1	plugins	GET={}; POST={};
<?PHP exit;?>	1754275158	admin	1	127.0.0.1	plugins	GET={operation=edit; pluginid=111; }; POST={};
<?PHP exit;?>	1754275164	admin	1	127.0.0.1	plugins	GET={operation=edit; type=common; pluginid=111; namenew=[凹凸曼]爱刷短视频; versionnew=1.1.0; identifiernew=apoyl_snsvideo; directorynew=apoyl_snsvideo/; descriptionnew=平常喜欢刷视频因此做了刷视频的，支持视频发布、点赞、收藏、分享、评论功能; langexists=1; editsubmit=提交; }; POST={namenew=[凹凸曼]爱刷短视频; versionnew=1.1.0; identifiernew=apoyl_snsvideo; directorynew=apoyl_snsvideo/; descriptionnew=平常喜欢刷视频因此做了刷视频的，支持视频发布、点赞、收藏、分享、评论功能; langexists=1; editsubmit=提交; };
<?PHP exit;?>	1754275195	admin	1	127.0.0.1	plugins	GET={operation=edit; pluginid=111; }; POST={};
<?PHP exit;?>	1754275330	admin	1	127.0.0.1		GET={}; POST={};
<?PHP exit;?>	1754275331	admin	1	127.0.0.1	index	GET={}; POST={};
<?PHP exit;?>	1754275332	admin	1	127.0.0.1	plugins	GET={}; POST={};
<?PHP exit;?>	1754275341	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; }; POST={};
<?PHP exit;?>	1754275343	admin	1	127.0.0.1	plugins	GET={operation=config; do=113; }; POST={};
<?PHP exit;?>	1754275345	admin	1	127.0.0.1	plugins	GET={operation=config; do=104; }; POST={};
<?PHP exit;?>	1754275346	admin	1	127.0.0.1	plugins	GET={operation=config; do=109; }; POST={};
<?PHP exit;?>	1754275348	admin	1	127.0.0.1	plugins	GET={operation=config; do=103; }; POST={};
<?PHP exit;?>	1754275357	admin	1	127.0.0.1	plugins	GET={operation=config; do=113; }; POST={};
<?PHP exit;?>	1754275357	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; }; POST={};
<?PHP exit;?>	1754275396	admin	1	127.0.0.1	plugins	GET={operation=edit; type=vars; pluginid=111; anchor=vars; displayordernew={1532=1; 1533=2; 1535=3; 1534=6; }; newdisplayorder=7; newtitle=手机版刷短视频组件; newvariable=openmb; newtype=radio; editsubmit=提交; }; POST={anchor=vars; displayordernew={1532=1; 1533=2; 1535=3; 1534=6; }; newdisplayorder=7; newtitle=手机版刷短视频组件; newvariable=openmb; newtype=radio; editsubmit=提交; };
<?PHP exit;?>	1754275398	admin	1	127.0.0.1	plugins	GET={operation=config; do=113; }; POST={};
<?PHP exit;?>	1754275398	admin	1	127.0.0.1	plugins	GET={frames=yes; operation=config; do=113; }; POST={};
<?PHP exit;?>	1754275398	admin	1	127.0.0.1	plugins	GET={operation=config; do=113; }; POST={};
<?PHP exit;?>	1754275429	admin	1	127.0.0.1	plugins	GET={operation=edit; pluginid=111; anchor=vars; }; POST={};
<?PHP exit;?>	1754275432	admin	1	127.0.0.1	plugins	GET={operation=vars; pluginid=111; pluginvarid=1552; }; POST={};
<?PHP exit;?>	1754275503	admin	1	127.0.0.1	plugins	GET={operation=vars; pluginid=111; pluginvarid=1552; titlenew=手机版刷短视频组件; descriptionnew=手机版刷短视频，手机上点赞、点关注、收藏、评论等，需安装扩展组件--手机版刷短视频组件,才能正常使用; typenew=radio; variablenew=openmb; varsubmit=提交; }; POST={titlenew=手机版刷短视频组件; descriptionnew=手机版刷短视频，手机上点赞、点关注、收藏、评论等，需安装扩展组件--手机版刷短视频组件,才能正常使用; typenew=radio; variablenew=openmb; varsubmit=提交; };
<?PHP exit;?>	1754275530	admin	1	127.0.0.1	plugins	GET={operation=edit; pluginid=111; anchor=vars; }; POST={};
<?PHP exit;?>	1754275559	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; }; POST={};
<?PHP exit;?>	1754275632	admin	1	127.0.0.1	plugins	GET={}; POST={};
<?PHP exit;?>	1754275632	admin	1	127.0.0.1	plugins	GET={frames=yes; }; POST={};
<?PHP exit;?>	1754275632	admin	1	127.0.0.1	plugins	GET={}; POST={};
<?PHP exit;?>	1754275634	admin	1	127.0.0.1	cloudaddons	GET={frame=no; id=apoyl_snsvideo.plugin; }; POST={};
<?PHP exit;?>	1754276193	admin	1	127.0.0.1	plugins	GET={operation=config; do=113; }; POST={};
<?PHP exit;?>	1754276193	admin	1	127.0.0.1	plugins	GET={operation=config; do=113; }; POST={};
<?PHP exit;?>	1754276196	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; }; POST={};
<?PHP exit;?>	1754276334	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; }; POST={};
<?PHP exit;?>	1754276343	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; varsnew={aotuman_enable=1; aotuman_title=爱刷短视频; aotuman_desc=凹凸曼插件《爱刷短视频》，支持视频发布、点赞、收藏、分享、评论功能; aotuman_welcome_title=短视频社交平台; aotuman_per_page=24; openmb=1; }; editsubmit=提交; }; POST={varsnew={aotuman_enable=1; aotuman_title=爱刷短视频; aotuman_desc=凹凸曼插件《爱刷短视频》，支持视频发布、点赞、收藏、分享、评论功能; aotuman_welcome_title=短视频社交平台; aotuman_per_page=24; openmb=1; }; editsubmit=提交; };
<?PHP exit;?>	1754276368	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; }; POST={};
<?PHP exit;?>	1754362292	admin	1	127.0.0.1		GET={}; POST={};
<?PHP exit;?>	1754362292	admin	1	127.0.0.1	index	GET={}; POST={};
<?PHP exit;?>	1754362294	admin	1	127.0.0.1	plugins	GET={}; POST={};
<?PHP exit;?>	1754362297	admin	1	127.0.0.1	plugins	GET={operation=edit; pluginid=111; }; POST={};
<?PHP exit;?>	1754362335	admin	1	127.0.0.1	plugins	GET={operation=edit; type=vars; pluginid=111; anchor=vars; displayordernew={1532=1; 1533=2; 1535=3; 1534=6; 1552=8; }; newdisplayorder=7; newtitle=VIP用户组上传视频; newvariable=vipgroup; newtype=radio; editsubmit=提交; }; POST={anchor=vars; displayordernew={1532=1; 1533=2; 1535=3; 1534=6; 1552=8; }; newdisplayorder=7; newtitle=VIP用户组上传视频; newvariable=vipgroup; newtype=radio; editsubmit=提交; };
<?PHP exit;?>	1754362440	admin	1	127.0.0.1	plugins	GET={operation=edit; pluginid=111; anchor=vars; }; POST={};
<?PHP exit;?>	1754362885	admin	1	127.0.0.1	plugins	GET={operation=vars; pluginid=111; pluginvarid=1553; }; POST={};
<?PHP exit;?>	1754362928	admin	1	127.0.0.1	plugins	GET={operation=vars; pluginid=111; pluginvarid=1553; titlenew=VIP用户组上传视频; descriptionnew=那些VIP用户组可以上传视频。比如管理员，VIP用户组; typenew=radio; variablenew=vipgroup; varsubmit=提交; }; POST={titlenew=VIP用户组上传视频; descriptionnew=那些VIP用户组可以上传视频。比如管理员，VIP用户组; typenew=radio; variablenew=vipgroup; varsubmit=提交; };
<?PHP exit;?>	1754362976	admin	1	127.0.0.1	plugins	GET={operation=edit; pluginid=111; anchor=vars; }; POST={};
<?PHP exit;?>	1754363449	admin	1	127.0.0.1	plugins	GET={operation=config; do=111; }; POST={};
<?PHP exit;?>	1754363453	admin	1	127.0.0.1	plugins	GET={}; POST={};
<?PHP exit;?>	1754363456	admin	1	127.0.0.1	plugins	GET={operation=edit; pluginid=111; }; POST={};
<?PHP exit;?>	1754363459	admin	1	127.0.0.1	plugins	GET={operation=vars; pluginid=111; pluginvarid=1553; }; POST={};
<?PHP exit;?>	1754363463	admin	1	127.0.0.1	plugins	GET={operation=vars; pluginid=111; pluginvarid=1553; titlenew=VIP用户组上传视频; descriptionnew=那些VIP用户组可以上传视频。比如管理员，VIP用户组; typenew=groups; variablenew=vipgroup; varsubmit=提交; }; POST={titlenew=VIP用户组上传视频; descriptionnew=那些VIP用户组可以上传视频。比如管理员，VIP用户组; typenew=groups; variablenew=vipgroup; varsubmit=提交; };
<?PHP exit;?>	1754363490	admin	1	127.0.0.1	plugins	GET={operation=edit; pluginid=111; anchor=vars; }; POST={};
<?PHP exit;?>	1754363543	admin	1	127.0.0.1		GET={}; POST={};
<?PHP exit;?>	1754363543	admin	1	127.0.0.1	index	GET={}; POST={};
