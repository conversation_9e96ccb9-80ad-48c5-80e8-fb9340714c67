<?php if(!defined('IN_DISCUZ')) exit('Access Denied'); hookscriptoutput('following');?><?php include template('common/header'); ?><link href="source/plugin/apoyl_snsvideo/static/css/pc/following.css" rel="stylesheet" />


<div class="aotuman-following-container">
    <!-- 页面头部 -->
    <div class="aotuman-following-header">
        <div class="aotuman-header-content">
            <div class="aotuman-header-left">
                <a href="javascript:history.back()" class="aotuman-back-btn">
                    <span>←</span>
                    <span>返回</span>
                </a>
                <div class="aotuman-header-title">我的关注</div>
            </div>
            <div class="aotuman-header-stats">
                <div class="aotuman-stats-number"><?php echo $aotuman_total;?></div>
                <div class="aotuman-stats-label">我的关注</div>
            </div>
        </div>
    </div>
    
    <!-- 页面内容 -->
    <div class="aotuman-following-content">
        <?php if($aotuman_formatted_following) { ?>
        <div class="aotuman-user-grid">
            <?php if(isset($aotuman_formatted_following) && is_array($aotuman_formatted_following)) foreach($aotuman_formatted_following as $aotuman_user) { ?>            <div class="aotuman-user-card">
            <div class="aotuman-author-avatar"><?php echo $aotuman_user['avatar'];?></div>
                <div class="aotuman-user-name"><?php echo $aotuman_user['username'];?></div>
                
                <div class="aotuman-user-meta">
                    <span>📅 <?php echo $aotuman_user['follow_time_text'];?></span>
                    <?php if($aotuman_user['is_mutual']) { ?>
                    <div class="aotuman-mutual-badge">互关</div>
                    <?php } ?>
                </div>
                
                <div class="aotuman-user-actions">
                    <a href="home.php?mod=space&amp;uid=<?php echo $aotuman_user['uid'];?>" class="aotuman-action-btn">
                        <span>👤</span>
                        <span>查看主页</span>
                    </a>
                    <button class="aotuman-action-btn unfollow" onclick="aotuman_unfollow_user(<?php echo $aotuman_user['uid'];?>, this)">
                        <span>✓</span>
                        <span>已关注</span>
                    </button>
                </div>
            </div>
            <?php } ?>
        </div>
        
        <!-- 分页 -->
        <?php if($aotuman_multi) { ?>
        <div class="aotuman-pagination">
            <?php echo $aotuman_multi;?>
        </div>
        <?php } ?>
        
        <?php } else { ?>
        <div class="aotuman-empty-state">
            <div class="aotuman-empty-icon">👥</div>
            <div class="aotuman-empty-title">还没有关注任何人</div>
            <div class="aotuman-empty-desc">去发现更多有趣的用户吧</div>
            <a href="plugin.php?id=apoyl_snsvideo" class="aotuman-action-btn">
                <span>🏠</span>
                <span>返回首页</span>
            </a>
        </div>
        <?php } ?>
    </div>
</div>

<script>

function aotuman_unfollow_user(uid, button) {
    if (!confirm('确定要取消关注这个用户吗？')) {
        return;
    }
    

    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'plugin.php?id=apoyl_snsvideo&action=follow', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.Message && response.Message.messageval === 'success') {

                        if (response.Values && response.Values.followed === false) {

                            var userCard = button.closest('.aotuman-user-card');
                            userCard.style.opacity = '0';
                            userCard.style.transform = 'scale(0.8)';
                            setTimeout(function() {
                                userCard.remove();


                                var statsNumber = document.querySelector('.aotuman-stats-number');
                                var currentCount = parseInt(statsNumber.textContent);
                                statsNumber.textContent = currentCount - 1;


                                var userGrid = document.querySelector('.aotuman-user-grid');
                                if (userGrid && userGrid.children.length === 0) {
                                    location.reload();
                                }
                            }, 300);
                        }
                    } else {
                        alert('操作失败：' + (response.Message ? response.Message.messagestr : '未知错误'));
                    }
                } catch (e) {
                    alert('操作失败，请重试');
                }
            } else {
                alert('网络错误，请重试');
            }
        }
    };
    
    xhr.send('uid=' + uid + '&formhash=<?php echo FORMHASH;?>');
}
</script><?php include template('common/footer'); ?>