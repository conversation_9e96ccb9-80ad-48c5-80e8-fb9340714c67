<?php if(!defined('IN_DISCUZ')) exit('Access Denied'); hookscriptoutput('my_videos');?><?php include template('common/header'); ?><link href="source/plugin/apoyl_snsvideo/static/css/pc/myvideo.css" rel="stylesheet" />

<div class="aotuman-container">
    <!-- 页面头部 -->
    <div class="aotuman-page-header">
        <h1 class="aotuman-page-title">🎬 我的视频</h1>
        <p class="aotuman-page-subtitle">管理您发布的所有视频内容</p>
        
        <div class="aotuman-page-stats">
            <div class="aotuman-stat-item">
                <span class="aotuman-stat-number"><?php echo $aotuman_total;?></span>
                <div class="aotuman-stat-label">发布视频</div>
            </div>
            <div class="aotuman-stat-item">
                <span class="aotuman-stat-number"><?php echo $_G['username'];?></span>
                <div class="aotuman-stat-label">用户</div>
            </div>
        </div>
        
        <div class="aotuman-actions">
            <a href="plugin.php?id=apoyl_snsvideo&amp;action=upload" class="aotuman-btn aotuman-btn-upload">
                <i class="fa fa-upload"></i> 上传视频
            </a>
            <a href="plugin.php?id=apoyl_snsvideo" class="aotuman-btn">
                <i class="fa fa-home"></i> 返回首页
            </a>
        </div>
    </div>
    
    <!-- 视频列表 -->
    <?php if($aotuman_formatted_videos) { ?>
    <div class="aotuman-video-grid">
        <?php if(isset($aotuman_formatted_videos) && is_array($aotuman_formatted_videos)) foreach($aotuman_formatted_videos as $aotuman_video) { ?>        <div class="aotuman-video-card" data-videoid="<?php echo $aotuman_video['videoid'];?>">
            <div class="aotuman-video-cover-container" onclick="window.location.href='plugin.php?id=apoyl_snsvideo&action=play&videoid=<?php echo $aotuman_video['videoid'];?>'">
                <div class="aotuman-video-cover">
                    <?php if($aotuman_video['cover_url']) { ?>
                    <img src="<?php echo $aotuman_video['cover_url'];?>" alt="<?php echo $aotuman_video['title'];?>" class="aotuman-cover-image">
                    <?php } else { ?>
                    <div class="aotuman-default-cover">
                        <div class="aotuman-default-icon">🎬</div>
                        <div class="aotuman-default-text">✨ 精彩视频 ✨</div>
                        <div class="aotuman-default-subtitle">点击观看</div>
                    </div>
                    <?php } ?>
                    <div class="aotuman-video-overlay">
                        <div class="aotuman-play-btn">▶️</div>
                    </div>

                </div>
            </div>
            <div class="aotuman-video-info">
                <h3><?php echo $aotuman_video['title'];?></h3>
                <div class="aotuman-video-meta">
                    <span>📅 <?php echo $aotuman_video['addtime_text'];?></span>
                </div>
                <div class="aotuman-video-stats">
                    <span><span class="aotuman-emoji">👀</span> <?php echo $aotuman_video['views'];?></span>
                    <span><span class="aotuman-emoji">💖</span> <?php echo $aotuman_video['likes'];?></span>
                    <span><span class="aotuman-emoji">⭐</span> <?php echo $aotuman_video['favorites'];?></span>
                    <span><span class="aotuman-emoji">🚀</span> <?php echo $aotuman_video['shares'];?></span>
                    <span><span class="aotuman-emoji">💬</span> <?php echo $aotuman_video['comments'];?></span>
                </div>
            </div>
        </div>
        <?php } ?>
    </div>
    <?php } else { ?>
    <div class="aotuman-no-data">
        <i class="fa fa-video-camera"></i>
        <h3>您还没有发布任何视频</h3>
        <p>快来发布您的第一个视频吧！分享精彩内容，与大家互动。</p>
        <a href="plugin.php?id=apoyl_snsvideo&amp;action=upload" class="aotuman-btn aotuman-btn-upload">
            <i class="fa fa-upload"></i> 去上传视频
        </a>
    </div>
    <?php } ?>
    
    <!-- 分页 -->
    <?php if($aotuman_multi) { ?>
    <div class="aotuman-pagination">
        <?php echo $aotuman_multi;?>
    </div>
    <?php } ?>
</div><?php include template('common/footer'); ?>