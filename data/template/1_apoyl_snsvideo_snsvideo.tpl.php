<?php if(!defined('IN_DISCUZ')) exit('Access Denied'); hookscriptoutput('snsvideo');?><?php include template('common/header'); ?><link href="source/plugin/apoyl_snsvideo/static/css/pc/snsvideo.css" rel="stylesheet" />

<div class="aotuman-video-container">
    <!-- 页面头部 -->
    <div class="aotuman-video-header">
        <h1><?php echo $navtitle;?></h1>
        <p><?php echo $metadescription;?></p>
    </div>
    
    <!-- 操作按钮 -->
    <div class="aotuman-video-actions">
        <a href="plugin.php?id=apoyl_snsvideo&amp;action=upload" class="aotuman-btn aotuman-btn-upload">
            <i class="fa fa-upload"></i> 发布视频
        </a>
        <?php if($_G['uid']) { ?>
        <a href="plugin.php?id=apoyl_snsvideo&amp;action=my" class="aotuman-btn">
            <i class="fa fa-user"></i> 我的视频
        </a>
        <a href="plugin.php?id=apoyl_snsvideo&amp;action=liked" class="aotuman-btn">
            <i class="fa fa-heart"></i> 我的点赞
        </a>
        <a href="plugin.php?id=apoyl_snsvideo&amp;action=favorites" class="aotuman-btn">
            <i class="fa fa-star"></i> 我的收藏
        </a>
    <a href="plugin.php?id=apoyl_snsvideo&amp;action=following" class="aotuman-btn">
    <i class="fa fa-star"></i> 我的关注
    </a>
    <a href="plugin.php?id=apoyl_snsvideo&amp;action=followers" class="aotuman-btn">
    <i class="fa fa-star"></i> 我的粉丝
    </a>
    <a href="plugin.php?id=apoyl_snsvideo&amp;action=mutual" class="aotuman-btn">
    <i class="fa fa-star"></i> 互关好友
    </a>
        <?php } ?>
    </div>
    
    <!-- 视频列表 -->
    <?php if($aotuman_videos) { ?>
    <div class="aotuman-video-grid">
        <?php if(isset($aotuman_videos) && is_array($aotuman_videos)) foreach($aotuman_videos as $aotuman_video) { ?>        <div class="aotuman-video-card" data-videoid="<?php echo $aotuman_video['aotuman_videoid'];?>">
            <!-- 视频封面区域 -->
            <div class="aotuman-video-cover-container" onclick="aotuman_goto_play(<?php echo $aotuman_video['aotuman_videoid'];?>)">
                <!-- 封面图片 -->
                <div class="aotuman-video-cover">
                    <?php if($aotuman_video['aotuman_cover_url']) { ?>
                    <img src="<?php echo $aotuman_video['aotuman_cover_url'];?>" alt="<?php echo $aotuman_video['aotuman_title'];?>" class="aotuman-cover-image" />
                    <?php } else { ?>
                    <!-- 默认封面图标 -->
                    <div class="aotuman-default-cover">
                        <div class="aotuman-default-icon">🎬</div>
                        <div class="aotuman-default-text">✨ 精彩视频 ✨</div>
                        <div class="aotuman-default-subtitle">点击观看</div>
                    </div>
                    <?php } ?>

                    <!-- 播放按钮覆盖层 -->
                    <div class="aotuman-play-overlay">
                        <div class="aotuman-play-btn">
                            <span class="aotuman-play-icon">▶️</span>
                        </div>
                    </div>
                </div>

                <!-- 悬停时显示的视频预览 -->
                <?php if($aotuman_video['aotuman_video_url']) { ?>
                <div class="aotuman-video-preview">
                    <video class="aotuman-preview-player"
                           preload="metadata"
                           muted
                           loop>
                        <source src="<?php echo $aotuman_video['aotuman_video_url'];?>" type="video/mp4">
                        <source src="<?php echo $aotuman_video['aotuman_video_url'];?>" type="video/webm">
                        <source src="<?php echo $aotuman_video['aotuman_video_url'];?>" type="video/ogg">
                    </video>
                </div>
                <?php } ?>


            </div>
            
            <!-- 视频信息 -->
            <div class="aotuman-video-info">
                <!-- 作者信息区域 -->
                <div class="aotuman-author-info">
                    <div class="aotuman-author-avatar-small">
                        <?php echo avatar($aotuman_video['aotuman_uid'], 'small');?>                    </div>
                    <div class="aotuman-author-details">
                        <div class="aotuman-author-name"><?php echo $aotuman_video['aotuman_username'];?></div>
                        <?php if($_G['uid'] && $aotuman_video['aotuman_uid'] != $_G['uid']) { ?>
                        <button class="aotuman-follow-btn-small <?php if($aotuman_video['aotuman_followed']) { ?>followed<?php } ?>"
                                onclick="aotuman_toggle_follow(<?php echo $aotuman_video['aotuman_uid'];?>, this)">
                            <span class="aotuman-follow-text-add" <?php if($aotuman_video['aotuman_followed']) { ?>style="display: none;"<?php } ?>>+ 关注</span>
                            <span class="aotuman-follow-text-check" <?php if(!$aotuman_video['aotuman_followed']) { ?>style="display: none;"<?php } ?>>✓ 已关注</span>
                        </button>
                        <?php } ?>
                    </div>
                </div>

                <div class="aotuman-video-title"><?php echo $aotuman_video['aotuman_title'];?></div>
                <?php if($aotuman_video['aotuman_description']) { ?>
                <div class="aotuman-video-desc"><?php echo $aotuman_video['aotuman_description'];?></div>
                <?php } ?>
                
                <div class="aotuman-video-meta">
                    <span class="aotuman-video-author"><?php echo $aotuman_video['aotuman_username'];?></span>
                    <span><?php echo $aotuman_video['aotuman_addtime_text'];?></span>
                </div>
                
                <div class="aotuman-video-stats">
                    <span><span class="aotuman-emoji">👀</span> <?php echo $aotuman_video['aotuman_views'];?></span>
                    <span><span class="aotuman-emoji">💖</span> <?php echo $aotuman_video['aotuman_likes'];?></span>
                    <span><span class="aotuman-emoji">⭐</span> <?php echo $aotuman_video['aotuman_favorites'];?></span>
                    <span><span class="aotuman-emoji">🚀</span> <?php echo $aotuman_video['aotuman_shares'];?></span>
                    <span><span class="aotuman-emoji">💬</span> <?php echo $aotuman_video['aotuman_comments'];?></span>
                </div>
                

                
                <!-- 标签 -->
                <?php if($aotuman_video['aotuman_tags_array']) { ?>
                <div class="aotuman-video-tags">
                    <?php if(isset($aotuman_video['aotuman_tags_array']) && is_array($aotuman_video['aotuman_tags_array'])) foreach($aotuman_video['aotuman_tags_array'] as $aotuman_tag) { ?>                    <span class="aotuman-tag"><?php echo $aotuman_tag;?></span>
                    <?php } ?>
                </div>
                <?php } ?>
            </div>
        </div>
        <?php } ?>
    </div>
    
    <!-- 分页 -->
    <?php if($aotuman_multipage) { ?>
    <div class="aotuman-pagination">
        <?php echo $aotuman_multipage;?>
    </div>
    <?php } ?>
    
    <?php } else { ?>
    <div class="aotuman-no-data">
        <i class="fa fa-video-camera"></i>
        <h3>暂无视频</h3>
        <p>登录后才能发布视频</p>
        <a href="plugin.php?id=apoyl_snsvideo&amp;action=upload" class="aotuman-btn aotuman-btn-upload">
            发布视频
        </a>
    </div>
    <?php } ?>
</div>
<script src="static/js/jquery/jquery.min.js" type="text/javascript" type="text/javascript" ></script>
<script>
var plugin_apoyl_snsvideo_jquery = jQuery.noConflict(true);


function aotuman_debug_response(action, data) {
    if(console) {

        if(data.Message) {

        }
        if(data.Values) {

        }

    }
}


function aotuman_goto_play(videoid) {

    plugin_apoyl_snsvideo_jquery.get('plugin.php?id=apoyl_snsvideo&action=view&videoid=' + videoid + '&formhash=<?php echo FORMHASH;?>&inajax=1');


    window.location.href = 'plugin.php?id=apoyl_snsvideo&action=play&videoid=' + videoid+'&formhash=<?php echo FORMHASH;?>';
}


function aotuman_play_video(videoid) {
    aotuman_goto_play(videoid);
}


function aotuman_record_view(videoid) {

    plugin_apoyl_snsvideo_jquery.get('plugin.php?id=apoyl_snsvideo&action=view&videoid=' + videoid + '&formhash=<?php echo FORMHASH;?>&inajax=1');
}


plugin_apoyl_snsvideo_jquery(document).ready(function() {

    plugin_apoyl_snsvideo_jquery('.aotuman-action-btn.liked').each(function() {
        plugin_apoyl_snsvideo_jquery(this).find('.aotuman-icon-like').hide();
        plugin_apoyl_snsvideo_jquery(this).find('.aotuman-icon-liked').show();
    });

    plugin_apoyl_snsvideo_jquery('.aotuman-action-btn.favorited').each(function() {
        plugin_apoyl_snsvideo_jquery(this).find('.aotuman-icon-favorite').hide();
        plugin_apoyl_snsvideo_jquery(this).find('.aotuman-icon-favorited').show();
    });


    plugin_apoyl_snsvideo_jquery('.aotuman-video-cover-container').each(function() {
        var aotuman_container = plugin_apoyl_snsvideo_jquery(this);
        var aotuman_preview = aotuman_container.find('.aotuman-preview-player')[0];
        var aotuman_hover_timer = null;

        if (aotuman_preview) {

            aotuman_container.on('mouseenter', function() {
                aotuman_hover_timer = setTimeout(function() {
                    aotuman_preview.currentTime = 0;
                    aotuman_preview.play().catch(function(error) {

                    });
                }, 500);

            });


            aotuman_container.on('mouseleave', function() {
                if (aotuman_hover_timer) {
                    clearTimeout(aotuman_hover_timer);
                    aotuman_hover_timer = null;
                }
                aotuman_preview.pause();
                aotuman_preview.currentTime = 0;
            });
        }
    });


    plugin_apoyl_snsvideo_jquery('.aotuman-cover-image').on('error', function() {
        var aotuman_cover = plugin_apoyl_snsvideo_jquery(this).closest('.aotuman-video-cover');
        plugin_apoyl_snsvideo_jquery(this).hide();
        aotuman_cover.append(
            '<div class="aotuman-default-cover">' +
            '<div class="aotuman-default-icon">📷</div>' +
            '<div class="aotuman-default-text">🚫 封面加载失败</div>' +
            '<div class="aotuman-default-subtitle">点击仍可观看</div>' +
            '</div>'
        );
    });
});


function aotuman_toggle_like(videoid, btn) {
    plugin_apoyl_snsvideo_jquery.get('plugin.php?id=apoyl_snsvideo&action=like&videoid=' + videoid + '&formhash=<?php echo FORMHASH;?>&inajax=1', function(data) {




        var aotuman_success = false;
        var aotuman_likes = 0;
        var aotuman_liked = false;

        if(data.Message && data.Message.messageval == 'success') {

            aotuman_success = true;
            aotuman_likes = data.Values.likes;
            aotuman_liked = data.Values.liked;
        } else if(data.messageval == 'success' || data.status == 'success') {

            aotuman_success = true;
            aotuman_likes = data.likes || data.Values.likes;
            aotuman_liked = data.liked || data.Values.liked;
        } else if(typeof data === 'object' && data.likes !== undefined) {

            aotuman_success = true;
            aotuman_likes = data.likes;
            aotuman_liked = data.liked;
        }

        if(aotuman_success) {
            var aotuman_btn = plugin_apoyl_snsvideo_jquery(btn);
            var aotuman_count = aotuman_btn.find('.aotuman-like-count');
            var aotuman_icon_like = aotuman_btn.find('.aotuman-icon-like');
            var aotuman_icon_liked = aotuman_btn.find('.aotuman-icon-liked');

            aotuman_count.text(aotuman_likes);

            if(aotuman_liked) {
                aotuman_btn.addClass('liked');
                aotuman_icon_like.hide();
                aotuman_icon_liked.show();
            } else {
                aotuman_btn.removeClass('liked');
                aotuman_icon_like.show();
                aotuman_icon_liked.hide();
            }
        } else {

            var aotuman_error_msg = '操作失败，请重试';
            if(data.Message && data.Message.messagestr) {
                aotuman_error_msg = data.Message.messagestr;
            } else if(data.message) {
                aotuman_error_msg = data.message;
            }
            alert(aotuman_error_msg);
        }
    }, 'json').fail(function(xhr, status, error) {

        alert('网络错误');
    });
}


function aotuman_toggle_favorite(videoid, btn) {
    plugin_apoyl_snsvideo_jquery.get('plugin.php?id=apoyl_snsvideo&action=favorite&videoid=' + videoid + '&formhash=<?php echo FORMHASH;?>&inajax=1', function(data) {




        var aotuman_success = false;
        var aotuman_favorites = 0;
        var aotuman_favorited = false;

        if(data.Message && data.Message.messageval == 'success') {

            aotuman_success = true;
            aotuman_favorites = data.Values.favorites;
            aotuman_favorited = data.Values.favorited;
        } else if(data.messageval == 'success' || data.status == 'success') {

            aotuman_success = true;
            aotuman_favorites = data.favorites || data.Values.favorites;
            aotuman_favorited = data.favorited || data.Values.favorited;
        } else if(typeof data === 'object' && data.favorites !== undefined) {

            aotuman_success = true;
            aotuman_favorites = data.favorites;
            aotuman_favorited = data.favorited;
        }

        if(aotuman_success) {
            var aotuman_btn = plugin_apoyl_snsvideo_jquery(btn);
            var aotuman_count = aotuman_btn.find('.aotuman-favorite-count');
            var aotuman_icon_favorite = aotuman_btn.find('.aotuman-icon-favorite');
            var aotuman_icon_favorited = aotuman_btn.find('.aotuman-icon-favorited');

            aotuman_count.text(aotuman_favorites);

            if(aotuman_favorited) {
                aotuman_btn.addClass('favorited');
                aotuman_icon_favorite.hide();
                aotuman_icon_favorited.show();
            } else {
                aotuman_btn.removeClass('favorited');
                aotuman_icon_favorite.show();
                aotuman_icon_favorited.hide();
            }
        } else {

            var aotuman_error_msg = '操作失败，请重试';
            if(data.Message && data.Message.messagestr) {
                aotuman_error_msg = data.Message.messagestr;
            } else if(data.message) {
                aotuman_error_msg = data.message;
            }
            alert(aotuman_error_msg);
        }
    }, 'json').fail(function(xhr, status, error) {

        alert('网络错误，请重试');
    });
}


function aotuman_share_video(videoid) {
    var aotuman_url = window.location.origin + '/plugin.php?id=apoyl_snsvideo&action=play&videoid=' + videoid+'&formhash=<?php echo FORMHASH;?>';


    plugin_apoyl_snsvideo_jquery.get('plugin.php?id=apoyl_snsvideo&action=share&videoid=' + videoid + '&formhash=<?php echo FORMHASH;?>&inajax=1', function(data) {

        var aotuman_success = false;
        var aotuman_shares = 0;

        if(data.Message && data.Message.messageval == 'success') {
            aotuman_success = true;
            aotuman_shares = data.Values.shares;
        } else if(data.messageval == 'success' || data.status == 'success') {
            aotuman_success = true;
            aotuman_shares = data.shares || data.Values.shares;
        } else if(typeof data === 'object' && data.shares !== undefined) {
            aotuman_success = true;
            aotuman_shares = data.shares;
        }

        if(aotuman_success) {
            var aotuman_card = plugin_apoyl_snsvideo_jquery('[data-videoid="' + videoid + '"]');
            var aotuman_count = aotuman_card.find('.aotuman-share-count');
            aotuman_count.text(aotuman_shares);
        }
    }, 'json').fail(function() {

    });


    if(navigator.clipboard) {
        navigator.clipboard.writeText(aotuman_url).then(function() {
            alert('链接已复制到剪贴板 📋');
        }).catch(function() {
            prompt('复制链接', aotuman_url);
        });
    } else {
        prompt('复制链接', aotuman_url);
    }
}


function aotuman_show_comments(videoid) {

    if(!confirm('是否要添加评论？')) {
        return;
    }

    var aotuman_comment = prompt('请输入评论内容：');
    if(!aotuman_comment || aotuman_comment.trim() === '') {
        return;
    }


    plugin_apoyl_snsvideo_jquery.post('plugin.php?id=apoyl_snsvideo&action=comment&videoid=' + videoid + '&formhash=<?php echo FORMHASH;?>&inajax=1&ajaxdata=json', {
        content: aotuman_comment.trim(),
        parent_id: 0,
        reply_uid: 0,
        reply_username: ''
    }, function(data) {

        var aotuman_success = false;
        var aotuman_comments = 0;

        if(data.Message && data.Message.messageval == 'success') {
            aotuman_success = true;
            aotuman_comments = data.Values.comments;
        } else if(data.messageval == 'success' || data.status == 'success') {
            aotuman_success = true;
            aotuman_comments = data.comments || data.Values.comments;
        } else if(typeof data === 'object' && data.comments !== undefined) {
            aotuman_success = true;
            aotuman_comments = data.comments;
        }

        if(aotuman_success) {
            var aotuman_card = plugin_apoyl_snsvideo_jquery('[data-videoid="' + videoid + '"]');
            var aotuman_count = aotuman_card.find('.aotuman-comment-count');
            aotuman_count.text(aotuman_comments);
            alert('评论发布成功！💬');
        } else {
            var aotuman_error_msg = '评论发布失败，请重试';
            if(data.Message && data.Message.messagestr) {
                aotuman_error_msg = data.Message.messagestr;
            } else if(data.message) {
                aotuman_error_msg = data.message;
            }
            alert(aotuman_error_msg);
        }
    }, 'json').fail(function() {
        alert('评论发布失败，请重试');
    });
}


function aotuman_toggle_follow(uid, btn) {
    var aotuman_btn = plugin_apoyl_snsvideo_jquery(btn);


    if(aotuman_btn.hasClass('processing')) {
        return;
    }

    aotuman_btn.addClass('processing');

    plugin_apoyl_snsvideo_jquery.get('plugin.php?id=apoyl_snsvideo&action=follow&uid=' + uid + '&formhash=<?php echo FORMHASH;?>&inajax=1', function(data) {
        if(data.Message && data.Message.messageval == 'success') {
            var aotuman_text_add = aotuman_btn.find('.aotuman-follow-text-add');
            var aotuman_text_check = aotuman_btn.find('.aotuman-follow-text-check');

            if(data.Values.followed) {

                aotuman_btn.addClass('followed');
                aotuman_text_add.hide();
                aotuman_text_check.show();


                aotuman_btn.addClass('success-animation');
                setTimeout(function() {
                    aotuman_btn.removeClass('success-animation');
                }, 300);


                alert('关注成功！');
            } else {

                aotuman_btn.removeClass('followed');
                aotuman_text_add.show();
                aotuman_text_check.hide();


                alert('已取消关注');
            }
        } else {
            alert(data.Message ? data.Message.messagestr : '操作失败，请重试');
        }


        aotuman_btn.removeClass('processing');
    }, 'json').fail(function() {
        alert('网络错误');
        aotuman_btn.removeClass('processing');
    });
}


plugin_apoyl_snsvideo_jquery(document).ready(function() {

    plugin_apoyl_snsvideo_jquery('.aotuman-follow-btn-small.followed').each(function() {
        var aotuman_btn = plugin_apoyl_snsvideo_jquery(this);
        aotuman_btn.find('.aotuman-follow-text-add').hide();
        aotuman_btn.find('.aotuman-follow-text-check').show();
    });
});
</script><?php include template('common/footer'); ?>