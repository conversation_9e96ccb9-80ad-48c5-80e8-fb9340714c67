<?php if(!defined('IN_DISCUZ')) exit('Access Denied'); ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0, minimum-scale=1.0">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#000000">
<title><?php echo $navtitle;?></title>
<meta name="description" content="<?php echo $metadescription;?>" />


    <!-- 移动端优化 -->
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="browsermode" content="application">
    <meta name="x5-page-mode" content="app">


<link rel="stylesheet" href="source/plugin/apoyl_snsvideo/static/css/mobile/all.min.css">

<link href="source/plugin/apoyl_snsvideo/static/css/mobile/snsvideo.css" rel="stylesheet" >

    <script>

        const isEdge = navigator.userAgent.includes("Edg");
    if (isEdge) {
        document.documentElement.classList.add("is-edge");
    }</script>
</head>
<body>
    <!-- 加载屏幕 -->
    <div class="aotuman-loading-screen" id="aotuman-loading-screen">
        <div class="aotuman-loading-logo">
            <i class="fa fa-video"></i>
        </div>
        <div class="aotuman-loading-text"><?php echo $aotuman_welcome_title;?></div>
        <div class="aotuman-loading-dots">
            <div class="aotuman-loading-dot"></div>
            <div class="aotuman-loading-dot"></div>
            <div class="aotuman-loading-dot"></div>
        </div>
    </div>

<div class="aotuman-mobile-container">
    <!-- 顶部栏 -->
    <div class="aotuman-top-bar">
        <div class="aotuman-top-title"><?php echo $aotuman_page_title;?></div>
        <div class="aotuman-top-actions">
            <button class="aotuman-top-btn" onclick="aotuman_search_videos()">
                <i class="fa fa-search"></i>
            </button>
        </div>
    </div>
    
    <!-- 视频滑动区域 -->
    <div class="aotuman-video-swiper" id="aotuman-video-swiper">
        <?php if($aotuman_videos) { ?>
        <?php if(isset($aotuman_videos) && is_array($aotuman_videos)) foreach($aotuman_videos as $aotuman_key => $aotuman_video) { ?>        <div class="aotuman-video-slide" data-videoid="<?php echo $aotuman_video['aotuman_videoid'];?>" data-index="<?php echo $aotuman_key;?>">
            <!-- 视频播放器 -->
            <?php if($aotuman_video['aotuman_video_url']) { ?>
            <video class="aotuman-video-player" 
                   src="<?php echo $aotuman_video['aotuman_video_url'];?>" 
                   poster="<?php echo $aotuman_video['aotuman_cover_url'];?>"
                   loop  playsinline
                   controlsList="nodownload"
                   onclick="aotuman_toggle_play(this)"
                   id="video-<?php echo $aotuman_video['aotuman_videoid'];?>">
            </video>
            <?php } else { ?>
            <div class="aotuman-video-cover" 
                 style="background-image: url('<?php echo $aotuman_video['aotuman_cover_url'];?>')"
                 onclick="aotuman_play_video(<?php echo $aotuman_video['aotuman_videoid'];?>)">
                <div class="aotuman-play-btn">
                    <i class="fa fa-play"></i>
                </div>
            </div>
            <?php } ?>
            
            <!-- 视频信息 -->
            <div class="aotuman-video-info">
                <div class="aotuman-video-title"><?php echo $aotuman_video['aotuman_title'];?></div>
                <?php if($aotuman_video['aotuman_description']) { ?>
                <div class="aotuman-video-desc"><?php echo $aotuman_video['aotuman_description'];?></div>
                <?php } ?>
                
                <div class="aotuman-video-meta">
                    <span class="aotuman-video-author">@<?php echo $aotuman_video['aotuman_username'];?></span>
                    <span><?php echo $aotuman_video['aotuman_addtime_text'];?></span>
                </div>
                
                <?php if($aotuman_video['aotuman_tags_array']) { ?>
                <div class="aotuman-video-tags">
                    <?php if(isset($aotuman_video['aotuman_tags_array']) && is_array($aotuman_video['aotuman_tags_array'])) foreach($aotuman_video['aotuman_tags_array'] as $aotuman_tag) { ?>                    <span class="aotuman-tag">#<?php echo $aotuman_tag;?></span>
                    <?php } ?>
                </div>
                <?php } ?>
            </div>
            
            <!-- 操作按钮 -->
            <div class="aotuman-video-actions">
                <!-- 作者头像 -->
                <div class="aotuman-author-section">
                    <div class="aotuman-author-avatar" onclick="aotuman_open_user_profile(<?php echo $aotuman_video['aotuman_uid'];?>, '<?php echo $aotuman_video['aotuman_username'];?>')">
                        <?php echo avatar($aotuman_video['aotuman_uid'], 'small');?>                    </div>
                    <!-- 关注按钮 -->
                    <?php if($_G['uid'] && $aotuman_video['aotuman_uid'] != $_G['uid']) { ?>
                    <button class="aotuman-follow-btn <?php if($aotuman_video['aotuman_followed']) { ?>followed<?php } ?>"
                            onclick="aotuman_toggle_follow(<?php echo $aotuman_video['aotuman_uid'];?>, this)">
                        <div class="aotuman-follow-plus <?php if($aotuman_video['aotuman_followed']) { ?>followed<?php } ?>">
                            <span class="aotuman-follow-icon-add" <?php if($aotuman_video['aotuman_followed']) { ?>style="display: none;"<?php } ?>>+</span>
                            <span class="aotuman-follow-icon-check" <?php if(!$aotuman_video['aotuman_followed']) { ?>style="display: none;"<?php } ?>>✓</span>
                        </div>
                    </button>
                    <?php } ?>
                </div>

                <button class="aotuman-action-btn aotuman-like-btn <?php if($aotuman_video['aotuman_liked']) { ?>liked<?php } ?>"
                        onclick="aotuman_toggle_like(<?php echo $aotuman_video['aotuman_videoid'];?>, this)">
                    <div class="aotuman-action-icon">
                        <span class="aotuman-emoji aotuman-icon-like">🤍</span>
                        <span class="aotuman-emoji aotuman-icon-liked" style="display: none;">❤️</span>
                    </div>
                    <div class="aotuman-action-count aotuman-like-count"><?php echo $aotuman_video['aotuman_likes'];?></div>
                </button>



                <button class="aotuman-action-btn aotuman-favorite-btn <?php if($aotuman_video['aotuman_favorited']) { ?>favorited<?php } ?>"
                        onclick="aotuman_toggle_favorite(<?php echo $aotuman_video['aotuman_videoid'];?>, this)">
                    <div class="aotuman-action-icon">
                        <span class="aotuman-emoji aotuman-icon-favorite">☆</span>
                        <span class="aotuman-emoji aotuman-icon-favorited" style="display: none;">⭐</span>
                    </div>
                    <div class="aotuman-action-count aotuman-favorite-count"><?php echo $aotuman_video['aotuman_favorites'];?></div>
                </button>

                <button class="aotuman-action-btn aotuman-share-btn"
                        onclick="aotuman_share_video(<?php echo $aotuman_video['aotuman_videoid'];?>)">
                    <div class="aotuman-action-icon">
                        <span class="aotuman-emoji">↗️</span>

                    </div>
                    <div class="aotuman-action-count aotuman-share-count"><?php echo $aotuman_video['aotuman_shares'];?></div>
                </button>

                <button class="aotuman-action-btn aotuman-comment-btn" onclick="aotuman_show_comments(<?php echo $aotuman_video['aotuman_videoid'];?>)">
                    <div class="aotuman-action-icon">
                        <span class="aotuman-emoji">💭</span>
                    </div>
                    <div class="aotuman-action-count aotuman-comment-count"><?php echo $aotuman_video['aotuman_comments'];?></div>
                </button>





            </div>
        </div>
        <?php } ?>
        

        
        <?php } else { ?>
        <div class="aotuman-no-data">
            <i class="fa fa-video-camera"></i>
            <h3>暂无视频</h3>
            <p>登录后才能发布视频</p>
        </div>
        <?php } ?>
    </div>
    

    <div class="aotuman-bottom-navigation">
        <!-- 首页 -->
        <div class="aotuman-nav-item active" onclick="aotuman_nav_to('home')">
            <div class="aotuman-nav-icon">🏠</div>
            <div class="aotuman-nav-label">首页</div>
        </div>

        <!-- 论坛 -->
        <div class="aotuman-nav-item"  onclick="aotuman_nav_to('forum')">
            <div class="aotuman-nav-icon"><img src="source/plugin/apoyl_snsvideo/static/img/forum.svg"></div>
            <div class="aotuman-nav-label">论坛</div>
        </div>

        <!-- 发视频按钮 -->
        <div class="aotuman-nav-item aotuman-upload-item">
            <a href="plugin.php?id=apoyl_snsvideo&amp;action=upload" class="aotuman-upload-btn">
                <i class="fa fa-plus"></i>
            </a>
        </div>

        <!-- 消息 -->
        <div class="aotuman-nav-item" onclick="aotuman_nav_to('messages')">
            <div class="aotuman-nav-icon">💬</div>
            <div class="aotuman-nav-label">消息</div>
            <div class="aotuman-message-badge" id="messageBadge" style="display: none;">
                <span id="messageCount">0</span>
            </div>
        </div>

        <!-- 我的 -->
        <div class="aotuman-nav-item" onclick="aotuman_nav_to('profile')">
            <div class="aotuman-nav-icon">👤</div>
            <div class="aotuman-nav-label">我的</div>
        </div>
    </div>
</div>

<!-- 视频风格评论弹层 -->
<div class="aotuman-comments-overlay" id="aotuman-comments-overlay">
    <div class="aotuman-comments-modal">
        <!-- 评论头部 -->
        <div class="aotuman-comments-modal-header">
            <h3>💬 评论 (<span id="aotuman-modal-comments-total">0</span>)</h3>
            <button class="aotuman-comments-close-btn" onclick="aotuman_close_comments_modal()">✕</button>
        </div>

        <!-- 评论列表 -->
        <div class="aotuman-comments-modal-list" id="aotuman-modal-comments-list">
            <div class="aotuman-comments-loading">
                <div class="aotuman-loading-spinner">⏳</div>
                <p>加载评论中...</p>
            </div>
        </div>

        <!-- 评论输入区 -->
        <div class="aotuman-comments-modal-input">
            <div class="aotuman-modal-input-wrapper">
                <div class="aotuman-modal-comment-avatar">
                    <?php if($_G['uid']) { ?>
                    <?php echo avatar($_G['uid'], 'small');?>                    <?php } else { ?>
                    <div class="aotuman-modal-comment-avatar-placeholder">👤</div>
                    <?php } ?>
                </div>
                <input type="text"
                       id="aotuman-modal-comment-input"
                       placeholder="写下你的想法..."
                       maxlength="500">
                <button id="aotuman-modal-submit-comment"
                        class="aotuman-modal-submit-btn"
                        onclick="aotuman_submit_modal_comment()">
                    发布
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 搜索页面模态框 -->
<div id="aotuman-search-modal" class="aotuman-search-modal" style="display: none;">
    <div class="aotuman-search-container">
        <!-- 搜索头部 -->
        <div class="aotuman-search-header">
            <button class="aotuman-search-back-btn" onclick="aotuman_close_search()">
                <span class="aotuman-back-icon">←</span>
            </button>
            <div class="aotuman-search-input-wrapper">
                <input type="text"
                       id="aotuman-search-input"
                       placeholder="搜索视频、用户..."
                       autocomplete="off">
                <button class="aotuman-search-clear-btn" onclick="aotuman_clear_search()" style="display: none;">
                    <span class="aotuman-clear-icon">×</span>
                </button>
            </div>
            <button class="aotuman-search-submit-btn" onclick="aotuman_submit_search()">
                搜索
            </button>
        </div>

        <!-- 搜索内容区域 -->
        <div class="aotuman-search-content">
            <!-- 搜索建议 -->
            <div id="aotuman-search-suggestions" class="aotuman-search-suggestions">
                <div class="aotuman-suggestions-title">热门搜索</div>
                <div class="aotuman-suggestions-list">
                    <span class="aotuman-suggestion-tag" onclick="aotuman_search_tag('搞笑')">搞笑</span>
                    <span class="aotuman-suggestion-tag" onclick="aotuman_search_tag('美食')">美食</span>
                    <span class="aotuman-suggestion-tag" onclick="aotuman_search_tag('音乐')">音乐</span>
                    <span class="aotuman-suggestion-tag" onclick="aotuman_search_tag('舞蹈')">舞蹈</span>
                    <span class="aotuman-suggestion-tag" onclick="aotuman_search_tag('游戏')">游戏</span>
                    <span class="aotuman-suggestion-tag" onclick="aotuman_search_tag('旅行')">旅行</span>
                </div>

                <div class="aotuman-suggestions-title" style="margin-top: 20px;">搜索历史</div>
                <div id="aotuman-search-history" class="aotuman-search-history">
                    <!-- 搜索历史将通过JavaScript动态加载 -->
                </div>
            </div>

            <!-- 搜索结果 -->
            <div id="aotuman-search-results" class="aotuman-search-results" style="display: none;">
                <div class="aotuman-search-tabs">
                    <button class="aotuman-search-tab active" data-tab="videos" onclick="aotuman_switch_search_tab('videos')">
                        视频
                    </button>
                    <button class="aotuman-search-tab" data-tab="users" onclick="aotuman_switch_search_tab('users')">
                        用户
                    </button>
                </div>

                <!-- 视频搜索结果 -->
                <div id="aotuman-search-videos-results" class="aotuman-search-tab-content">
                    <div class="aotuman-search-loading">
                        <div class="aotuman-loading-spinner"></div>
                        <p>搜索中...</p>
                    </div>
                </div>

                <!-- 用户搜索结果 -->
                <div id="aotuman-search-users-results" class="aotuman-search-tab-content" style="display: none;">
                    <div class="aotuman-search-loading">
                        <div class="aotuman-loading-spinner"></div>
                        <p>搜索中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="static/js/mobile/jquery.min.js" type="text/javascript" type="text/javascript"></script>
    <script src="source/plugin/apoyl_snsvideo/static/js/mobile/snsvideo.min.js" type="text/javascript" type="text/javascript"></script>
    <script>
        var plugin_apoyl_snsvideo_jquery = jQuery.noConflict(true);



        // DZX全局变量
        var discuz_uid = '<?php echo $_G['uid'];?>';
        var discuz_username = '<?php echo $_G['username'];?>';
        var discuz_formhash = '<?php echo FORMHASH;?>';
        var siteurl= '<?php echo $siteurl;?>';


        var aotuman_site_title='<?php echo $navtitle;?>';
        // 兼容性变量
        window.discuz_uid = discuz_uid;
        window.discuz_username = discuz_username;
        window.FORMHASH = discuz_formhash;



        // 语言包变量
        var aotuman_lang = {


            // 视频相关语言包
            'loading': '短视频社交平台',
            'no_videos_found': '!mall_no_search_results!',
            'no_title': '无标题',
            'default_cover_text': '视频',
            'wonderful_video_share': '精彩视频分享',
            'check_this_video': '来看看这个有趣的视频！',
            'copy_link_prompt': '复制链接',
            'operation_failed_retry': '操作失败，请重试',
            'network_error_retry': '网络错误，请重试',
            'follow_success_msg': '关注成功！ 🎉',
            'unfollow_msg': '已取消关注',
            'comment_publish_success_msg': '评论发布成功！💬',
            'no_comments_placeholder': '暂无评论，快来抢沙发吧！',
            'comment_load_failed': '评论加载失败',
            'anonymous_user': '匿名用户',
            'profile_not_logged_in': '未登录',
            'profile_no_works': '还没有发布作品',
            'profile_load_failed': '加载失败',
            'profile_history_developing': '观看历史功能开发中...',
            'profile_no_history': '暂无观看历史',
            'profile_history_desc': '观看的视频会在这里显示',
            'stats_user_desc_default': '这个人很懒，什么都没留下',
            'stats_no_mutual_friends': '暂无互关好友',
            'stats_no_users': '暂无用户',
            'stats_load_failed': '加载失败',
            'network_connection_failed': '网络连接失败，请检查网络后重试',
            'favorite_operation_failed': '操作失败，请重试',
            'upload_now': '立即上传',
            'retry': '重试',
            'video_id_empty': '视频ID为空',
            'invalid_video_id': '无效的视频ID',
            'cannot_get_video_id': '无法获取视频ID',
            'play_failed': '播放失败',
            'followed': '已关注',
            'follow': '关注',

            'please_input_comment_content': '请输入评论内容',
            'publishing_comment': '发布中...',
            'comment_publish_failed_msg': '评论发布失败，请重试',
            'publish_button': '发布',
            'please_input_search_keyword': '请输入搜索关键词',
            'no_search_history': '暂无搜索历史',
            'clear_search_history': '清空搜索历史',
            'confirm_clear_search_history': '确定要清空所有搜索历史吗？',
            'searching': '搜索中...',
            'login_required': '请先登录',

        };

        var aotuman_current_index = 0;
        var aotuman_current_videoid = 0;
        var aotuman_videos = [];
        var aotuman_touch_start_y = 0;
        var aotuman_touch_end_y = 0;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            aotuman_init_swiper();
            aotuman_init_touch_events();

            // 自动播放第一个视频
            if (document.querySelector('.aotuman-video-slide')) {
                aotuman_play_current_video();
            }
        });



        // 评论相关变量
        var aotuman_current_video_id = null;
        var aotuman_modal_comments_page = 1;
        var aotuman_modal_comments_loading = false;
        var aotuman_modal_comments_has_more = true;



        // 搜索功能实现
        var aotuman_search_history = [];
        var aotuman_current_search_keyword = '';
        var aotuman_current_search_tab = 'videos';


        // 搜索输入框事件监听
        plugin_apoyl_snsvideo_jquery(document).ready(function() {
            // 搜索输入框输入事件
            plugin_apoyl_snsvideo_jquery('#aotuman-search-input').on('input', function() {
                var value = plugin_apoyl_snsvideo_jquery(this).val();
                if (value.length > 0) {
                    plugin_apoyl_snsvideo_jquery('.aotuman-search-clear-btn').show();
                } else {
                    plugin_apoyl_snsvideo_jquery('.aotuman-search-clear-btn').hide();
                    // 显示搜索建议，隐藏搜索结果
                    plugin_apoyl_snsvideo_jquery('#aotuman-search-suggestions').show();
                    plugin_apoyl_snsvideo_jquery('#aotuman-search-results').hide();
                }
            });

            // 搜索输入框回车事件
            plugin_apoyl_snsvideo_jquery('#aotuman-search-input').on('keypress', function(e) {
                if (e.which === 13) { // 回车键
                    aotuman_submit_search();
                }
            });

            // 评论弹层事件
            plugin_apoyl_snsvideo_jquery('#aotuman-comments-overlay').on('click', function(e) {
                if (e.target === this) {
                    aotuman_close_comments_modal();
                }
            });

            // 评论列表滚动加载
            plugin_apoyl_snsvideo_jquery('#aotuman-modal-comments-list').on('scroll', function() {
                var aotuman_list = plugin_apoyl_snsvideo_jquery(this);
                if (aotuman_list.scrollTop() + aotuman_list.height() >= aotuman_list[0].scrollHeight - 50) {
                    if (aotuman_current_video_id) {
                        aotuman_load_modal_comments(aotuman_current_video_id, false);
                    }
                }
            });

            // 评论输入框回车发送
            plugin_apoyl_snsvideo_jquery('#aotuman-modal-comment-input').on('keydown', function(e) {
                if (e.keyCode === 13) { // Enter
                    e.preventDefault();
                    aotuman_submit_modal_comment();
                }
            });
            // 初始加载消息徽章
            aotuman_update_message_badge();

            // 每30秒检查一次新消息
            setInterval(aotuman_check_new_messages, 30000);

            // 标签页事件监听器已移除，改为视频风格统一列表

            plugin_apoyl_snsvideo_jquery('.aotuman-action-btn.liked').each(function() {
                plugin_apoyl_snsvideo_jquery(this).find('.aotuman-icon-like').hide();
                plugin_apoyl_snsvideo_jquery(this).find('.aotuman-icon-liked').show();
            });

            plugin_apoyl_snsvideo_jquery('.aotuman-action-btn.favorited').each(function() {
                plugin_apoyl_snsvideo_jquery(this).find('.aotuman-icon-favorite').hide();
                plugin_apoyl_snsvideo_jquery(this).find('.aotuman-icon-favorited').show();
            });

            // 初始化关注状态 - 确保所有状态正确显示
            aotuman_init_follow_states();
        });

        // 加载屏幕控制
        window.addEventListener('load', function() {
            // 页面完全加载后隐藏加载屏幕
            setTimeout(function() {
                var aotuman_loading_screen = document.getElementById('aotuman-loading-screen');
                if (aotuman_loading_screen) {
                    aotuman_loading_screen.classList.add('hidden');
                    setTimeout(function() {
                        aotuman_loading_screen.style.display = 'none';
                    }, 500);
                }
            }, 1000); // 最少显示1秒加载屏幕
        });

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // 页面隐藏时暂停所有视频
                var aotuman_videos = document.querySelectorAll('video');
                aotuman_videos.forEach(function(video) {
                    video.pause();
                });
            } else {
                // 页面显示时恢复当前视频播放
                if (typeof aotuman_play_current_video === 'function') {
                    aotuman_play_current_video();
                }
            }
        });

        // 防止页面刷新时的闪烁
        document.addEventListener('DOMContentLoaded', function() {
            // 确保页面样式正确应用
            document.body.style.visibility = 'visible';
        });

        // 错误处理
        window.addEventListener('error', function(e) {

            // 隐藏加载屏幕，即使出现错误
            var aotuman_loading_screen = document.getElementById('aotuman-loading-screen');
            if (aotuman_loading_screen) {
                aotuman_loading_screen.classList.add('hidden');
            }
        });

        // 网络状态监听
        window.addEventListener('online', function() {
            aotuman_show_toast('The network connection has been restored.', 'success');
        });

        window.addEventListener('offline', function() {
            aotuman_show_toast('You are offline.', 'error');
        });



        // 当前个人中心标签页类型
        var aotuman_current_profile_tab = 'works';


        // 当前查看的用户信息
        var aotuman_current_user_profile = {
            uid: 0,
            username: '',
            is_following: false
        };

        // 当前用户个人页面标签页类型
        var aotuman_current_user_profile_tab = 'user-works';



    </script>
<!-- 消息页面 -->
<div id="aotuman-messages-page" class="aotuman-page-modal" style="display: none;">
    <div class="aotuman-page-header">
        <button class="aotuman-back-btn" onclick="aotuman_close_messages()">
            <span>←</span>
        </button>
        <h2>消息</h2>
        <div class="aotuman-messages-actions">

            <button class="aotuman-clear-btn" onclick="aotuman_clear_all_messages()">
清空
            </button>
        </div>
    </div>

    <div class="aotuman-messages-content">
        <!-- 视频风格统一消息列表 -->
        <div class="aotuman-message-list" id="messageList">
            <div class="aotuman-message-loading">
                <div class="aotuman-loading-spinner"></div>
                <p>加载中...</p>
            </div>
        </div>
    </div>
</div>

<!-- 个人中心页面 -->
<div id="aotuman-profile-page" class="aotuman-page-modal" style="display: none;">
    <div class="aotuman-page-header">
        <button class="aotuman-back-btn" onclick="aotuman_close_profile()">
            <span>←</span>
        </button>
        <h2>个人中心</h2>
        <button class="aotuman-settings-btn" onclick="aotuman_open_settings()">
            ⚙️
        </button>
    </div>

    <div class="aotuman-profile-content">
        <!-- 用户信息区域 -->
        <div class="aotuman-profile-header">
            <div class="aotuman-profile-avatar">
                <?php if($_G['uid']) { ?>
                <?php echo avatar($_G['uid'], 'big');?>                <?php } else { ?>
                <div class="aotuman-default-avatar">👤</div>
                <?php } ?>
            </div>
            <div class="aotuman-profile-info">
                <h3 class="aotuman-profile-username">
                    <?php if($_G['uid']) { ?>
                    <?php echo $_G['username'];?>
                    <?php } else { ?>
                    未登录
                    <?php } ?>
                </h3>
                <p class="aotuman-profile-id">ID: <?php if($_G['uid']) { ?><?php echo $_G['uid'];?><?php } else { ?>--<?php } ?></p>
            </div>
            <button class="aotuman-edit-profile-btn" onclick="aotuman_edit_profile()">
编辑资料
            </button>
        </div>

        <!-- 统计数据 -->
        <div class="aotuman-profile-stats">
            <div class="aotuman-stat-item" onclick="aotuman_view_stat('likes')">
                <div class="aotuman-stat-number" id="totalLikes">0</div>
                <div class="aotuman-stat-label">获赞</div>
            </div>
            <div class="aotuman-stat-item" onclick="aotuman_view_stat('following')">
                <div class="aotuman-stat-number" id="followingCount">0</div>
                <div class="aotuman-stat-label">关注</div>
            </div>
            <div class="aotuman-stat-item" onclick="aotuman_view_stat('followers')">
                <div class="aotuman-stat-number" id="followersCount">0</div>
                <div class="aotuman-stat-label">粉丝</div>
            </div>
            <div class="aotuman-stat-item" onclick="aotuman_view_stat('mutual')">
                <div class="aotuman-stat-number" id="mutualCount">0</div>
                <div class="aotuman-stat-label">互关</div>
            </div>
        </div>

        <!-- 标签页导航 -->
        <div class="aotuman-profile-tabs">
            <button class="aotuman-profile-tab active" data-tab="works" onclick="aotuman_switch_profile_tab('works')">
                <span class="aotuman-tab-icon">🎬</span>
                <span class="aotuman-tab-text">作品</span>
                <span class="aotuman-tab-count" id="worksCount">0</span>
            </button>
            <button class="aotuman-profile-tab" data-tab="favorites" onclick="aotuman_switch_profile_tab('favorites')">
                <span class="aotuman-tab-icon">⭐</span>
                <span class="aotuman-tab-text">收藏</span>
                <span class="aotuman-tab-count" id="favoritesCount">0</span>
            </button>
            <button class="aotuman-profile-tab" data-tab="likes" onclick="aotuman_switch_profile_tab('likes')">
                <span class="aotuman-tab-icon">❤️</span>
                <span class="aotuman-tab-text">喜欢</span>
                <span class="aotuman-tab-count" id="likesCount">0</span>
            </button>

        </div>

        <!-- 标签页内容区域 -->
        <div class="aotuman-profile-tab-content">
            <!-- 我的作品 -->
            <div class="aotuman-tab-pane active" id="worksPane">
                <div class="aotuman-content-grid" id="worksGrid">
                    <div class="aotuman-loading-placeholder">
                        <div class="aotuman-loading-spinner"></div>
                        <p>加载中...</p>
                    </div>
                </div>
            </div>

            <!-- 我的收藏 -->
            <div class="aotuman-tab-pane" id="favoritesPane">
                <div class="aotuman-content-grid" id="favoritesGrid">
                    <div class="aotuman-loading-placeholder">
                        <div class="aotuman-loading-spinner"></div>
                        <p>加载中...</p>
                    </div>
                </div>
            </div>

            <!-- 我的喜欢 -->
            <div class="aotuman-tab-pane" id="likesPane">
                <div class="aotuman-content-grid" id="likesGrid">
                    <div class="aotuman-loading-placeholder">
                        <div class="aotuman-loading-spinner"></div>
                        <p>加载中...</p>
                    </div>
                </div>
            </div>

            <!-- 观看历史 -->
            <div class="aotuman-tab-pane" id="historyPane">
                <div class="aotuman-content-grid" id="historyGrid">
                    <div class="aotuman-empty-state">
                        <div class="aotuman-empty-icon">🕒</div>
                        <div class="aotuman-empty-text">暂无观看历史</div>
                        <div class="aotuman-empty-desc">观看的视频会在这里显示</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 用户个人页面 -->
<div class="aotuman-user-profile-page" id="aotuman-user-profile-page" style="display: none;">
    <div class="aotuman-page-header">
        <button class="aotuman-back-btn" onclick="aotuman_close_user_profile()">
            <span>←</span>
        </button>
        <h2 id="userProfileTitle">用户主页</h2>
        <div class="aotuman-user-profile-actions">
            <!-- 关注按钮 -->
            <button class="aotuman-follow-btn" id="userProfileFollowBtn" onclick="aotuman_toggle_user_follow()" style="display: none;">
                <span id="followBtnText">+ 关注</span>
            </button>
        </div>
    </div>

    <div class="aotuman-user-profile-content">
        <!-- 用户信息区域 -->
        <div class="aotuman-user-info-section">
            <div class="aotuman-user-avatar" id="userProfileAvatar">
                <!-- 用户头像 -->
            </div>
            <div class="aotuman-user-details">
                <div class="aotuman-user-name" id="userProfileName">用户名</div>
                <div class="aotuman-user-stats">
                    <div class="aotuman-stat-item">
                        <span class="aotuman-stat-number" id="userWorksCount">0</span>
                        <span class="aotuman-stat-label">作品</span>
                    </div>
                    <div class="aotuman-stat-item">
                        <span class="aotuman-stat-number" id="userFollowersCount">0</span>
                        <span class="aotuman-stat-label">粉丝</span>
                    </div>
                    <div class="aotuman-stat-item">
                        <span class="aotuman-stat-number" id="userFollowingCount">0</span>
                        <span class="aotuman-stat-label">关注</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户标签页导航 -->
        <div class="aotuman-user-profile-tabs">
            <button class="aotuman-profile-tab active" data-tab="user-works" onclick="aotuman_switch_user_profile_tab('user-works')">
                <span class="aotuman-tab-icon">🎬</span>
                <span class="aotuman-tab-text">作品</span>
                <span class="aotuman-tab-count" id="userWorksTabCount">0</span>
            </button>
            <button class="aotuman-profile-tab" data-tab="user-info" onclick="aotuman_switch_user_profile_tab('user-info')">
                <span class="aotuman-tab-icon">ℹ️</span>
                <span class="aotuman-tab-text">资料</span>
                <span class="aotuman-tab-count"></span>
            </button>
        </div>

        <!-- 用户标签页内容区域 -->
        <div class="aotuman-user-profile-tab-content">
            <!-- 用户作品 -->
            <div class="aotuman-tab-pane active" id="user-worksPane">
                <div class="aotuman-content-grid" id="user-worksGrid">
                    <div class="aotuman-loading-placeholder">
                        <div class="aotuman-loading-spinner"></div>
                        <p>加载中...</p>
                    </div>
                </div>
            </div>

            <!-- 用户资料 -->
            <div class="aotuman-tab-pane" id="user-infoPane">
                <div class="aotuman-user-info-content" id="user-infoContent">
                    <div class="aotuman-info-item">
                        <div class="aotuman-info-label">用户ID</div>
                        <div class="aotuman-info-value" id="userIdValue">--</div>
                    </div>
                    <div class="aotuman-info-item">
                        <div class="aotuman-info-label">注册时间</div>
                        <div class="aotuman-info-value" id="userRegTimeValue">--</div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>


</body>
</html>