<?php if(!defined('IN_DISCUZ')) exit('Access Denied'); hookscriptoutput('upload');?><?php include template('common/header'); ?><link href="source/plugin/apoyl_snsvideo/static/css/pc/upload.css" rel="stylesheet" />


<div class="aotuman-upload-container">
    <!-- 页面头部 -->
    <div class="aotuman-upload-header">
        <h1>发布视频</h1>
        <p>发布视频</p>
    </div>
    
    <!-- 上传表单 -->
    <form class="aotuman-upload-form" method="post" id="aotuman-upload-form" action="plugin.php?id=apoyl_snsvideo&amp;action=submit_video">
    <input type="hidden" name="formhash" value="<?php echo FORMHASH;?>" />
    <input type="hidden" name="video_file_path" id="aotuman-video-file-path" value="" />
    <input type="hidden" name="original_filename" id="aotuman-original-filename" value="" />
    <input type="hidden" name="file_size" id="aotuman-file-size" value="" />
    <!-- 视频文件 -->
        <div class="aotuman-form-group">
            <label class="aotuman-form-label">选择视频 *</label>
            <div class="aotuman-file-upload">
                <input type="file" id="aotuman-video-input" class="aotuman-file-input"
                       accept="video/*" onchange="aotuman_handle_video_select(this)">
                <label for="aotuman-video-input" class="aotuman-file-label" id="aotuman-video-label">
                    <i class="fa fa-cloud-upload aotuman-file-icon"></i>
                    <div class="aotuman-file-text">
                        <div>选择视频</div>
                        <div style="margin-top: 5px; font-size: 12px;">
                            支持的视频格式：MP4、MOV<br>
                            视频文件大小不能超过设定的限制 (200MB)
                        </div>
                    </div>
                </label>
                <div class="aotuman-file-selected" id="aotuman-video-selected">
                    <div class="aotuman-file-info">
                        <i class="fa fa-file-video-o"></i>
                        <div>
                            <div class="aotuman-file-name" id="aotuman-video-name"></div>
                            <div class="aotuman-file-size" id="aotuman-video-size"></div>
                            <div class="aotuman-chunk-info" id="chunk-info" style="font-size: 12px; color: #666; margin-top: 5px; display: none;"></div>
                        </div>
                    </div>

                    <!-- 上传进度条 -->
                    <div class="aotuman-progress" id="upload-progress" style="display: none;">
                        <div class="aotuman-progress-bar" id="progress-bar" style="width: 0%;"></div>
                        <div class="aotuman-progress-text" id="progress-text">0%</div>
                    </div>

                    <!-- 上传状态 -->
                    <div class="aotuman-upload-status" id="upload-status" style="margin-top: 10px; font-size: 14px; color: #333;"></div>

                    <!-- 上传控制按钮 -->
                    <div class="aotuman-upload-controls" id="upload-controls" style="display: none; margin-top: 10px;">
                        <button type="button" class="aotuman-btn aotuman-btn-primary" id="start-upload" onclick="aotuman_start_chunked_upload()" style="margin-right: 10px;">
                            <i class="fa fa-play"></i> 开始上传
                        </button>
                        <button type="button" class="aotuman-btn aotuman-btn-warning" id="pause-upload" onclick="aotuman_pause_upload()" style="margin-right: 10px; display: none;">
                            <i class="fa fa-pause"></i> 暂停上传
                        </button>
                        <button type="button" class="aotuman-btn aotuman-btn-success" id="resume-upload" onclick="aotuman_resume_upload()" style="margin-right: 10px; display: none;">
                            <i class="fa fa-play"></i> 恢复上传
                        </button>
                        <button type="button" class="aotuman-btn aotuman-btn-danger" id="cancel-upload" onclick="aotuman_cancel_upload()">
                            <i class="fa fa-stop"></i> 取消上传
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 视频预览 -->
        <div class="aotuman-preview" id="aotuman-video-preview" style="display: none;">
            <video class="aotuman-preview-video" id="aotuman-preview-video" controls></video>
            <div class="aotuman-preview-info">
                <div class="aotuman-preview-title">视频预览</div>
                <div class="aotuman-preview-desc">上传完成后可以预览视频效果</div>
            </div>
        </div>
        
        <!-- 视频标题 -->
        <div class="aotuman-form-group">
            <label class="aotuman-form-label" for="aotuman-title">视频标题 *</label>
            <input type="text" name="aotuman_title" id="aotuman-title" class="aotuman-form-input"
                   placeholder="视频标题" required maxlength="255">
        </div>

        <!-- 视频描述 -->
        <div class="aotuman-form-group">
            <label class="aotuman-form-label" for="aotuman-description">视频描述</label>
            <textarea name="aotuman_description" id="aotuman-description" class="aotuman-form-input aotuman-form-textarea"
                      placeholder="视频描述"></textarea>
        </div>

        <!-- 视频标签 -->
        <div class="aotuman-form-group">
            <label class="aotuman-form-label" for="aotuman-tags">视频标签</label>
            <input type="text" name="aotuman_tags" id="aotuman-tags" class="aotuman-form-input"
                   placeholder="多个标签用逗号分隔">
            <div class="aotuman-form-help">多个标签用逗号分隔</div>
        </div>
        
        <!-- 视频封面 -->
        <div class="aotuman-form-group" >
            <label class="aotuman-form-label">视频封面</label>
            <div class="aotuman-file-upload">
                <input type="file" name="aotuman_cover" id="aotuman-cover-input" class="aotuman-file-input"
                       accept="image/*" onchange="aotuman_handle_cover_select(this)" style="display: none;">
                <div class="aotuman-cover-upload" onclick="document.getElementById('aotuman-cover-input').click()">
                    <i class="fa fa-image"></i>
                    <div>选择封面图片</div>
                    <div style="margin-top: 5px; font-size: 12px; color: #666;">支持JPG、PNG格式，建议尺寸9:16，最大5MB</div>
                </div>
                <div class="aotuman-cover-selected" id="aotuman-cover-selected" style="display: none;">
                    <div class="aotuman-cover-info">
                        <i class="fa fa-file-image-o"></i>
                        <div class="aotuman-cover-details">
                            <div class="aotuman-cover-name" id="aotuman-cover-name"></div>
                            <div class="aotuman-cover-size" id="aotuman-cover-size"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 封面预览 -->
        <div class="aotuman-preview" id="aotuman-cover-preview">
            <img class="aotuman-preview-cover" id="aotuman-preview-cover" alt="封面预览">
        </div>
        
        <!-- 提交按钮 -->
        <div class="aotuman-form-actions">
            <button type="submit" name="aotuman_submit" class="aotuman-btn aotuman-btn-primary disabled" id="aotuman-submit-btn" value="true" disabled>
                <i class="fa fa-check"></i> 提交视频信息
            </button>
            <a href="plugin.php?id=apoyl_snsvideo" class="aotuman-btn aotuman-btn-default">
                <i class="fa fa-arrow-left"></i> 返回
            </a>
        </div>
    </form>
</div>
<script src="static/js/jquery/jquery.min.js" type="text/javascript" type="text/javascript" ></script>
<script src="source/plugin/apoyl_snsvideo/static/js/pc/upload.min.js" type="text/javascript" type="text/javascript"></script>
<script>
var plugin_apoyl_snsvideo_jquery = jQuery.noConflict(true);


var CHUNK_SIZE = 5 * 1024 * 1024;
var aotuman_upload_state = {
    file: null,
    fileName: '',
    fileSize: 0,
    totalChunks: 0,
    currentChunk: 0,
    uploadId: '',
    isUploading: false,
    isPaused: false,
    uploadedChunks: [],
    retryCount: 0,
    maxRetries: 3
};

var discuz_uid = '<?php echo $_G['uid'];?>';
var discuz_username = '<?php echo $_G['username'];?>';
var discuz_formhash = '<?php echo FORMHASH;?>';
var siteurl= '<?php echo $siteurl;?>';
var aotuman_lang = {
    'video_duration':'视频时长',
    'video_preview_load_error':'视频预览加载失败',
    'cover_image_type_error':'请选择图片文件',
    'cover_image_size_error':'图片文件过大，请选择小于5MB的图片',



}
document.addEventListener('DOMContentLoaded', function() {

    var videoUploadArea = document.querySelector('.aotuman-upload-area');
    var coverUploadArea = document.querySelector('.aotuman-cover-upload');


    if (videoUploadArea) {
        videoUploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        videoUploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        videoUploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');

            var files = e.dataTransfer.files;
            if (files.length > 0) {
                var videoInput = document.getElementById('aotuman-video-input');
                if (videoInput) {
                    videoInput.files = files;
                    aotuman_handle_video_select(videoInput);
                }
            }
        });
    }


    if (coverUploadArea) {
        coverUploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        coverUploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        coverUploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');

            var files = e.dataTransfer.files;
            if (files.length > 0) {
                var coverInput = document.getElementById('aotuman-cover-input');
                if (coverInput) {
                    coverInput.files = files;
                    aotuman_handle_cover_select(coverInput);
                }
            }
        });
    }
});


document.getElementById('aotuman-upload-form').addEventListener('submit', function(e) {

    var videoInput = document.getElementById('aotuman-video-input');
    var titleInput = document.getElementById('aotuman-title');


    var videoFilePathInput = document.getElementById('aotuman-video-file-path');
    if (!videoFilePathInput || !videoFilePathInput.value.trim()) {
        var errorMessage = '!aotuman_validate_video_empty!';
        aotuman_show_error_message(errorMessage, document.querySelector('.aotuman-upload-area'));
        e.preventDefault();
        return false;
    }


    if (!titleInput.value.trim()) {
        var errorMessage = '!aotuman_validate_title_empty!';
        aotuman_show_error_message(errorMessage, titleInput);
        titleInput.focus();
        e.preventDefault();
        return false;
    }




    var submitBtn = document.getElementById('aotuman-submit-btn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 处理中...';


    return true;
        progressBar.style.width = '100%';
    }, 2000);





</script><?php include template('common/footer'); ?>