<?php if(!defined('IN_DISCUZ')) exit('Access Denied'); ?><?php
$return = <<<EOF

{$restr}

EOF;
 if($floatstrleft||$floatstrright) { 
$return .= <<<EOF
			
<style>.apoyl_teladv_tfr {
    right: 0;
    text-align: right;
}
.apoyl_teladv_left {
    left: 0;

}
.apoyl_teladv_fr {
    float: right;
    position: fixed;
    top: 50%;
    z-index: 998;
}</style>

EOF;
 } if($floatstrleft) { 
$return .= <<<EOF
	
<div class="apoyl_teladv_fr apoyl_teladv_left">{$floatstrleft}</div>

EOF;
 } if($floatstrright) { 
$return .= <<<EOF
	
<div class="apoyl_teladv_fr apoyl_teladv_tfr">{$floatstrright}</div>

EOF;
 } if(!$isc&&$apoylglobal) { 
$return .= <<<EOF

           <link rel="stylesheet" href="source/plugin/apoyl_teladv/pub/apoyllayer.css">
    			<div class="apoyl_teladv_wfloor" id="apoyl_teladv_layer">
    				<div class="apoyl_teladv_wrap">
    					<span class="apoyl_teladv_close" ></span>{$apoylglobal}
    				</div>
    		  </div>
    		  
EOF;
 if(!$closejq) { 
$return .= <<<EOF

            <script src="source/plugin/apoyl_teladv/pub/jquery-3.1.1.min.js" type="text/javascript"></script>
           
EOF;
 } 
$return .= <<<EOF

            <script>
    		  apoyl_teladv_teladv = jQuery.noConflict();
            apoyl_teladv_teladv(function(){
            	showLayer('apoyl_teladv_layer');
            	function showLayer(id){
            		var layer = apoyl_teladv_teladv('#'+id),
            			layerwrap = layer.find('.apoyl_teladv_wrap');
            		layer.fadeIn();
            
            		layerwrap.css({
            			'margin-top': -layerwrap.outerHeight()/2
            		});
            	}
            	function hideLayer(){
            		apoyl_teladv_teladv('.apoyl_teladv_wfloor').fadeOut();
            	}
            	apoyl_teladv_teladv('.apoyl_teladv_close').on('click', function() {
            		hideLayer();
            	});
            });
            </script>
            
EOF;
 } 
$return .= <<<EOF


EOF;
?>