<?php if(!defined('IN_DISCUZ')) exit('Access Denied'); ?><?php
$return = <<<EOF


EOF;
 if($ifs) { 
$return .= <<<EOF

<script src="source/plugin/apoyl_weixinshare/img/jweixin-1.6.0.js" type="text/javascript"></script>
<script>

    function apoyl_weixinshare_getMeta() {
        var obj = document.getElementsByTagName('meta');
        var desc = '{$apoylmsg}';
        for (i in obj) {
            if (obj[i].name.toLowerCase() == 'description' && obj[i].content.length > 8) {
                desc = obj[i].content;
                break;
            }
        }

        return desc;
    }

    wx.config({
        debug: {$opendebug},
        appId: '{$signPackage["appId"]}',
        timestamp: '{$signPackage["timestamp"]}',
        nonceStr: '{$signPackage["nonceStr"]}',
        signature: '{$signPackage["signature"]}',
        jsApiList: [
            
EOF;
 if(in_array(1,$ifs)) { 
$return .= <<<EOF

            'updateTimelineShareData',
            
EOF;
 } 
$return .= <<<EOF

            
EOF;
 if(in_array(2,$ifs)) { 
$return .= <<<EOF

            'updateAppMessageShareData',
            
EOF;
 } 
$return .= <<<EOF

        ]
    });

    wx.ready(function () {
        var apoyl_weixinshare_title = document.title;
        var apoyl_weixinshare_link = window.location.href;
        var apoyl_weixinshare_desc = apoyl_weixinshare_getMeta();
        
EOF;
 if(in_array(1,$ifs)) { 
$return .= <<<EOF

        wx.updateTimelineShareData({
            title: apoyl_weixinshare_title,
            link: apoyl_weixinshare_link,
            imgUrl: '{$apoylimg}',
            success: function () {

            }
        });
        
EOF;
 } 
$return .= <<<EOF

        
EOF;
 if(in_array(2,$ifs)) { 
$return .= <<<EOF


        wx.updateAppMessageShareData({
            title: apoyl_weixinshare_title,
            desc: apoyl_weixinshare_desc,
            link: apoyl_weixinshare_link,
            imgUrl: '{$apoylimg}',
            success: function () {

            }
        });
        
EOF;
 } 
$return .= <<<EOF


    });
</script>

EOF;
 } 
$return .= <<<EOF


EOF;
?>