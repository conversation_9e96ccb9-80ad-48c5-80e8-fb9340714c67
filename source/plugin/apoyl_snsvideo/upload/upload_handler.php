<?php
/**
 * [liyuanchao] (C)2019-2099    http://www.apoyl.com/
 * This is NOT a freeware, use is subject to license terms
 *
 * $Id: upload_handler.php 2025-7 liyuanchao（凹凸曼） $
 */

if (!defined('IN_DISCUZ')) {
    exit('Access Denied');
}

   
if (!$_G['uid']) {
    showmessage(lang('plugin/apoyl_snsvideo', 'aotuman_login_required'), 'member.php?mod=logging&action=login');
}
$vipgroup=empty($aotuman_config['vipgroup'])?array():unserialize($aotuman_config['vipgroup']);

if(!in_array($_G['groupid'], $vipgroup)){
    showmessage(lang('plugin/apoyl_snsvideo', 'aotuman_upload_not_allowed'));
}
if ($aotuman_action == 'upload') {

    include template('apoyl_snsvideo:upload');
    exit;
}

   
if ($aotuman_action == 'submit_video'&&FORMHASH == $_GET['formhash'] ) {

    
    $video_file_path = trim($_POST['video_file_path']);
    $original_filename = trim($_POST['original_filename']);
    $file_size = intval($_POST['file_size']);
    $title = trim($_POST['aotuman_title']);
    $description = trim($_POST['aotuman_description']);
    $tags = trim($_POST['aotuman_tags']);
    $cover_path = trim($_POST['aotuman_cover_path']);    
    
       
    if (empty($video_file_path)) {
        showmessage(lang('plugin/apoyl_snsvideo', 'aotuman_upload_video_required'), 'plugin.php?id=apoyl_snsvideo&action=upload');
    }

    if (empty($title)) {
        showmessage(lang('plugin/apoyl_snsvideo', 'aotuman_upload_title_required'), 'plugin.php?id=apoyl_snsvideo&action=upload');
    }

       
    if (strlen($title) > 255) {
        showmessage(lang('plugin/apoyl_snsvideo', 'aotuman_upload_title_too_long'), 'plugin.php?id=apoyl_snsvideo&action=upload');
    }
    
       
    $full_file_path = DISCUZ_ROOT . './' . $video_file_path;
    if (!file_exists($full_file_path)) {
        showmessage(lang('plugin/apoyl_snsvideo', 'aotuman_upload_file_not_exist'), 'plugin.php?id=apoyl_snsvideo&action=upload');
    }
    
       
    $file_ext = strtolower(pathinfo($original_filename, PATHINFO_EXTENSION));
    $allowed_extensions = array('mp4', 'mov');
    if (!in_array($file_ext, $allowed_extensions)) {
        showmessage(lang('plugin/apoyl_snsvideo', 'aotuman_upload_unsupported_type'), 'plugin.php?id=apoyl_snsvideo&action=upload');
    }
    
       
    $tags_array = array();
    if (!empty($tags)) {
        $tags_array = array_filter(array_map('trim', explode(',', $tags)));
        $tags = implode(',', $tags_array);
    }
    
       
    $duration = 0;

    

    $cover_url = '';

       
    $final_cover_url = !empty($cover_path) ? $cover_path : $cover_url;

       
    $video_data = array(
        'aotuman_title' => $title,
        'aotuman_description' => $description,
        'aotuman_tags' => $tags,
        'aotuman_video_url' => $video_file_path,
        'aotuman_cover_url' => $final_cover_url,

        'aotuman_file_size' => $file_size,
        'aotuman_duration' => $duration,
        'aotuman_uid' => $_G['uid'],
        'aotuman_username' => $_G['username'],
        'aotuman_addtime' => TIMESTAMP,
        'aotuman_status' => 1,    
        'aotuman_views' => 0,
        'aotuman_likes' => 0,
        'aotuman_favorites' => 0,
        'aotuman_comments' => 0,
        'aotuman_shares' => 0
    );
    
    try {
        $videoid = DB::insert('plugin_apoyl_snsvideo_video', $video_data, true);
        
        if ($videoid) {
               
            $log_data = array(
                'aotuman_videoid' => $videoid,
                'aotuman_uid' => $_G['uid'],
                'aotuman_username' => $_G['username'],
                'aotuman_action' => 'upload',
                'aotuman_ip' => $_G['clientip'],
                'aotuman_addtime' => TIMESTAMP
            );
            

            
            showmessage(
                lang('plugin/apoyl_snsvideo', 'upload_success_message'),
                'plugin.php?id=apoyl_snsvideo&action=play&videoid=' . $videoid,
                array('videoid' => $videoid)
            );
        } else {
               
            if (file_exists($full_file_path)) {
                unlink($full_file_path);
            }
            if (!empty($cover_url) && file_exists(DISCUZ_ROOT . './' . $cover_url)) {
                unlink(DISCUZ_ROOT . './' . $cover_url);
            }
            
            showmessage(lang('plugin/apoyl_snsvideo', 'aotuman_upload_info_save_failed'), 'plugin.php?id=apoyl_snsvideo&action=upload');
        }
    } catch (Exception $e) {
           
        if (file_exists($full_file_path)) {
            unlink($full_file_path);
        }
        if (!empty($cover_url) && file_exists(DISCUZ_ROOT . './' . $cover_url)) {
            unlink(DISCUZ_ROOT . './' . $cover_url);
        }
        
        showmessage(lang('plugin/apoyl_snsvideo', 'aotuman_system_error') . '：' . $e->getMessage(), 'plugin.php?id=apoyl_snsvideo&action=upload');
    }
}

/**
 * 验证视频文件的辅助函数
 */
function apoyl_snsvideo_validate_video_file($file_path, $original_filename) {
       
    if (!file_exists($file_path)) {
        return array('success' => false, 'message' => lang('plugin/apoyl_snsvideo', 'aotuman_upload_file_not_exist'));
    }
    
       
    $file_size = filesize($file_path);
    $max_size = 500 * 1024 * 1024;    
    if ($file_size > $max_size) {
        return array('success' => false, 'message' => lang('plugin/apoyl_snsvideo', 'aotuman_upload_file_too_big'));
    }
    
       
    $file_ext = strtolower(pathinfo($original_filename, PATHINFO_EXTENSION));
    $allowed_extensions = array('mp4',  'mov');
    if (!in_array($file_ext, $allowed_extensions)) {
        return array('success' => false, 'message' => lang('plugin/apoyl_snsvideo', 'aotuman_upload_unsupported_type'));
    }
    
       
    if (function_exists('finfo_open')) {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($finfo, $file_path);
        finfo_close($finfo);
        
        $allowed_mime_types = array(
            'video/mp4',
            'video/x-ms-wmv',

        );
        
        if (!in_array($mime_type, $allowed_mime_types)) {
            return array('success' => false, 'message' => lang('plugin/apoyl_snsvideo', 'aotuman_upload_file_validate_failed'));
        }
    }
    
    return array('success' => true, 'message' => lang('plugin/apoyl_snsvideo', 'aotuman_upload_file_validate_success'));
}

/**
 * 清理临时文件的辅助函数
 */
function apoyl_snsvideo_cleanup_temp_files($file_path, $cover_path = '') {
    if (!empty($file_path) && file_exists($file_path)) {
        unlink($file_path);
    }
    
    if (!empty($cover_path) && file_exists($cover_path)) {
        unlink($cover_path);
    }
}
?>
