body { background-color: transparent; }
	.wp { width: 1210px; }
	#hd { margin-bottom: 0; position: relative; height: 140px; }
		#hd h2 { padding: 25px 0 0 15px; font-size: 14px; }
			#hd h2 strong { display: block; }
		#nv { position: absolute; left: 10px; height: auto; bottom: 0; background: transparent none; }
			#nv li, #home { margin-right: 1px; padding: 0 6px; border: none; height: 30px; background: rgba(255, 255, 255, 0.7); line-height: 30px; font-size: 12px; }
				#nv a, #nv a:visited { padding: 0 14px; color: #333; }
		#ct, .n, .w { padding-top: 15px; border: none; background: transparent none !important; }
		.area { padding: 0 5px; }
		.frame { background: transparent none; }
		.block { padding: 0; }
			.block .dxb_bc { padding: 5px 10px 10px; }
		.block .title { padding: 0 10px; font-size: 12px; color: #333; }
	#pcd .hm img { padding: 2px; background: {WRAPBG}; border: 1px solid; border-color: {COMMONBG} {COMMONBORDER} {COMMONBORDER} {COMMONBG}; }
	#pcd h2 { margin: 10px auto 0; }
	.mls li { width: 69px; }
.move-span{ margin: 1px; }
.column, #share { overflow: hidden; }
.musicbj { width: 60px; height: 60px;}
#spaceinfoshow { position: relative;}
	#spaceinfoshow .oshr { float: none; margin-left: 10px; padding: 1px 5px 1px 22px; color: {HIGHLIGHTLINK}; }
#doingform #message { width: 500px; }
#toptb { padding: 0 10px; border-bottom: 1px solid {COMMONBORDER}; background: url({IMGDIR}/topnav.png) repeat-x; }
#hd h2 span { display: block; }
#hd ul.savebar { width: 450px; height: 20px; border: 1px solid #ccc;}
#hd ul.savebar li { float:right; margin: 0 3px;}
.ipx {background: none; height: 17px;}
#infoedit { margin-left: 5px; padding: 0 5px; background: #369; color: #FFF; cursor: pointer; }
#diy-tg { float: right; color: #9E831F; padding: 0 4px !important;}
.topnav_pop li span{ display: none; }
#myspace_menu li a, #navs_menu li a { background-repeat: no-repeat; background-position: -200px 0; }
.ct2 .sd { width: 180px; }

.mn { display: inline; margin-left: 10px; width: 1000px !important; }
	.mt { margin: 0; padding: 0; }
	h1.mt { font-size: 12px; }
	.bm { background: #FFF; zoom: 1; }
.sd { display: inline; margin-right: 10px; min-height: 0; }
	#pcd { padding: 15px 4px 15px 6px; }

	.mla li, .mlp li { width: 153px; }
	.buddy li { width: 156px; }
	.pic .c img { max-width: 714px; }
	.ul_list {}
		.ul_list a { padding: 2px 0; }
		.ul_list li:before { font-family: dzicon; line-height: 14px; font-size: 16px; color: #7DA0CC; margin-right: 4px; }
		.ul_diy:before { content: "\f107"; }
		.ul_msg:before { content: "\f10e"; }
		.ul_avt:before { content: "\f102"; }
		.ul_profile:before { content: "\f147"; }
		.ul_add:before { content: "\f11f"; }
		.ul_ignore:before { content: "\f100"; }
		.ul_contect:before { content: "\f10e"; }
		.ul_poke:before { content: "\f146"; }
		.ul_pm:before { content: "\f119"; }
		.ul_list .ul_magicgift a { padding-left: 20px; }
		.ul_broadcast:before { content: "\f131"; }
		.ul_flw:before { content: "\f11e"; }

#magicreceivegift { position: absolute; top: 50px; right: 20px; }

.ct_vw_mn .buddy li { width: 170px; }