<!--{template common/header}-->

<div id="pt" class="bm cl">
	<div class="z">
		<a href="./" class="nvhm" title="{lang homepage}">$_G['setting']['bbname']</a> <em>&rsaquo;</em>
		<!--{if empty($_GET[action])}-->
			{lang faq}
		<!--{else}-->
			<a href="misc.php?mod=faq">{lang faq}</a>$navigation
		<!--{/if}-->
	</div>
</div>

<div id="ct" class="ct2_a wp cl">
	<div class="mn">
		<div class="bm bw0">
			<form method="post" autocomplete="off" action="misc.php?mod=faq&action=search" class="y mtn pns">
				<input type="hidden" name="formhash" value="{FORMHASH}" />
				<input type="hidden" name="searchtype" value="all" />
				<input type="text" name="keyword" size="16" value="$keyword" class="px vm" />
				<button type="submit" name="searchsubmit" class="pn vm" value="yes"><em>{lang search}</em></button>
			</form>
			<!--{if empty($_GET[action])}-->
				<h1 class="mt mbm">{lang all}{lang faq}</h1>
				<div class="lum">
					<!--{loop $faqparent $fpid $parent}-->
						<h2 class="blocktitle"><a href="misc.php?mod=faq&action=faq&id=$fpid">$parent[title]</a></h2>
						<ul name="$parent[title]">
							<!--{loop $faqsub[$parent[id]] $sub}-->
								<li><a href="misc.php?mod=faq&action=faq&id=$sub[fpid]&messageid=$sub[id]">$sub[title]</a></li>
							<!--{/loop}-->
						</ul>
					<!--{/loop}-->
				</div>
			<!--{elseif $_GET[action] == 'faq'}-->
				<h1 class="mt mbm">$ctitle</h1>
				<!--{loop $faqlist $faq}-->
					<div id="messageid$faq[id]_c" class="umh{if $messageid != $faq[id]} umn{/if}">
						<h3 onclick="toggle_collapse('messageid$faq[id]', 1, 1);">$faq[title]</h3>
						<div class="umh_act">
							<p class="umh_cb" onclick="toggle_collapse('messageid$faq[id]', 1, 1);">[ {lang open} ]</p>
						</div>
					</div>
					<div class="um" id="messageid$faq[id]" style="{if $messageid != $faq[id]} display: none {/if}">$faq[message]</div>
				<!--{/loop}-->
			<!--{elseif $_GET[action] == 'search'}-->
				<h1 class="mt mbm">{lang keyword_faq}</h1>
				<!--{if $faqlist}-->
					<!--{loop $faqlist $faq}-->
						<div class="umh schfaq"><h3>$faq[title]</h3></div>
						<div class="um">$faq[message]</div>
					<!--{/loop}-->
				<!--{else}-->
					<p class="emp">{lang faq_search_nomatch}</p>
				<!--{/if}-->
			<!--{elseif $_GET[action] == 'plugin' && !empty($_GET['id'])}-->
				<!--{eval include(template($_GET['id']));}-->
			<!--{/if}-->
		</div>
	</div>
	<div class="appl">
		<div class="tbn">
			<h2 class="mt bbda">{lang faq}</h2>
			<ul>
				<li class="cl{if empty($_GET[action])} a{/if}"><a href="misc.php?mod=faq">{lang all}</a></li>
				<!--{loop $faqparent $fpid $parent}-->
					<li name="$parent[title]" class="cl{if $_GET[id] == $fpid} a{/if}"><a href="misc.php?mod=faq&action=faq&id=$fpid">$parent[title]</a></li>
				<!--{/loop}-->
				<!--{if !empty($_G['setting']['plugins']['faq'])}-->
					<!--{loop $_G['setting']['plugins']['faq'] $id $module}-->
						<li class="cl{if $_GET[id] == $id} a{/if}"><a href="misc.php?mod=faq&action=plugin&id=$id">$module[name]</a></li>
					<!--{/loop}-->
				<!--{/if}-->
			</ul>
		</div>
		<!--{hook/faq_extra}-->
	</div>
</div>

<!--{template common/footer}-->