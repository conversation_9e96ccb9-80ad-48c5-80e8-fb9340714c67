/* IE compatible CSS file for Discuz! X
This file is automatically generated. */

/* ------ extracted from common.css ------ */
.ie_all .fic4 { font-size: 17px; }.ie_all .fic6 { font-size: 19px; }.ie_all .fic8 { font-size: 22px; }
.ie8 .fic4:before { font-size: 17px; }.ie8 .fic6:before { font-size: 19px; }.ie8 .fic8:before { font-size: 22px; }
hr { *margin-top: -8px !important; *margin-bottom: -8px !important; }
hr.bk { *margin-bottom: 2px !important; }
.ie6 .sec .p_pop { white-space: expression(this.offsetWidth >= 220 ? 'normal' : 'nowrap'); width: expression(this.offsetWidth >= 220 ? 200 : 'auto'); }
.ie6 .pn { overflow-x: visible; width: 0; }
.ie7 .pn em, .ie7 .pn span, .ie7 .pn strong { padding: 0 5px; line-height: 18px; }
.ie6 a.pn { width: auto; }
.ie6 a.pn em, .ie6 a.pn span, .ie6 a.pn strong { display: block; }
.ie7 a.pn em, .ie7 a.pn span, .ie7 a.pn strong { line-height: 21px; }
.ie6 .pr, .ie6 .pc, .ie7 .pr, .ie7 .pc { margin-right: 2px; }
.ie6 .pbt .ftid a, .ie7 .pbt .ftid a { margin-top: 1px; }
.ie6 .alert_right, .ie7 .alert_right { background: url({IMGDIR}/right.gif) no-repeat 8px 8px; }
.ie6 .alert_error, .ie7 .alert_error { background: url({IMGDIR}/error.gif) no-repeat 8px 8px; }
.ie6 .alert_info, .ie7 .alert_info { background: url({IMGDIR}/info.gif) no-repeat 8px 8px; }
.ie6 .hdc { height: 70px; }
#scbar_btn { *font-family: 'dzicon'; *text-align: center; *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf101;&nbsp;'); }
.ie8 #scbar_txt { display: block; }
.ie8 #scbar_type { margin-left: 0; }
.ie_all #nv li { line-height: 36px; }
.ie6 #nv li { line-height: 33px; }
.ie6 #mu, .ie7 #mu { line-height: 0; font-size: 0; }
#um { _padding-right: 54px; }
.vwmy { *font-family: dzicon,{FONT}; *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf100;&nbsp;'+this.innerHTML); }
.vwmy.qq { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf118;&nbsp;'+this.innerHTML); }
.ie6 #g_upmine { border: 0; }
.ie6 #qmenu_menu ul.nav li { clear: none !important; width: auto !important; }
.ie6 #ct { height: 300px; }
.ct2 .sd { _overflow-y: visible; }
.ct2_a, .ct3_a { background-image: url({IMGDIR}/vlineb.png); }
.ie6 #scrolltop { position: absolute; bottom: auto; }
#scrolltop a { *font-family: dzicon; *font-size: 24px; *line-height: 24px; }
#scrolltop a.scrolltopa { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf11b;&nbsp;'); }
#scrolltop a.replyfast { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf11c;&nbsp;'); }
#scrolltop a.returnlist, #scrolltop a.returnboard { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf11d;&nbsp;'); }
.nvhm { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf103;&nbsp;'); }
.ie6 #uhd .tb .a { position: relative; }
.ie6 .tb .a, .ie6 .tb .current { position: relative; }
.ie6 .tb .o a { float: left; }
* html .a_fl, * html .a_fr { position: absolute; top: expression(offsetParent.scrollTop+350); }
* html .a_cb { top: expression(offsetParent.scrollTop+20); }
* html .a_cn { position: absolute; top: expression(offsetParent.scrollTop+document.documentElement.clientHeight-this.offsetHeight); }
.ie8 .xld .atc { max-width: 86px; }
.ie6 .xld .atc img { width: expression(this.width > 80 && this.width>=this.height ? 80 : true); height: expression(this.height > 80 && this.width<=this.height ? 80 : true); }
* html .xlda dd img { width: expression(this.width > 550 ? 550 : true); }
.mla img { _width: expression(this.width > 120 && this.width>=this.height ? 120 : true); _height: expression(this.height > 120 && this.width<=this.height ? 120 : true); }
* html .mlp img { width: expression(this.width > 120 && this.width>=this.height ? 120 : true); height: expression(this.height > 120 && this.width<=this.height ? 120 : true); }
.lk .m img { margin-top: 4px; margin-top/*\**/: 1px\9; }
.sllt_p { *float: left; }
.ie6 .slg, .ie7 .slg { width: expression(this.parentNode.offsetWidth); }
.switchwidth { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf106;&nbsp;'); }
#sslct { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf107;&nbsp;'); }
.ie6 .p_pop { width: 100px; }
.ie6 .p_pop li { zoom: 1; clear: both; width: 100%; }
.ie6 .p_pop a { position: relative; }
.ie6 .ignore_notice { display: none; }
.t_l, .t_c, .t_r, .m_l, .m_r, .b_l, .b_c, .b_r { filter: alpha(opacity=20); }
.flbc { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xd7;&nbsp;'); }
* html .pm .flb { padding: 4px 5px 1px; }
* html .pmd { width: expression(this.offsetWidth > 292 ? 292+'px':'auto'); }
* html .pmd img { width: expression(this.width > 292 ? 292 : true); }
* html .pmd .blockcode code { font-family: 'Courier New', serif; }
.pmo { line-height /*\**/: 26px\9; }
.lgfm { *margin-bottom: -10px; }
.fsb .pnr { *margin-top: 4px; }
.ie6 .pg label { padding-top: 3px; height: 23px; }
* html .pc_inner span { display: inline-block; }
.ie6 .jump_bdl li { clear: none !important; float: left !important; width: 178px !important; }
* html .focus { position: absolute; top: expression(offsetParent.scrollTop+document.documentElement.clientHeight-this.offsetHeight); }
.filebtn .pf { filter:alpha(opacity=0); }
.frame .sd { _height: auto; }
* html .frame-tab .tb li a { float: left; }
.slideshow span.title, .slidebar li { filter:progid:DXImageTransform.Microsoft.gradient(startColorstr = #30000000, endColorstr = #30000000); }
.slidebar li.on { filter:progid:DXImageTransform.Microsoft.gradient(startColorstr = #50FFFFFF, endColorstr = #50FFFFFF); }
.ie6 .card_mn { height: 56px; }
.ie_all .card .o a { padding: 3px 0 0; }
.ie6 .colorwd, .ie7 .colorwd { background-position: -1px -1px; }
.ie6 a.colorwd, .ie7 a.colorwd { background-position: 0 0; }
.ie_all .imgzoom_exif, .imgzoom_exif_hover { background: #000; }
.ie6 .imgzoom_exif { bottom: 39px; }
/* ------ extracted from widthauto.css ------ */
.ie7.widthauto .ct2 .sd { margin-left: 15px; }
.ie6.widthauto .ct2 .sd { padding-left: 15px; position: relative; }
.ie6.widthauto .ct2 #chart, .ie6.widthauto .ct2 #an { position: relative; }
.ie6.widthauto .ct2_a .appl { position: relative; display: inline; }
.ie6.widthauto .ct3_a .appl, .ie6.widthauto .ct3_a .sd { display: inline; }
.widthauto .switchwidth { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf105;&nbsp;'); }
/* ------ extracted from module.css ------ */
.ie6 .category_l4 { margin-right: -3px; }
.ie6 .el, .ie6 .el li { zoom: 1; }
.ie6 .el .o { position: absolute; margin: 2px 0 0 525px; }
.ie6 .ec a .tn { width: expression(this.width > 90 && this.width >= this.height ? 90 : true); height: expression(this.height > 90 && this.width <= this.height ? 90 : true); }
.ie6 .xlda .el .o { margin: 2px 0 0 460px; }
.ie6 .side_btn, .ie7 .side_btn { display:inline; }
* html .blockcode code { font-family: 'Courier New', serif; }
.str { *padding-bottom: 17px; }
.ie6 .taglist { height: 270px; }
*+html .upf { width: 520px; }
.p_pop .flb { *top: 13px; }
.p_tbl table { *table-layout: fixed; }
*+html .attc { width: 40px; }
.ie8 .attach_preview, .ie9 .attach_preview { top: 57px; right: 18px; }
.imgl { *width: 99.6%; }
.ie6 .imgl img { width: expression(this.width > 110 ? 110 : true); }
.imgdeleted { filter: alpha(opacity=30); }
.opattach_ctrl { filter:progid:DXImageTransform.Microsoft.gradient(startColorstr = #30000000, endColorstr = #30000000); }
.ie6 .exfm .pn, .ie7 .exfm .pn { line-height: 16px; }
.sinf dl dt, .sadd dl dt { padding-top: 3px\9; }
.spmf, .spmf3 { _height: 1%; }
.ie6 .mobile-type, .ie7 .mobile-type { margin-left: 88px; }
.ie6 .mobile-type, .ie6 .mobile-type a { background-image: url({IMGDIR}/mobile-type-ie6.png) !important; }
#threadbeginid .beginidimg { filter:alpha(opacity=0); }
.ie_all .fl_icn { background: url({IMGDIR}/forum.gif) no-repeat left center; }
.ie6 .bdl { position: relative; }
.ie_all .ico_increase, .ie_all .ico_fall { width: 16px; font-size: 18px; margin: 0 5px; }
.ico_increase { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf124;&nbsp;'); }
.ico_fall { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf125;&nbsp;'); }
.ie_all .fa_fav_0:before, .ie_all .fa_fav:before, .ie_all .fa_rss:before, .ie_all .fa_achv:before, .ie_all .fa_bin:before, .ie_all .fa_ivt:before { font-size: 18px; margin-left: 0; margin-right: 4px; }
.ie6 .tl th .tdpre, .ie6 .tl td.fn .tdpre { display: none; }
.ie6 .fromg, tr:hover .fromg { visibility: visible; }
.ie_all .tl .tpin { display: block; width: 18px; height: 18px; }
.ie_all .tl .tpin1 { background: url({IMGDIR}/pin_1.gif); }
.ie_all .tl .tpin2 { background: url({IMGDIR}/pin_2.gif); }
.ie_all .tl .tpin3 { background: url({IMGDIR}/pin_3.gif); }
.ie_all .tl .tpin4 { background: url({IMGDIR}/pin_4.gif); }
.ie6 .tsm, .ie7 .tsm { margin-bottom: 0; }
.ie_all .pattimg_zoom { background-color: #FFF; filter:alpha(opacity=0); opacity:0; }
.ie6 .cgtl { width: auto; }
.ie6 #photo_pic img { width: expression(this.width > 620 ? 620:true); }
.ie6 .photo_pic { width:780px; margin:0 auto; }
.ie6 .authi .none { display:none; }
.ie6 .authi .show { display:inline-block; }
.ie_all #atarget, .ie_all .unchk, .ie_all .chked { display: inline-block; height: 20px; line-height: 20px; }
.ie6 .closeprev { display: none; }
.ie_all #hiddenthread { border-top: none; }
.ie6 .pls dd img, .ie7 .pls dd img { margin-top: 2px; width: expression(this.width > 65 ? 65 : true); }
.ie6 .pcbs table, .ie6 .t_fsz table { width: auto; }
* html .t_fsz { height: 100px; overflow: visible; }
* html .sign { height: expression(signature(this)); }
.ie_all .pob em a:before { font-size: 16px; margin-left: 0; margin-right: 6px; }
.ie6 .pl .quote blockquote, .ie7 .pl .quote blockquote { display: inline }
* html .pl .blockcode ol li { font-family: 'Courier New',serif; }
.ie6 .actl_pop { width: 200px !important; height: expression(this.offsetHeight > 300 ? 300 : 'auto'); }
.rsld cite, .rusld cite { _margin-bottom: -6px; }
.ie6 .vw .t_fsz { height: 30px; }
* html .tradl img { width: expression(this.width > 130 && this.width>=this.height ? 130 : true); height: expression(this.height > 130 && this.width<=this.height ? 130 : true); }
.ie6 .vw .d img { width: expression(this.width > 620 ? 620:true); }
.ie6 .avatar a { float: left; }
.mi .avatar a span { filter:progid:DXImageTransform.Microsoft.gradient(startColorstr = #50FFFFFF, endColorstr = #50FFFFFF); }
* html .pic .c img { width: expression(this.width > 764 ? 764 : true); }
.mlnv img { _width: expression(this.width > 100 && this.width>=this.height ? 100 : true); height: expression(this._height > 100 && this.width<=this.height ? 100 : true); }
.sl .h { _height: 1%; }
.ie6 .favl li { position: relative; }
.ie6 .favl .o { margin: 0; right: 0; top: 12px; }
* html .pll ol { margin-left: 25px; }
*+html .pll ol { margin-left: 25px; }
.rwdl .uslvd em { bottom /*\**/: 9px\9; }
* html .rwdl .uslvd em { bottom: 11px; }
*+html .rwdl .uslvd em { bottom: 9px; }
.ie7 .pm_c .o { margin-top: -2px; }
.ie6 .pm_mn .tedt { width: 587px; }
.pm_b img { height: expression(this.height > 575 ? 575 : true); }
.tdats { *padding-bottom: 10px; }
.ie6 .un_selector input, .ie7 .un_selector input { height: 17px; }
.ie6 .pmfrndl, .ie7 .pmfrndl { margin-top: 4px; }
.note li { *margin-left: 25px; }
.ie6 .appl li { width: 100%; }
.ie6 #scform_submit strong, .ie7 #scform_submit strong { display: inline; }
.ie6 .rnk1 .mlp img { width: expression(this.width > 100 && this.width>=this.height ? 100 : true); height: expression(this.height > 75 && this.width<=this.height ? 75 : true); }
.ie6 .rnk1 .bigpic img { width: expression(this.width > 286 && this.width>=this.height ? 286 : true); height: expression(this.height > 200 && this.width<=this.height ? 200 : true); }
.clct_list .xld .m { background-color: #D8DEEA; }
.ie6 .flw_article .c img { width: expression(this.width > 600 ? 600 : true); }
.ie8 .flw_delete { position: relative; z-index: 1; }
.ie6 .flw_btn_fo, .ie7 .flw_btn_fo { position: relative; z-index: 1; }
.ie8 .flw_autopt .pts, .ie9 .flw_autopt .pts { min-height: 21px; }
.ie6 .tb .a { position: static; }
.ie6 #flw_bar object { position: absolute; }
.ie6 #livereplycontent dl, .ie7 #livereplycontent dl { width: 97%; }
/* ------ extracted from css_space.css ------ */
#space #nv li, #space #home { filter:progid:DXImageTransform.Microsoft.gradient(startColorstr = #70FFFFFF, endColorstr = #70FFFFFF); }
* html #space .pic .c img { width: expression(this.width > 714 ? 714 : true); }
/* ------ extracted from editor.css ------ */
.ie8 .edt .pt { width: 800px; max-width: 100%; min-width: 100%; }
.ie8 .editortoolbar .flbc { position: relative; }
/* ------ extracted from icon.css ------ */
[class^="fico-"], [class*=" fico-"] { *font-family: 'dzicon'; *line-height: 1em; }
.fico-person { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf100;&nbsp;'); }
.fico-search { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf101;&nbsp;'); }
.fico-account_box { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf102;&nbsp;'); }
.fico-account { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf102;&nbsp;'); }
.fico-home { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf103;&nbsp;'); }
.fico-assessment { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf104;&nbsp;'); }
.fico-widthfixed { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf105;&nbsp;'); }
.fico-widthauto { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf106;&nbsp;'); }
.fico-styleselect { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf107;&nbsp;'); }
.fico-add_circle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf108;&nbsp;'); }
.fico-add { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf108;&nbsp;'); }
.fico-remove_circle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf109;&nbsp;'); }
.fico-remove { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf109;&nbsp;'); }
.fico-rss_feed { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf10a;&nbsp;'); }
.fico-rss { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf10a;&nbsp;'); }
.fico-refresh { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf10b;&nbsp;'); }
.fico-delete { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf10c;&nbsp;'); }
.fico-comment { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf10d;&nbsp;'); }
.fico-edit { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf10e;&nbsp;'); }
.fico-push { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf10f;&nbsp;'); }
.fico-thumbup { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf110;&nbsp;'); }
.fico-thumbdown { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf111;&nbsp;'); }
.fico-collection { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf112;&nbsp;'); }
.fico-activitysm { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf113;&nbsp;'); }
.fico-share { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf114;&nbsp;'); }
.fico-check_right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf115;&nbsp;'); }
.fico-error { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf116;&nbsp;'); }
.fico-info { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf117;&nbsp;'); }
.fico-qq { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf118;&nbsp;'); }
.fico-email { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf119;&nbsp;'); }
.fico-task { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf11a;&nbsp;'); }
.fico-scrolltop { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf11b;&nbsp;'); }
.fico-replyfast { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf11c;&nbsp;'); }
.fico-list { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf11d;&nbsp;'); }
.fico-follow { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf11e;&nbsp;'); }
.fico-friendadd { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf11f;&nbsp;'); }
.fico-mypost { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf120;&nbsp;'); }
.fico-interactive { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf121;&nbsp;'); }
.fico-settings { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf122;&nbsp;'); }
.fico-link { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf123;&nbsp;'); }
.fico-up { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf124;&nbsp;'); }
.fico-down { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf125;&nbsp;'); }
.fico-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf126;&nbsp;'); }
.fico-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf127;&nbsp;'); }
.fico-valid { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf128;&nbsp;'); }
.fico-invalid { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf129;&nbsp;'); }
.fico-stars { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf12a;&nbsp;'); }
.fico-sun { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf12b;&nbsp;'); }
.fico-star3 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf12b;&nbsp;'); }
.fico-moon { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf12c;&nbsp;'); }
.fico-star2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf12c;&nbsp;'); }
.fico-star { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf12d;&nbsp;'); }
.fico-star1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf12d;&nbsp;'); }
.fico-checkbox { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf12e;&nbsp;'); }
.fico-checked { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf12f;&nbsp;'); }
.fico-doing { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf130;&nbsp;'); }
.fico-volume { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf131;&nbsp;'); }
.fico-image { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf132;&nbsp;'); }
.fico-attachment { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf133;&nbsp;'); }
.fico-thread { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf134;&nbsp;'); }
.fico-clock { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf135;&nbsp;'); }
.fico-lock { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf136;&nbsp;'); }
.fico-print { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf137;&nbsp;'); }
.fico-help { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf138;&nbsp;'); }
.fico-launch { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf139;&nbsp;'); }
.fico-imgadjust { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf13a;&nbsp;'); }
.fico-vote { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf13b;&nbsp;'); }
.fico-reward { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf13c;&nbsp;'); }
.fico-vs { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf13d;&nbsp;'); }
.fico-group { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf13e;&nbsp;'); }
.fico-cart { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf13f;&nbsp;'); }
.fico-headset { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf140;&nbsp;'); }
.fico-phone { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf141;&nbsp;'); }
.fico-place { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf142;&nbsp;'); }
.fico-camera { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf143;&nbsp;'); }
.fico-voice { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf144;&nbsp;'); }
.fico-bell { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf145;&nbsp;'); }
.fico-poke { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf146;&nbsp;'); }
.fico-profile { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf147;&nbsp;'); }
.fico-dropdown { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf148;&nbsp;'); }
.fico-ban { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf149;&nbsp;'); }
.fico-tag { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xf14a;&nbsp;'); }
