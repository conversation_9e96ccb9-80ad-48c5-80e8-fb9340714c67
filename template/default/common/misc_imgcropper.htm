<!--{if $_GET['op'] == 'loadcropper'}-->
<!DOCTYPE html>
<html>
	<head>
		<meta charset="{CHARSET}" />
		<meta name="renderer" content="webkit" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<title><!--{if !empty($navtitle)}-->$navtitle - <!--{/if}--><!--{if empty($nobbname)}--> $_G['setting']['bbname'] - <!--{/if}--> Powered by Discuz!</title>

		<meta name="copyright" content="{lang copyright_s}" />
		<meta name="MSSmartTagsPreventParsing" content="True" />
		<meta http-equiv="MSThemeCompatible" content="Yes" />
		<script type="text/javascript">var STYLEID = '{STYLEID}', STATICURL = '{STATICURL}', IMGDIR = '{IMGDI<PERSON>}', VERHASH = '{VERHASH}', charset = '{CHARSET}', discuz_uid = '$_G[uid]', cookiepre = '{$_G[config][cookie][cookiepre]}', cookiedomain = '{$_G[config][cookie][cookiedomain]}', cookiepath = '{$_G[config][cookie][cookiepath]}', showusercard = '{$_G[setting][showusercard]}', attackevasive = '{$_G[config][security][attackevasive]}', disallowfloat = '{$_G[setting][disallowfloat]}', creditnotice = '<!--{if $_G['setting']['creditnotice']}-->$_G['setting']['creditnames']<!--{/if}-->', defaultstyle = '$_G[style][defaultextstyle]', REPORTURL = '$_G[currenturl_encode]', SITEURL = '$_G[siteurl]', JSPATH = '$_G[setting][jspath]';</script>
		<script type="text/javascript" src="{$_G[setting][jspath]}common.js?{VERHASH}"></script>
		<script type="text/javascript" src="{$_G[setting][jspath]}imgcropper.js?{VERHASH}"></script>

		<style type="text/css">
			body{margin: 0; padding: 0}
			#rightDown{
				position:absolute;
				background:#FFF url({IMGDIR}/r_b_resize.png) no-repeat 0 0;
				width: 16px;
				height: 16px;
				z-index:500;
				font-size:0;
				opacity: 0.8;
				filter:alpha(opacity=80);
				cursor:nw-resize;
				right:0;
				bottom:0;
			}
			#bgDiv{width:{$cbgboxwidth}px; height:{$cbgboxheight}px; border:1px solid #666666; position:relative;}
			#dragDiv{border:1px dashed #00f; width:{$cboxwidth}px; height:{$cboxheight}px; top:{$dragpt}px; left:{$dragpl}px; cursor:move; }
		</style>
	</head>
	<body>
		<div id="bgDiv" style="width:{$cbgboxwidth}px; height:{$cbgboxheight}px;">
			<div id="dragDiv" style="top:{$dragpt}px; left:{$dragpl}px;">
			</div>
			<div id="rightDown"></div>
		</div>
		<script type="text/javascript">

			var cropper = new ImgCropper("bgDiv", "dragDiv", "$_GET[img]", {
				width: $cbgboxwidth, height: $cbgboxheight, color: "#000", min:true, minWidth:$cboxwidth, minHeight:{$cboxheight},
				resize: true, rightDown: "rightDown"
			});
		</script>
	</body>
</html>

<!--{else}-->
	<!--{template common/header}-->
	<h3 class="flb">
		<em id="return_$_GET[handlekey]">{lang imgcropper}</em>
		<!--{if $_G[inajax]}--><span><a href="javascript:;" onclick="hideWindow('$_GET[handlekey]');" class="flbc" title="{lang close}">{lang close}</a></span><!--{/if}-->
	</h3>
	<div id="__groupnameform_">

		<form method="post" autocomplete="off" id="groupnameform_" name="groupnameform_" action="misc.php?mod=imgcropper" {if $_G[inajax]}onsubmit="ajaxpost(this.id, 'return_$_GET[handlekey]');"{/if}>
			<input type="hidden" name="referer" value="{echo dreferer()}">
			<input type="hidden" name="imgcroppersubmit" value="true" />
			<!--{if $_G[inajax]}--><input type="hidden" name="handlekey" value="$_GET[handlekey]" /><!--{/if}-->
			<input type="hidden" name="formhash" value="{FORMHASH}" />
			<input type="hidden" name="cutleft" id="cutleft" value="0" />
			<input type="hidden" name="cuttop" id="cuttop" value="0" />
			<input type="hidden" name="cutwidth" id="cutwidth" value="0" />
			<input type="hidden" name="cutheight" id="cutheight" value="0" />
			<input type="hidden" name="picwidth" id="picwidth" value="0" />
			<input type="hidden" name="picheight" id="picheight" value="0" />
			<input type="hidden" name="cutimg" value="$_GET['img']" />
			<!--{if $_GET['ictype'] == 'block'}-->
			<input type="hidden" name="bid" value="$_GET[bid]" />
			<input type="hidden" name="picflag" value="$_GET[picflag]" />
			<!--{/if}-->
			<input type="hidden" name="ictype" value="$_GET[ictype]" />
			<div class="c">
				<iframe src="misc.php?mod=imgcropper&op=loadcropper&img={$prefix}{$_GET['img']}&width=$_GET['width']&height=$_GET['height']" id="cropperiframe" frameborder="0" scrolling="no" marginwidth="0" onload="setWinHeight(this)"></iframe>
			</div>
			<p class="o pns">
				<button type="submit" name="groupnamesubmit_btn" value="true" class="pn pnc"><strong>{lang imgcropper_crop}</strong></button>
			</p>
		</form>
		<script type="text/javascript">
			function setWinHeight(obj){
				var win = obj;
				if (document.getElementById) {
					if (win && !window.opera) {
						if (win.contentDocument && win.contentDocument.documentElement.scrollHeight) {
							win.height =  win.contentDocument.documentElement.scrollHeight;
							win.width =  win.contentDocument.documentElement.scrollWidth;
						} else if(win.Document && win.Document.body.scrollHeight) {
							win.height = win.Document.body.scrollHeight;
							win.width = win.Document.body.scrollWidth;
						}
					}
				}
			}
			function resetHeight(divObj, ipos, imgObj) {
				var iframeObject = $('cropperiframe');
				iframeObject.height =  divObj.style.height;
				iframeObject.width =  divObj.style.width;
				$('cutleft').value = ipos.Left;
				$('cuttop').value = ipos.Top;
				$('cutwidth').value = ipos.Width-2;
				$('cutheight').value = ipos.Height-2;
				$('picwidth').value = imgObj.width;
				$('picheight').value = imgObj.height;
			}
			function succeedhandle_$_GET[handlekey](url, msg, values) {
				<!--{if $_GET['ictype'] == 'block'}-->
				$('icflag').value = 1;
				<!--{/if}-->
			}
		</script>
	</div>
	<!--{template common/footer}-->
<!--{/if}-->