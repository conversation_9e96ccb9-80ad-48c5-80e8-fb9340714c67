/*
wysiwyg CSS file for Discuz! X
(C) Comsenz Inc.
https://www.discuz.vip
*/
* { margin: 0; padding: 0; word-wrap: break-word; }
html, body { border: 0 !important; }
body { background: #FFF; font: {FONTSIZE} {FONT}; font-size: {MSGFONTSIZE}; color: {TABLETEXT}; height: 400px; }
li { list-style-type: disc; margin-left: 2em; }
ol { *margin: 0; padding: 0; }
.litype_1 li, ol li { list-style-type: decimal; }
.litype_2 li { list-style-type: lower-alpha; }
.litype_3 li { list-style-type: upper-alpha; }
a { color: {HIGHLIGHTLINK}; }
	a:hover { text-decoration: none; }
	a img { border: none; }
hr.l { height: 1px; border: none; background: {COMMONBORDER}; color: {COMMONBORDER}; }
table { empty-cells: show; border-collapse: collapse; }
table td { padding: 4px; border: 1px solid {COMMONBORDER}; }
th { font-weight: 400; }
div.quote, div.blockcode { margin: 10px 0; padding: 10px 10px 10px 65px; }
div.quote { padding-bottom: 5px; background: #F9F9F9 url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='28' height='27'%3e%3cpath fill='%23d8e7f2' d='M11 16v10H1V16C1 10 7 1 11 1v5c-2 0-5 6-5 10h5zm16 0v10H17V16c0-6 6-15 10-15v5c-2 0-5 6-5 10h5z'/%3e%3c/svg%3e") no-repeat 20px 6px; }
	div.quote blockquote { margin: 0; padding: 0 65px 5px 0; background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='28' height='27'%3e%3cpath fill='%23d8e7f2' d='M17 11V1h10v10c0 6-6 15-10 15v-5c2 0 5-6 5-10h-5zM1 11V1h10v10c0 6-6 15-10 15v-5c2 0 5-6 5-10H1z'/%3e%3c/svg%3e") no-repeat 100% 100%; line-height: 1.6em; }
div.blockcode { border: 1px solid #CCC; background: #F7F7F7 url({IMGDIR}/codebg.gif) repeat-y 0 0; }
	div.blockcode blockquote { margin: 0; }
.float td { border: 0; padding: 0; }
img { max-width:400px; }