<!--{template common/header_ajax}-->
	<script type="text/javascript">
		if(!isUndefined(checkForumnew_handle)) {
			clearTimeout(checkForumnew_handle);
		}
		<!--{if $threadlist}-->
			if($('separatorline')) {
				var table = $('separatorline').parentNode;
			} else {
				var table = $('forum_' + $fid);
			}
			var newthread = [];
			<!--{eval $i = 0;}-->
			<!--{loop $threadlist $thread}-->
				{block icon}<a href="forum.php?mod=viewthread&tid=$thread[icontid]&{if $_GET['archiveid']}archiveid={$_GET['archiveid']}&{/if}extra=$extra" title="{if $thread['displayorder'] == 1}{lang thread_type1} - {/if}{if $thread['displayorder'] == 2}{lang thread_type2} - {/if}{if $thread['displayorder'] == 3}{lang thread_type3} - {/if}{if $thread['displayorder'] == 4}{lang thread_type4} - {/if}{if $thread[folder] == 'lock'}{lang closed_thread} - {/if}{if $thread['special'] == 1}{lang thread_poll} - {/if}{if $thread['special'] == 2}{lang thread_trade} - {/if}{if $thread['special'] == 3}{lang thread_reward} - {/if}{if $thread['special'] == 4}{lang thread_activity} - {/if}{if $thread['special'] == 5}{lang thread_debate} - {/if}{if $thread[folder] == "new"}{lang have_newreplies} - {/if}{lang target_blank}" target="_blank"><!--{if $thread[folder] == 'lock'}--><i class="fico-lock fic6 fc-s"></i><!--{elseif $thread['special'] == 1}--><i class="fico-vote fic6 fc-l"></i><!--{elseif $thread['special'] == 2}--><i class="fico-cart fic6 fc-l"></i><!--{elseif $thread['special'] == 3}--><i class="fico-reward fic6 fc-l"></i><!--{elseif $thread['special'] == 4}--><i class="fico-group fic6 fc-l"></i><!--{elseif $thread['special'] == 5}--><i class="fico-vs fic6 fc-l"></i><!--{elseif in_array($thread['displayorder'], array(1, 2, 3, 4))}--><i class="tpin tpin{$thread[displayorder]}"><svg width="18" height="18"><path d="M9 0l9 9H14v9H4V9H0z"></path></svg></i><!--{else}--><i class="fico-thread fic6 fc-l"></i><!--{/if}--></a>{/block}
				newthread[{$i}] = {'tid':$thread[tid], 'thread': {'icn':{'className':'icn','val':'$icon'}<!--{if $_G['forum']['ismoderator']}-->, 'o':{'className':'o','val':'<input type="checkbox" value="{$thread[tid]}" name="moderate[]" onclick="tmodclick(this)">'}<!--{/if}--> ,'common':{'className':'new fn','val':'{if !$_G['setting']['forumdisplaythreadpreview']}<a class="tdpre y" href="javascript:void(0);" onclick="javascript:previewThread(\'$thread[tid]\', \'$thread[id]\');">{lang preview}</a>{/if}$thread[threadurl]'}, 'author':{'className':'by','val':'<cite>$thread[authorurl]</cite><em><span>{$thread[dateline]}</span></em>'}, 'num':{'className':'num','val':'<a href="forum.php?mod=viewthread&tid={$thread[tid]}">{$thread[replies]}</a><em>{$thread[views]}</em>'}, 'lastpost':{'className':'by','val':'<cite>$thread[lastposterurl]</cite><em><a href="forum.php?mod=redirect&tid=$thread[tid]&goto=lastpost#lastpost">{$thread[lastpost]}</a></em>'}}};
				<!--{eval $i++;}-->
			<!--{/loop}-->
			removetbodyrow(table, 'forumnewshow');
			for(var i = 0; i < newthread.length; i++) {
				if(newthread[i].tid) {
					addtbodyrow(table, ['tbody', 'newthread'], ['normalthread_', 'normalthread_'], 'separatorline', newthread[i]);
				}
			}
			function readthread(tid) {
				if($("checknewthread_"+tid)) {
					$("checknewthread_"+tid).className = "";
				}
			}
		<!--{elseif !$threadlist && $_GET['uncheck'] == 2}-->
			showDialog('{lang none_newthread}', 'notice', null, null, 0, null, null, null, null, 3);
		<!--{/if}-->
		checkForumnew_handle = setTimeout(function () {checkForumnew('{$fid}', '{$_G[timestamp]}');}, checkForumtimeout);
	</script>
<!--{template common/footer_ajax}-->