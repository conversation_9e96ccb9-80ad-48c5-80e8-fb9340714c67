<!--{template common/header}-->
<div id="pt" class="bm cl">
	<div class="z">
		<a href="./" class="nvhm" title="{lang homepage}">$_G[setting][bbname]</a> <em>&rsaquo;</em>
		<a href="forum.php?mod=collection">{lang collection}</a> <em>&rsaquo;</em>
		<a href="forum.php?mod=collection&action=view&ctid=$ctid">{$_G['collection']['name']}</a> <em>&rsaquo;</em>
		{lang collection_followlist}
	</div>
</div>
<script type="text/javascript" src="{$_G[setting][jspath]}home_friendselector.js?{VERHASH}"></script>
<script type="text/javascript">
	var fs;
	var clearlist = 0;
</script>

<div id="ct" class="wp cl">
	<div class="bm bml pbn">
		<div class="bm_h cl">
			<a href="forum.php?mod=collection&action=view&ctid={$_G['collection']['ctid']}" class="y pn"><span class="xi2">&laquo; {lang collection_backcollection}</span></a>
			<h1 class="xs2">
				<a href="forum.php?mod=collection&action=view&ctid={$_G['collection']['ctid']}">{$_G['collection']['name']}</a>
			</h1>
		</div>
		<div class="bm_c">
			<div title="$avgrate" class="pbn xg1 cl">
			<!--{if $_G['collection']['ratenum'] > 0}-->
				<span class="clct_ratestar"><span class="star star$star">&nbsp;</span></span>
				 &nbsp;{lang collection_totalrates}
			<!--{else}-->
				{lang collection_norate}
			<!--{/if}-->
			</div>
			<div>{$_G['collection']['desc']}</div>
		</div>
	</div>
	
	<div class="bm">
		<div class="tb tb_h cl">
			<ul>
				<li><a href="forum.php?mod=collection&action=view&op=comment&ctid={$_G['collection']['ctid']}">{lang collection_commentlist}</a></li>
				<li class="a"><a href="forum.php?mod=collection&action=view&op=followers&ctid={$_G['collection']['ctid']}">{lang collection_followlist}</a></li>
			</ul>
		</div>
		<!--{if $followers}-->
			<div class="bm_c">
				<ul class="ml mls cl">
				<!--{loop $followers $follower}-->
					<li>
						<a href="home.php?mod=space&uid=$follower[uid]" class="avt"><!--{avatar($follower['uid'], 'small')}--></a>
						<p><a href="home.php?mod=space&uid=$follower[uid]">$follower[username]</a></p>
					</li>
				<!--{/loop}-->
				</ul>
			</div>
		<!--{else}-->
			<p class="emp">{lang no_content}</p>
		<!--{/if}-->
		<!--{if $multipage}--><div class="bm_c cl">$multipage</div><!--{/if}-->
	</div>
</div>
<!--{template common/footer}-->