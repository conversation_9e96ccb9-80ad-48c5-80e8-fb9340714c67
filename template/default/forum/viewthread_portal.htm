{eval
	$specialarr = array(0 => array('thread', '{lang index_posts}'), 1 => array('poll', '{lang thread_poll}'), 2 => array('trade', '{lang thread_trade}'), 3 => array('reward', '{lang thread_reward}'), 4 => array('activity', '{lang thread_activity}'), 5 => array('debate', '{lang thread_debate}'));
	$specialtype = $specialarr[$_G['forum_thread']['special']];
	$_G['home_tpl_titles'][] = $navsubject;
	$_G['home_tpl_titles'][] = $specialtype[1];
	$_G['home_tpl_titles'][] = '{lang portal}';
}

<!--{template common/header}-->

<script type="text/javascript">var fid = parseInt('$_G[fid]'), tid = parseInt('$_G[tid]');</script>
<!--{if $_G['forum']['ismoderator']}-->
	<script type="text/javascript" src="{$_G['setting']['jspath']}forum_moderate.js?{VERHASH}"></script>
<!--{/if}-->
<form method="post" autocomplete="off" name="modactions" id="modactions">
<input type="hidden" name="formhash" value="{FORMHASH}" />
<input type="hidden" name="optgroup" />
<input type="hidden" name="operation" />
<input type="hidden" name="listextra" value="$_GET[extra]" />
</form>

<script type="text/javascript" src="{$_G['setting']['jspath']}forum_viewthread.js?{VERHASH}"></script>
<script type="text/javascript">zoomstatus = parseInt($_G['setting']['zoomstatus']);var imagemaxwidth = '{$_G['setting']['imagemaxwidth']}';var aimgcount = new Array();</script>

<div id="pt" class="bm cl">
	<div class="z">
		<a href="./" class="nvhm" title="{lang homepage}">$_G[setting][bbname]</a> $navigation <em>&rsaquo;</em> {lang user_threads}
	</div>
</div>

<style id="diy_style" type="text/css"></style>
<div class="wp">
	<!--[diy=diy1]--><div id="diy1" class="area"></div><!--[/diy]-->
</div>

<div id="ct" class="ct2 wp cl">
	<div class="mn">
		<div class="bm vw pl">
			<div class="h hm">
				<h1 class="ph">$_G[forum_thread][subject]</h1>
				<p class="xg1">
					{lang posted_by}:
					<!--{if $_G[forum_thread][author] && $_G[forum_thread][authorid]}-->
					<a href="home.php?mod=space&uid=$_G[forum_thread][authorid]">$_G[forum_thread][author]</a>
					<!--{else}-->
						<!--{if $_G['forum']['ismoderator']}-->
							<a href="home.php?mod=space&uid=$_G[forum_thread][authorid]">{$_G['setting']['anonymoustext']}</a>
						<!--{else}-->
							{$_G['setting']['anonymoustext']}
						<!--{/if}-->
					<!--{/if}-->
					<span class="pipe">|</span>
					{lang dateline}: <!--{date($_G['forum_thread']['dateline'])}--><span class="pipe">|</span>
					{lang home_view_num}: $_G[forum_thread][views]<span class="pipe">|</span>
					{lang comment_num}: $_G[forum_thread][replies]
					<!--{if (($_G['forum']['ismoderator'] && $_G['group']['alloweditpost'] && (!in_array($post['adminid'], array(1, 2, 3)) || $_G['adminid'] <= $post['adminid'])) || ($_G['forum']['alloweditpost'] && $_G['uid'] && ($post['authorid'] == $_G['uid'] && $_G['forum_thread']['closed'] == 0) && !(!$alloweditpost_status && $edittimelimit && TIMESTAMP - $post['dbdateline'] > $edittimelimit)))}-->
					<span class="pipe">|</span><a href="forum.php?mod=post&action=edit&fid=$_G[fid]&tid=$_G[tid]&pid=$post[pid]{if !empty($_GET[modthreadkey])}&modthreadkey=$_GET[modthreadkey]{/if}&page=$page{if $_GET[from]}&from=$_GET[from]{/if}"><!--{if $_G['forum_thread']['special'] == 2 && !$post['message']}-->{lang post_add_aboutcounter}<!--{else}-->{lang edit}<!--{/if}--></a>
					<!--{/if}-->
					<span class="pipe">|</span><a href="forum.php?mod=viewthread&tid=$_G[tid]" class="xg1">{lang thread_mod}</a>
				</p>
			</div>
			<!--{if empty($_GET['page']) || $_GET['page'] < 2}-->
			<div class="d">
				<table cellpadding="0" cellspacing="0" width="100%">
					<tr>
						<td>
							<!--{subtemplate forum/viewthread_node_body}-->

							<!--{if !IS_ROBOT && $post['first'] && !$_G['forum_thread']['archiveid']}-->
								<!--{if !empty($lastmod['modaction'])}--><div class="modact xs1"><a href="forum.php?mod=misc&action=viewthreadmod&tid=$_G[tid]" title="{lang thread_mod}" onclick="showWindow('viewthreadmod', this.href)">{lang thread_mod_by}</a></div><!--{/if}-->
								<!--{if $post['invisible'] == 0}-->
									<div id="p_btn" class="mtw mbm cl xs1">
										<!--{hook/viewthread_useraction_prefix}-->
										<!--{if helper_access::check_module('share')}-->
											<a href="home.php?mod=spacecp&ac=share&type=thread&id=$_G[tid]" id="k_share" onclick="showWindow(this.id, this.href, 'get', 0);" onmouseover="this.title = $('sharenumber').innerHTML + ' {lang activity_member_unit}{lang thread_share}'"><i><img src="{IMGDIR}/oshr.png" alt="{lang thread_share}" />{lang thread_share}<span id="sharenumber">{$_G['forum_thread']['sharetimes']}</span></i></a>
										<!--{/if}-->
										<!--{if helper_access::check_module('favorite')}-->
											<a href="home.php?mod=spacecp&ac=favorite&type=thread&id=$_G[tid]" id="k_favorite" onclick="showWindow(this.id, this.href, 'get', 0);" onmouseover="this.title = $('favoritenumber').innerHTML + ' {lang activity_member_unit}{lang thread_favorite}'"><i><img src="{IMGDIR}/fav.gif" alt="{lang thread_favorite}" />{lang thread_favorite}<span id="favoritenumber">{$_G['forum_thread']['favtimes']}</span></i></a>
										<!--{/if}-->
										<!--{if ($_G['group']['allowrecommend'] || !$_G['uid']) && $_G['setting']['recommendthread']['status']}-->
											<!--{if !empty($_G['setting']['recommendthread']['addtext'])}-->
											<a id="recommend_add" href="forum.php?mod=misc&action=recommend&do=add&tid=$_G[tid]&hash={FORMHASH}" {if $_G['uid']}onclick="ajaxmenu(this, 3000, 1, 0, '43', 'recommendupdate({$_G['group']['allowrecommend']})');return false;"{else} onclick="showWindow('login', this.href)"{/if} onmouseover="this.title = $('recommendv_add').innerHTML + ' {lang activity_member_unit}$_G[setting][recommendthread][addtext]'"><i><img src="{IMGDIR}/rec_add.gif" alt="$_G['setting']['recommendthread'][addtext]" />$_G['setting']['recommendthread'][addtext]<span id="recommendv_add">$_G[forum_thread][recommend_add]</span></i></a>
											<!--{/if}-->
											<!--{if !empty($_G['setting']['recommendthread']['subtracttext'])}-->
											<a id="recommend_subtract" href="forum.php?mod=misc&action=recommend&do=subtract&tid=$_G[tid]&hash={FORMHASH}" {if $_G['uid']}onclick="ajaxmenu(this, 3000, 1, 0, '43', 'recommendupdate(-{$_G['group']['allowrecommend']})');return false;"{else} onclick="showWindow('login', this.href)"{/if} onmouseover="this.title = $('recommendv_subtract').innerHTML + ' {lang activity_member_unit}$_G[setting][recommendthread][subtracttext]'"><i><img src="{IMGDIR}/rec_subtract.gif" alt="$_G['setting']['recommendthread'][subtracttext]" />$_G['setting']['recommendthread'][subtracttext]<span id="recommendv_subtract">$_G[forum_thread][recommend_sub]</span></i></a>
											<!--{/if}-->
										<!--{/if}-->
										<!--{if $_G['group']['raterange'] && $post['authorid']}-->
											<a href="javascript:;" id="ak_rate" onclick="showWindow('rate', 'forum.php?mod=misc&action=rate&tid=$_G[tid]&pid=$post[pid]{if $_GET[from]}&from=$_GET[from]{/if}');return false;" title="{echo count($postlist[$post[pid]][totalrate]);} {lang people_score}"><i><img src="{IMGDIR}/agree.gif" alt="{lang rate}" />{lang rate}</i></a>
										<!--{/if}-->
										<!--{if $post['first'] && $_G[uid] && $_G[uid] == $post[authorid] && helper_access::check_module('friend')}-->
											<a href="misc.php?mod=invite&action=thread&id=$_G[tid]" onclick="showWindow('invite', this.href, 'get', 0);"><i><img src="{IMGDIR}/activitysmall.gif" alt="{lang invite}" />{lang invite}</i></a>
										<!--{/if}-->
										<!--{hook/viewthread_useraction}-->
									</div>
								<!--{/if}-->
							<!--{/if}-->
						</td>
					</tr>
				</table>
			</div>
			<!--{/if}-->
			<!--{if !empty($aimgs[$post[pid]])}-->
			<script type="text/javascript" reload="1">
				aimgcount[{$post[pid]}] = [<!--{echo dimplode($aimgs[$post[pid]]);}-->];
				attachimggroup($post['pid']);
				<!--{if empty($_G['setting']['lazyload'])}-->
					<!--{if !$post['imagelistthumb']}-->
						attachimgshow($post[pid]);
					<!--{else}-->
						attachimgshow($post[pid], 1);
					<!--{/if}-->
				<!--{/if}-->
				<!--{if $post['imagelistthumb']}-->
					attachimglstshow($post['pid'], <!--{echo intval($_G['setting']['lazyload'])}-->, 0, '{$_G[setting][showexif]}');
				<!--{/if}-->
			</script>
			<!--{/if}-->
		</div>

		<div class="bm vw pl" id="comment">
			<div class="bm_h cl">
				<h2>{lang latest_comments}</h2>
			</div>
			<div class="bm_c">
			<!--{loop $postlist $postid $post}-->
				<!--{if $postid && !$post['first']}-->
				<div id="post_$post[pid]" class="xld xlda mbm">
					<!--{subtemplate forum/viewthread_from_node}-->
				</div>
				<!--{/if}-->
			<!--{/loop}-->
			<div id="postlistreply" class="xld xlda mbm"><div id="post_new" class="viewthread_table" style="display: none"></div></div>
			<!--{if $multipage}-->
			<div class="pgs cl">$multipage</div>
			<!--{/if}-->
			</div>

			<!--{if $_G['setting']['fastpost'] && $allowpostreply && !$_G['forum_thread']['archiveid']}-->
			<div class="bm_c">
				<!--{subtemplate forum/viewthread_fastpost}-->
			</div>
			<!--{/if}-->
		</div>

	</div>
	<div class="sd">
		<!--{if $_G['setting']['visitedforums']}-->
			<div class="bm">
				<div class="bm_h cl">
					<h2>{lang viewed_forums}</h2>
				</div>
				<div class="bm_c xl xl2 cl">
					<ul>
						$_G['setting']['visitedforums']
					</ul>
				</div>
			</div>
		<!--{/if}-->
		<!--{if $oldthreads}-->
			<div class="bm">
				<div class="bm_h cl">
					<h2>{lang viewd_threads}</h2>
				</div>
				<div class="bm_c xl xl1">
					<ul>
					<!--{loop $oldthreads $oldtid $oldsubject}-->
						<li><a href="forum.php?mod=viewthread&tid=$oldtid&from=portal">$oldsubject</a></li>
					<!--{/loop}-->
					</ul>
				</div>
			</div>
		<!--{/if}-->

		<!--{hook/viewthread_side_bottom}-->
		<div class="drag">
			<!--[diy=diy2]--><div id="diy2" class="area"></div><!--[/diy]-->
		</div>
	</div>
</div>

<!--{if !IS_ROBOT && !empty($_G[setting][lazyload])}-->
	<script type="text/javascript">
	new lazyload();
	</script>
<!--{/if}-->

<div class="wp mtn">
	<!--[diy=diy3]--><div id="diy3" class="area"></div><!--[/diy]-->
</div>

<!--{template common/footer}-->