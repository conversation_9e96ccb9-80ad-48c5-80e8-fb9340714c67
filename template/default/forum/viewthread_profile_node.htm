<!--{eval}-->
<!--
function profile_node_numbercard($post, $s, $e) {
	$return = '';
	foreach($post['numbercard'] as $numbercardkey => $numbercard) $return .= ($numbercardkey == 2 ? '<td>' : '<th>').'<p><a href="'.$numbercard['link'].'" class="xi2">'.dnumber($numbercard['value']).'</a></p>'.$numbercard['lang'].($numbercardkey == 2 ? '</td>' : '</th>');
-->
<!--{/eval}-->
<!--{block return}-->$s<div class="tns xg2"><table cellspacing="0" cellpadding="0">$return</table></div>$e<!--{/block}-->
<!--{eval}-->
<!--
	return $return;
}

function profile_node_groupicon($post, $s, $e) {
-->
<!--{/eval}-->
<!--{block return}--><!--{if $post[groupicon]}-->$s<a href="home.php?mod=spacecp&ac=usergroup&gid=$post[groupid]" target="_blank">$post[groupicon]</a>$e<!--{/if}--><!--{/block}-->
<!--{eval}-->
<!--
	return $return;
}

function profile_node_authortitle($post, $s, $e) {
-->
<!--{/eval}-->
<!--{block return}-->$s<a href="home.php?mod=spacecp&ac=usergroup&gid=$post[groupid]" target="_blank">{$post[authortitle]}</a>$e<!--{/block}-->
<!--{eval}-->
<!--
	return $return;
}

function profile_node_customstatus($post, $s, $e) {
-->
<!--{/eval}-->
<!--{block return}--><!--{if $post['customstatus']}-->$s$post[customstatus]$e<!--{/if}--><!--{/block}-->
<!--{eval}-->
<!--
	return $return;
}

function profile_node_medal($post, $s, $e) {
	if(!$post['medals']) return;
	foreach($post['medals'] as $medal) {
		$return .= '<img id="md_'.$post['pid'].'_'.$medal['medalid'].'" src="'.$medal['image'].'" alt="'.$medal['name'].'" title="" onmouseover="showMenu({\'ctrlid\':this.id, \'menuid\':\'md_'.$medal['medalid'].'_menu\', \'pos\':\'12!\'})" />';
	}
-->
<!--{/eval}-->
<!--{block return}-->$s<a href="home.php?mod=medal">$return</a>$e<!--{/block}-->
<!--{eval}-->
<!--
	return $return;
}

function profile_node_star($post, $s, $e, $upgrademenu = 1) {
	$stars = showstars($post['stars']);
	$menu = (isset($post['upgradecredit']) && $post['upgradecredit'] !== false) ? profile_node_upgrade_menu($post) : '';
-->
<!--{/eval}-->
<!--{block return}-->
	$s<span{if isset($post['upgradecredit']) && $post['upgradecredit'] !== false && $upgrademenu} id="g_up$post[pid]" onmouseover="showMenu({'ctrlid':this.id, 'pos':'12!'});"{/if}>$stars</span>$e
	<!--{if $upgrademenu}-->$menu<!--{/if}-->
<!--{/block}-->
<!--{eval}-->
<!--
	return $return;
}

function profile_node_upgradeprogress($post, $s, $e, $upgrademenu = 1) {
	if($post['upgradecredit'] !== false) {
		$menu = profile_node_upgrade_menu($post);
-->
<!--{/eval}-->
<!--{block return}-->
	$s<span class="pbg2" {if $upgrademenu} id="upgradeprogress_$post[pid]" onmouseover="showMenu({'ctrlid':this.id, 'pos':'12!', 'menuid':'g_up$post[pid]_menu'});"{/if}><span class="pbr2" style="width:$post['upgradeprogress']%;"></span></span>$e
	<!--{if $upgrademenu}-->$menu<!--{/if}-->
<!--{/block}-->
<!--{eval}-->
<!--
		return $return;
	}
}

function profile_node_upgrade_menu($post) {
	global $_G;
-->
<!--{/eval}-->
<!--{block return}--><div id="g_up$post[pid]_menu" class="tip tip_4" style="display: none;"><div class="tip_horn"></div><div class="tip_c">$post['authortitle'], {lang credits} $post['credits'], {lang thread_groupupgrade} $post['upgradecredit'] {lang credits}</div></div><!--{/block}-->
<!--{eval}-->
<!--
	return $return;
}

function profile_node_baseinfo($post, $s, $e, $extra) {
	$str = viewthread_baseinfo($post, $extra);
	return $str !== '' ? $s.$str.$e : '';
}
-->
<!--{/eval}-->


