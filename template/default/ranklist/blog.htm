<!--{template common/header}-->
<div id="pt" class="bm cl">
	<div class="z">
		<a href="./" class="nvhm" title="{lang homepage}">$_G[setting][bbname]</a> <em>&rsaquo;</em>
		<a href="misc.php?mod=ranklist">{lang ranklist}</a> <em>&rsaquo;</em>
		{lang blog_ranklist}
	</div>
</div>

<style id="diy_style" type="text/css"></style>

<!--[diy=diyranklisttop]--><div id="diyranklisttop" class="area"></div><!--[/diy]-->

<div class="ct2_a wp cl">
	<div class="mn">
		<!--[diy=diycontenttop]--><div id="diycontenttop" class="area"></div><!--[/diy]-->
		<div class="bm bw0">
			<h1 class="mt">{lang blog_ranklist}</h1>
			<ul class="tb cl">
				<li{if $_GET[view] == 'heats'} class="a"{/if}><a href="misc.php?mod=ranklist&type=blog&view=heats&orderby=$orderby">{lang hot_ranklist}</a></li>
				<li{if $_GET[view] == 'replies'} class="a"{/if}><a href="misc.php?mod=ranklist&type=blog&view=replies&orderby=$orderby">{lang comment_ranklist}</a></li>
				<li{if $_GET[view] == 'views'} class="a"{/if}><a href="misc.php?mod=ranklist&type=blog&view=views&orderby=$orderby">{lang visit_ranklist}</a></li>
				<li{if $_GET[view] == 'sharetimes'} class="a"{/if}><a href="misc.php?mod=ranklist&type=blog&view=sharetimes&orderby=$orderby">{lang ranklist_share}</a></li>
				<li{if $_GET[view] == 'favtimes'} class="a"{/if}><a href="misc.php?mod=ranklist&type=blog&view=favtimes&orderby=$orderby">{lang ranklist_favorite}</a></li>
				<!--{if $clicks}-->
				<!--{loop $clicks $key $value}-->
				<li{if $_GET[view] == $key} class="a"{/if}><a href="misc.php?mod=ranklist&type=blog&view=$key&orderby=$orderby">$value[name]{lang ranklist}</a></li>
				<!--{/loop}-->
				<!--{/if}-->
			</ul>
			<p id="before" class="tbmu">
				<a href="misc.php?mod=ranklist&type=blog&view=$_GET[view]&orderby=thisweek" id="604800" {if $orderby == 'thisweek'}class="a"{/if} />{lang ranklist_week}</a><span class="pipe">|</span>
				<a href="misc.php?mod=ranklist&type=blog&view=$_GET[view]&orderby=thismonth" id="2592000" {if $orderby == 'thismonth'}class="a"{/if} />{lang ranklist_month}</a><span class="pipe">|</span>
				<a href="misc.php?mod=ranklist&type=blog&view=$_GET[view]&orderby=today" id="86400" {if $orderby == 'today'}class="a"{/if} />{lang ranklist_today}</a><span class="pipe">|</span>
				<a href="misc.php?mod=ranklist&type=blog&view=$_GET[view]&orderby=all" id="all" {if $orderby == 'all'}class="a"{/if} />{lang all}</a>
			</p>
			<!--{if $bloglist}-->
				<div class="xld xlda hasrank">
				<!--{loop $bloglist $blog}-->
					<dl class="bbda">
						<dd class="ranknum"><span class="ranks{if $blog['rank'] <= 3} ranks_$blog['rank']{/if}">$blog['rank']</span></dd>
						<dd class="m">
							<div class="avt">
								<a href="home.php?mod=space&uid=$blog[uid]" target="_blank">$blog[avatar]</a>
							</div>
						</dd>
						<dt class="xs2">
							<!--{if helper_access::check_module('share')}-->
							<a href="home.php?mod=spacecp&ac=share&type=blog&id=$blog[blogid]&handlekey=lsbloghk_{$blog[blogid]}" id="a_share_$blog[blogid]" onclick="showWindow(this.id, this.href, 'get', 0);" class="oshr xs1 xw0">{lang share}</a>
							<!--{/if}-->
							<a href="home.php?mod=space&uid=$blog[uid]&do=blog&id=$blog[blogid]" target="_blank">$blog[subject]</a>
						</dt>
						<dd>
							<a href="home.php?mod=space&uid=$blog[uid]" target="_blank">$blog[username]</a> <span class="xg1">$blog[dateline]</span>
						</dd>
						<dd class="cl">
							$blog[message]
						</dd>
						<dd class="xg1">
							<!--{if $_GET[view] == 'heats'}-->{lang views} $blog[hot]
							<!--{elseif $_GET[view] == 'replies'}--><a href="home.php?mod=space&uid=$blog[uid]&do=blog&id=$blog[blogid]#comment" target="_blank">{lang comment} $blog[replynum]</a>
							<!--{elseif $_GET[view] == 'views'}-->{lang ranklist_thread_view} $blog[viewnum]
							<!--{elseif $_GET[view] == 'sharetimes'}-->{lang ranklist_thread_share} $blog[sharetimes]
							<!--{elseif $_GET[view] == 'favtimes'}-->{lang ranklist_thread_favorite} $blog[favtimes]
							<!--{else}-->$clicks[$_GET[view]][name] $blog['click'.$_GET[view]]<!--{/if}-->
						</dd>
					</dl>
				<!--{/loop}-->
				</div>
			<!--{else}-->
				<div class="emp">{lang none_data}</div>
			<!--{/if}-->
			<div class="notice">{lang ranklist_update}</div>
		</div>
		<!--[diy=diycontentbottom]--><div id="diycontentbottom" class="area"></div><!--[/diy]-->
	</div>

	<div class="appl">
		<!--[diy=diysidetop]--><div id="diysidetop" class="area"></div><!--[/diy]-->
		<!--{subtemplate ranklist/side_left}-->
		<!--[diy=diysidebottom]--><div id="diysidebottom" class="area"></div><!--[/diy]-->
	</div>
</div>

<!--[diy=diyranklistbottom]--><div id="diyranklistbottom" class="area"></div><!--[/diy]-->

<!--{template common/footer}-->