/*
[name]红[/name]
[iconbgcolor]#BA350F[/iconbgcolor]
Powered by Discuz! X
*/

html { background: #FAF6ED url(background.jpg); }
::selection{ background-color: #BA350F; color: #FFF; }
:focus { outline-color: #C00; }
body { background: transparent url(bgimg.jpg) no-repeat 50% 0; background-size: 970px; }
@media(min-resolution:120dpi){ body { background-image: url(bgimg2x.jpg); }}
.fc-p { color: #C00; }
#nv { background-color: #C00; }
	#nv li.a { background-color: #800; }
	#nv li a:hover, #nv li.hover a:hover, #nv li.hover a { background-color: #600; }
#scbar { background-color: #FDF5F5; border-color: #FCC; }
	#scbar_btn { background-color: #C00; }
#scform_srchtxt { border-color: #C00; }
#scform_tb .a::before { border-bottom-color: #C00; }
#scform_submit { background-color: #C00; }

.bm_h, .tl th, .tl td { border-bottom-color: #CDCDCD; }
.fl .bm_h { border-color: #FCC; background: #FFECEC; }

/* 重定义 {SPECIALBG} */
.bmn, .bm_h, td.pls, .ad td.plc, div.exfm, .tb a, .tb_h, .ttp li.a a, div.uo a, input#addsubmit_btn, #gh .bm .bm_h { background-color: #FFECEC; }

.ct2_a, .ct3_a { background-image: url(vlineb.jpg); }
	.ct3_a .bm { background-color: transparent; }
.tbn li.a { background: #FAF6ED url(background.jpg); }
	#nv_portal.pg_portalcp .ct2_a_r, #nv_forum.pg_post .ct2_a_r, #nv_group.pg_post .ct2_a_r { background-image: none; }

/* 重定义 {SPECIALBORDER} */
.bmn, .pg a, .pgb a, .pg strong, .card, .card .o, div.exfm  { border-color: #FCC; }
.pg strong { background-color: #FCC; }
.pnc, .tb .o, .tb .o a { background-color: #C00; }
	.pnc, a.pnc, .tb .o { border-color: #900; }
	.pnc:active { background: #800; }
	.pnc:focus { box-shadow: 0 0 0 2px rgba(136, 0, 0, 0.25); }

.mi #moodfm.hover textarea { border-color: #FCC; }

.ad td.pls { background-color: #FCC; }
.side_btn a svg { fill: #BA350F; }

/* 重定义字体 */
#qmenu, .fl .bm_h h2 a, .xi1, #um .new, .topnav .new, .sch .new, .el .ec .hot em, .pll .s a.joins, #diy_backup_tip .xi2 { color: #BA350F; }
.pll .s a.go { color: #FFF; }
.xi2, .xi2 a { color: #000; }

/* 重定义按钮 */
.pgsbtn { background-color: #C00; }
	.pgsbtn:hover { background-color: #800; }
	.pgsbtn:active { box-shadow: 0 0 0 3px rgba(136,0,0,0.25); }
.el .ec .hot, .clct_list .xld .m { background-image: linear-gradient(0deg,#FFB5AA 40%,#FFE7E2 40%); }
.pll .s { background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='60' height='89'%3e%3cpath fill='%23ffe7e2' d='M60 0v50c0 5.5-4.5 10-10 10H0V10C0 4.5 4.5 0 10 0z'/%3e%3cpath fill='%23ffb5aa' d='M60 65v19c0 2.75-2.25 5-5 5H0V70c0-2.75 2.25-5 5-5zM8 75l2.5 2.5L8 80h2l2.5-2.5L10 75z'/%3e%3c/svg%3e"); }

/* 重定义 Tab */
.tb .a a { border-bottom-color: #FAF6ED; background: url(background.jpg); }

/* 重定义广播 */
#uhd, #flw_header .bar { background-color: #FFF9F9; }
	#uhd .tb a, #flw_header .bar { border-top-color: #FFE6E6; }
	#uhd .tb .a a { border-bottom-color: #FAF6ED; }
.flw_replybox { background-color: #FFF9F9; }
	.flw_replybox .px, .flw_autopt, #flw_header .tedt { border-color: #FCC; }
#flw_post_extra .sec { border-color: #FFE6E6; background-color: #FFF9F9; }

/* 重定义版块快速跳转和侧边导航 */
#fjump_menu li.a a { background-color: #FFF9F9; }
.bdl, .bdl dt, .bdl dd.bdl_a a { border-color: #FCC; background-color: #FFF9F9; }
.bdl dd.bdl_a a { color: #BA350F; }

/* 重定义下拉菜单 */
.p_pop a { border-bottom-color: #FFECEC; }
	.p_pop a:hover, .p_pop a.a, #sctype_menu .sca { background-color: #FFECEC; color: #BA350F; }
	.blk a:hover { background-color: transparent; }

/* 重定义好友列表 */
#nv_home .buddy li { background-color: transparent; border: none; }