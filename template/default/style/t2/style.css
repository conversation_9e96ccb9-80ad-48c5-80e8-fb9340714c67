/*
[name]青[/name]
[iconbgcolor]#429296[/iconbgcolor]
Powered by Discuz! X
*/

::selection{ background-color: #429296; color: #FFF; }
:focus { outline-color: #429296; }
body { background: #FFF url(bgimg.jpg) no-repeat 50% 0; background-size: 1400px; }
@media(min-resolution:120dpi){ body { background-image: url(bgimg2x.jpg); }}
.fc-p { color: #429296; }
#nv,#scbar_btn { background-color: #429296; }
	#nv li.a { background-color: #005D5D; }
	#nv li a:hover, #nv li.hover a:hover, #nv li.hover a { background-color: #044; }
#scform_srchtxt { border-color: #429296; }
#scform_tb .a::before { border-bottom-color: #429296; }
#scform_submit { background-color: #429296; }

.bm_h, .tl th, .tl td { border-bottom-color: #CDCDCD; }
.fl .bm_h { border-color: #C5E6DF; background: #EBF5F6; }

/* 重定义 {SPECIALBG} */
.bmn, .bm_h, td.pls, .ad td.plc, div.exfm, .tb a, .tb_h, .ttp li.a a, div.uo a, input#addsubmit_btn, #gh .bm .bm_h, .appnew, .apphot { background-color: #EBF5F6; }

.ct2_a, .ct3_a { background-image: linear-gradient(0deg,#E8F6F3,#E8F6F3); }
.tbn li.a { background: #FFF; }
	#nv_portal.pg_portalcp .ct2_a_r, #nv_forum.pg_post .ct2_a_r, #nv_group.pg_post .ct2_a_r { background-image: none; }

/* 重定义 {SPECIALBORDER} */
.bmn, .pg a, .pgb.a, .pg strong, .card, .card .o, div.exfm  { border-color: #C5E6DF; }
.pg strong { background-color: #C5E6DF; }
.pnc, .tb .o, .tb .o a { background-color: #429296; }
	.pnc, a.pnc, .tb .o { border-color: #066; }
	.pnc:active { background: #005D5D; }
	.pnc:focus { box-shadow: 0 0 0 2px rgba(0, 93, 93, 0.25); }

.mi #moodfm.hover textarea { border-color: #C5E6DF; }

.ad td.pls { background-color: #C5E6DF; }
.side_btn a svg { fill: #429296; }

/* 重定义字体 */
#qmenu, .fl .bm_h h2 a, .xi1, #um .new, .topnav .new, .sch .new, .el .ec .hot em, .pll .s a.joins { color: #429296; }
.pll .s a.go { color: #FFF; }
.xi2, .xi2 a { color: #000; }

/* 重定义按钮 */
.pgsbtn { background-color: #429296; }
	.pgsbtn:hover { background-color: #005D5D; }
	.pgsbtn:active { box-shadow: 0 0 0 3px rgba(0, 93, 93, 0.25); }
.el .ec .hot, .clct_list .xld .m { background-image: linear-gradient(0deg,#9AD1CF 40%,#CAE7E2 40%); }; }
.pll .s { background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='60' height='89'%3e%3cpath fill='%23cae7e2' d='M60 0v50c0 5.5-4.5 10-10 10H0V10C0 4.5 4.5 0 10 0z'/%3e%3cpath fill='%239ad1cf' d='M60 65v19c0 2.75-2.25 5-5 5H0V70c0-2.75 2.25-5 5-5zM8 75l2.5 2.5L8 80h2l2.5-2.5L10 75z'/%3e%3c/svg%3e"); }

/* 重定义广播 */
#uhd, #flw_header .bar, .flw_replybox { background-color: #F4F9F9; }
#flw_post_extra .sec { background-color: #F4F9F9; }

/* 重定义版块快速跳转和侧边导航 */
#fjump_menu li.a a { background-color: #EBF5F6; }
.bdl, .bdl dt, .bdl dd.bdl_a a { border-color: #C2D5E3; background-color: #EBF5F6; }
.bdl dd.bdl_a a { color: #429296; }

/* 重定义下拉菜单 */
.p_pop a { border-bottom-color: #EBF5F6; }
	.p_pop a:hover, .p_pop a.a, #sctype_menu .sca { background-color: #EBF5F6; color: #429296; }
	.blk a:hover { background-color: transparent; }