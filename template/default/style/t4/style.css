/*
[name]紫[/name]
[iconbgcolor]#9934B7[/iconbgcolor]
Powered by Discuz! X
*/

body { background: #FFF url(bgimg.jpg) no-repeat 50% 28px; background-size: 1400px; }
@media(min-resolution:120dpi){ body { background-image: url(bgimg2x.jpg); }}
.fc-p { color: #9934B7; }
#nv { background-color: #9934B7; }
	#nv li.a { background-color: #6C1992; }
	#nv li a:hover, #nv li.hover a:hover, #nv li.hover a { background-color: #5C127D; }
	#scbar_btn { background-color: #9934B7; }
#scform_srchtxt { border-color: #9934B7; }
#scform_tb .a::before { border-bottom-color: #9934B7; }
#scform_submit { background-color: #9934B7; }

.bm_h, .tl th, .tl td { border-bottom-color: #CDCDCD; }
.fl .bm_h { border-color: #DFCDDE; background: #F8EFF8; }

/* 重定义 {SPECIALBG} */
.bmn, .bm_h, td.pls, .ad td.plc, div.exfm, .tb a, .tb_h, .ttp li.a a, div.uo a, input#addsubmit_btn, #gh .bm .bm_h { background-color: #F8EFF8; }

.ct2_a, .ct3_a { background-image: linear-gradient(0deg,#D7F5FD,#D7F5FD); }
.tbn li.a { background: #FFF; }
	#nv_portal.pg_portalcp .ct2_a_r, #nv_forum.pg_post .ct2_a_r, #nv_group.pg_post .ct2_a_r { background-image: none; }

/* 重定义 {SPECIALBORDER} */
.bmn, .pg a, .pgb a, .pg strong, .card, .card .o, div.exfm  { border-color: #DFCDDE; }
.pg strong { background-color: #DFCDDE; }
.pnc, .tb .o, .tb .o a { background-color: #9934B7; }
	.pnc, a.pnc, .tb .o { border-color: #609; }
	.pnc:active { background: #6C1992; }
	.pnc:focus { box-shadow: 0 0 0 2px rgba(108, 25, 146, 0.25); }

.mi #moodfm.hover textarea { border-color: #DFCDDE; }

.ad td.pls { background-color: #DFCDDE; }
.side_btn a svg { fill: #9934B7; }

/* 重定义字体 */
#qmenu, .fl .bm_h h2 a, .xi1, #um .new, .topnav .new, .sch .new, .el .ec .hot em, .pll .s a.joins, #diy_backup_tip .xi2 { color: #6F099E; }
.pll .s a.go { color: #FFF; }
.xi2, .xi2 a { color: #000; }

/* 重定义按钮 */
.pgsbtn { background-color: #9934B7; }
	.pgsbtn:hover { background-color: #6C1992; }
	.pgsbtn:active { box-shadow: 0 0 0 3px rgba(108, 25, 146, 0.25); }
.el .ec .hot, .clct_list .xld .m { background-image: linear-gradient(0deg,#CBA4D3 40%,#E6CEE5 40%); }
.pll .s { background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='60' height='89'%3e%3cpath fill='%23e6cee5' d='M60 0v50c0 5.5-4.5 10-10 10H0V10C0 4.5 4.5 0 10 0z'/%3e%3cpath fill='%23cba4d3' d='M60 65v19c0 2.75-2.25 5-5 5H0V70c0-2.75 2.25-5 5-5zM8 75l2.5 2.5L8 80h2l2.5-2.5L10 75z'/%3e%3c/svg%3e"); }

/* 重定义广播 */
#uhd, #flw_header .bar, .flw_replybox { background-color: #FCF9FC; }
	#uhd .tb a, #flw_header .bar { border-top-color: #F8EFF8; }
#flw_post_extra .sec { border-color: #F8EFF8; background-color: #FCF9FC; }

/* 重定义版块快速跳转和侧边导航 */
#fjump_menu li.a a { background-color: #FCF8FC; }
.bdl, .bdl dt, .bdl dd.bdl_a a { border-color: #DFCDDE; background-color: #FCF8FC; }
.bdl dd.bdl_a a { color: #6F099E; }

/* 重定义下拉菜单 */
.p_pop a { border-bottom-color: #F8EFF8; }
	.p_pop a:hover, .p_pop a.a, #sctype_menu .sca { background-color: #F8EFF8; color: #6F099E; }
	.blk a:hover { background-color: transparent; }