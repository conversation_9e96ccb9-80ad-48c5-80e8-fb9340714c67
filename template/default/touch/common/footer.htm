<!--{hook/global_footer_mobile}-->
<div id="mask" style="display:none;"></div>
<!--{if $_G['setting']['statcode']}--><div id="statcode" style="display:none;">{$_G['setting']['statcode']}</div><!--{/if}-->
<!--{if !$nofooter}-->
<div class="foot_height"></div>
<div class="foot flex-box">
	<a href="index.php" class="flex{if (strpos($_SERVER['REQUEST_URI'], $_G['setting']['defaultindex']) && strpos('forum.php', $_G['setting']['defaultindex']) === false) || strpos($_SERVER['REQUEST_URI'], 'index.php') || (strpos('forum.php', $_G['setting']['defaultindex']) === 0 && $_G['basescript'] == 'forum' && $_GET['mod'] != 'find')} mon{/if}">
		<span class="foot-ico"><em class="ma"></em></span>
		<span class="foot-txt">{lang mobhome}</span>
	</a>
	<!--{if strpos('forum.php', $_G['setting']['defaultindex']) === false}-->
	<a href="<!--{if $_G['setting']['mobile']['forum']['index'] > 1}-->forum.php<!--{elseif $_G['setting']['mobile']['forum']['index'] == 1}-->forum.php?mod=guide&view=newthread<!--{else}-->forum.php?forumlist=1<!--{/if}-->" class="flex{if $_G['basescript'] == 'forum' && $_GET['mod'] != 'find' && (CURMODULE == 'index' || $_GET['forumlist'] == 1 || CURMODULE == 'forumdisplay' || CURMODULE == 'viewthread' || CURMODULE == 'guide')} mon{/if}">
		<span class="foot-ico"><em class="mb"></em></span>
		<span class="foot-txt">{$_G['setting']['navs'][2]['navname']}</span>
	</a>
	<!--{elseif strpos('forum.php', $_G['setting']['defaultindex']) === 0}-->
		<!--{if helper_access::check_module('portal')}-->
		<a href="portal.php" class="flex{if $_G['basescript'] == 'portal'} mon{/if}">
			<span class="foot-ico"><em class="mb"></em></span>
			<span class="foot-txt">{lang mobnews}</span>
		</a>
		<!--{elseif helper_access::check_module('group')}-->
		<a href="group.php" class="flex{if $_G['basescript'] == 'group'} mon{/if}">
			<span class="foot-ico"><em class="mb"></em></span>
			<span class="foot-txt">{$_G['setting']['navs'][3]['navname']}</span>
		</a>
		<!--{elseif helper_access::check_module('feed')}-->
		<a href="home.php" class="flex{if $_G['basescript'] == 'home' && CURMODULE == 'space' && $_GET['do'] == 'home'} mon{/if}">
			<span class="foot-ico"><em class="mb"></em></span>
			<span class="foot-txt">{lang mobfeed}</span>
		</a>
		<!--{else}-->
		<a href="home.php?mod=space&do=pm" class="flex{if $_G['basescript'] == 'home' && CURMODULE == 'space' && $_GET['do'] == 'pm'} mon{/if}">
			<span class="foot-ico"><em class="mb"><!--{if $_G['uid'] && ($_G['member']['newpm'] || $_G['member']['newprompt'])}--><i class="ico_msg"></i><!--{/if}--></em></span>
			<span class="foot-txt">{lang pm_center}</span>
		</a>
		<!--{/if}-->
	<!--{/if}-->
	<a href="forum.php?mod=misc&action=nav" class="flex foot-post">
		<span class="foot-ico"><em class="mc"></em></span>
		<span class="foot-txt">{lang mobpost}</span>
	</a>
	<a href="forum.php?mod=find" class="flex{if $_G['basescript'] == 'forum' && $_GET['mod'] == 'find'} mon{/if}">
		<span class="foot-ico"><em class="md"></em></span>
		<span class="foot-txt">{lang mobfind}</span>
	</a>
	<a href="{if $_G['uid']}home.php?mod=space&uid={$_G['uid']}&do=profile&mycenter=1{else}member.php?mod=logging&action=login{/if}" class="flex{if $_G['basescript'] == 'home' && CURMODULE == 'space' && $_GET['mycenter'] == 1} mon{/if}">
		<span class="foot-ico"><em class="me">
			<!--{if strpos('forum.php', $_G['setting']['defaultindex']) === false || helper_access::check_module('portal') || helper_access::check_module('group') || helper_access::check_module('feed')}-->
			<!--{if $_G['uid'] && ($_G['member']['newpm'] || $_G['member']['newprompt'])}--><i class="ico_msg"></i><!--{/if}-->
			<!--{/if}-->
		</em></span>
		<span class="foot-txt">{lang mobmy}</span>
	</a>
</div>
<!--{/if}-->
</body>
</html>
<!--{eval updatesession();}-->
<!--{if defined('IN_MOBILE')&&!defined('IN_PREVIEW')}-->
	<!--{eval output();}-->
<!--{else}-->
	<!--{eval output_preview();}-->
<!--{/if}-->
