{eval
	$sechash = 'S'.random(4);
	$sectpl = !empty($sectpl) ? explode("<sec>", $sectpl) : array('<br />',': ','<br />','');	
}
<!--{if $sectpl}-->
	<!--{if $secqaacheck}-->
		<script type="text/javascript">
			//Todo: 抽函数到文件中
			var seccheck_tpl = new Array();
			var seccheck_modid = new Array();
			function updatesecqaa(idhash, tpl) {
				if(document.getElementById('secqaa_' + idhash)) {
					if(tpl) {
						seccheck_tpl[idhash] = tpl;
					}
					var id = 'seqaajs_' + idhash;
					var src = 'misc.php?mod=secqaa&action=update&idhash=' + idhash + '&' + Math.random();
					if(document.getElementById(id)) {
						document.getElementsByTagName('head')[0].appendChild(document.getElementById(id));
					}
					var scriptNode = document.createElement("script");
					scriptNode.type = "text/javascript";
					scriptNode.id = id;
					scriptNode.src = src;
					document.getElementsByTagName('head')[0].appendChild(scriptNode);
				}
			}
		</script>
		<li class="sec_code vm">
			<span id="secqaa_$sechash" class="flex-box"></span>
			<script type="text/javascript" reload="1">updatesecqaa('$sechash', '$sectpl');</script>
		</li>
	<!--{/if}-->
	<!--{if $seccodecheck}-->
		<script type="text/javascript">
			//Todo: 抽函数到文件中
			var seccheck_tpl = new Array();
			var seccheck_modid = new Array();
			function updateseccode(idhash, tpl, modid) {
				if(!document.getElementById('seccode_' + idhash)) {
					return;
				}
				if(tpl) {
					seccheck_tpl[idhash] = tpl;
				}
				if(modid) {
					seccheck_modid[idhash] = modid;
				} else {
					modid = seccheck_modid[idhash];
				}
				var id = 'seccodejs_' + idhash;
				var src = 'misc.php?mod=seccode&action=update&mobile=2&idhash=' + idhash + '&' + Math.random() + '&modid=' + modid;
				if(document.getElementById(id)) {
					document.getElementsByTagName('head')[0].appendChild(document.getElementById(id));
				}
				var scriptNode = document.createElement("script");
				scriptNode.type = "text/javascript";
				scriptNode.id = id;
				scriptNode.src = src;
				document.getElementsByTagName('head')[0].appendChild(scriptNode);
			}
		</script>
		<li class="sec_code vm">
			<span id="seccode_c$sechash" class="flex-box"></span>
			<script type="text/javascript" reload="1">updateseccode('c$sechash', '$sectpl', '{$_G['basescript']}::{CURMODULE}');</script>
		</li>
	<!--{/if}-->
<!--{/if}-->