<!--{if $param['login']}-->
	<!--{if $_G['inajax']}-->
	<!--{eval dheader('Location:member.php?mod=logging&action=login&inajax=1&infloat=1');exit;}-->
	<!--{else}-->
	<!--{eval dheader('Location:member.php?mod=logging&action=login');exit;}-->
	<!--{/if}-->
<!--{/if}-->
<!--{template common/header}-->
<!--{if $_G['inajax']}-->
<div class="tip">
	<dt id="messagetext">
		<p>$show_message</p>
        <!--{if $_G['forcemobilemessage']}-->
        <p>
			<a href="{$_G['setting']['mobile']['pageurl']}" class="mtn">{lang continue}</a><br />
			<a href="javascript:history.back();">{lang goback}</a>
		</p>
        <!--{/if}-->
		<!--{if $url_forward && !$_GET['loc']}-->
		<script type="text/javascript">
			setTimeout(function() {
				window.location.href = '$url_forward';
			}, '3000');
		</script>
		<!--{elseif $allowreturn}-->
		<dd><input type="button" class="close pn" onclick="popup.close();" value="{lang close}"></dd>
		<!--{/if}-->
	</dt>
</div>
<!--{else}-->
<div class="header cl">
	<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div>
	<h2>{lang mobtishi}</h2>
	<div class="my"><a href="index.php"><i class="dm-house"></i></a></div>
</div>
<div class="jump_c">
	<p>$show_message</p>
	<!--{if $_G['forcemobilemessage']}-->
	<p class="mt10">
		<a href="{$_G['setting']['mobile']['pageurl']}">{lang continue}</a><br />
		<a href="javascript:history.back();">{lang goback}</a>
	</p>
	<!--{/if}-->
	<!--{if $url_forward}-->
	<p><a href="$url_forward" class="grey">{lang message_forward_mobile}</a></p>
	<!--{elseif $allowreturn}-->
	<p><a href="javascript:history.back();" class="grey">{lang message_go_back}</a></p>
	<!--{/if}-->
</div>
<!--{/if}-->
<!--{template common/footer}-->
