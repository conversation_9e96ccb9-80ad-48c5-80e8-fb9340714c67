<!--{template common/header_ajax}-->
<span class="cnr"></span>
<form method="post" autocomplete="off" id="postform_{$_GET['feedid']}" action="forum.php?mod=post&action=reply&fid=$_GET['fid']&extra=$extra&tid=$_GET['tid']&replysubmit=yes" onsubmit="this.message.value = parseurl(this.message.value);{if !empty($_GET['inajax'])}ajaxpost(this.id, 'return_$_GET['handlekey']', 'return_$_GET['handlekey']', 'onerror');return false;{/if}" class="mbm">
	<input type="hidden" name="formhash" id="formhash" value="{FORMHASH}" />
	<input type="hidden" name="handlekey" value="$_GET['handlekey']" />
	<span id="subjectbox" style="display: none;"><input name="subject" id="subject" class="px" value="" style="width: 25em" /></span>
	<div class="moodfm">
		<div class="moodfm_post">
			<div class="moodfm_text flw_autopt">
				<textarea name="message" id="postmessage_{$_GET['tid']}_{$_GET['feedid']}" class="pts" cols="80" rows="4"  placeholder="{lang thread_content}" onkeyup="resizeTx(this);" onkeydown="resizeTx(this);" onpropertychange="resizeTx(this);" oninput="resizeTx(this);"></textarea>
			</div>

			<!--{if $secqaacheck || $seccodecheck}-->
			<!--{block sectpl}--><ul><li><em class="d"><sec></em><span id="sec<hash>" onclick="showMenu({'ctrlid':this.id,'win':'{$_GET[handlekey]}'})"><sec></span><div id="sec<hash>_menu" class="p_pop p_opt" style="display:none"><sec></div></li></ul><!--{/block}-->
			<div class="mtm sec post_box"><ul class="cl"><!--{subtemplate common/seccheck}--></ul></div>
			<!--{/if}-->

			<div class="moodfm_f">
				<div class="moodfm_btn">
					<button type="submit" id="postsubmit" class="pgsbtn button" value="true" name="{if $_GET['action'] == 'newthread'}topicsubmit{elseif $_GET['action'] == 'reply'}replysubmit{/if}">{lang reply}</button>
				</div>

				<!--{if helper_access::check_module('follow')}-->
				<div class="moodfm_signature">
					<label>
						<input type="checkbox" name="adddynamic" class="pc" value="1" />
						<span>{lang post_meanwhile_relay}</span>
					</label>
				</div>
				<!--{/if}-->
			</div>
		</div>
	</div>
</form>
<ul id="newreply_{$_GET['tid']}_{$_GET['feedid']}" class="flw_newreply">
	<!--{loop $list $pid $post}-->
	<li><a href="home.php?mod=space&uid=$post['authorid']" class="d xi2">$post['author']:</a>$post['message']</li>
	<!--{/loop}-->
</ul>
<div class="cl">
	<a href="javascript:;" onclick="replybox_close('replybox_{$_GET['feedid']}')" class="replybox_close">{lang close}</a>
	<!--{if $fid != $_G['setting']['followforumid']}-->
		<a href="forum.php?mod=viewthread&tid={$_GET['tid']}&extra=page%3D1" target="_blank" class="xi2">&rarr; {lang view_all_replies}</a>
	<!--{/if}-->
</div>
<script type="text/javascript">
	function succeedhandle_$_GET[handlekey](url, msg, values) {
		var x = new Ajax();
		x.get('forum.php?mod=ajax&action=getpost&inajax=1&tid='+values.tid+'&fid='+values.fid+'&pid='+values.pid, function(s){
			newli = document.createElement("li");
			newli.innerHTML = s;
			var ulObj = $('newreply_'+values.tid+'_{$_GET['feedid']}');
			ulObj.insertBefore(newli, ulObj.firstChild);
			$('postmessage_{$_GET['tid']}_{$_GET['feedid']}').value = '';
			if(values.sechash) {
				updatesecqaa(values.sechash);
				updateseccode(values.sechash);
				$('seccodeverify_'+values.sechash).value='';
			}
		});
		
		if(parseInt(values.feedid)) {
			getNewFollowFeed(values.tid, values.fid, values.pid, values.feedid);
		}
	}
</script>
<!--{template common/footer_ajax}-->