<!--{template common/header}-->
<div class="tip loginbox loginpop p5" id="floatlayout_comment">
	<form method="post" autocomplete="off" id="commentform" action="forum.php?mod=post&action=reply&comment=yes&tid=$post['tid']&pid=$_GET['pid']&extra=$extra{if !empty($_GET['page'])}&page=$_GET['page']{/if}&commentsubmit=yes&infloat=yes&inajax=1">
		<h2 class="log_tit" id="return_comment"><a href="javascript:;" onclick="popup.close();"><span class="icon_close y">&nbsp;</span></a>{lang comments}</h2>
		<input type="hidden" name="formhash" id="formhash" value="{FORMHASH}" />
		<input type="hidden" name="handlekey" value="$_GET['handlekey']" />
		<!--{if $commentitem}-->
		<ul class="post-box cl">
			<!--{eval $items = range(0, 5);$itemlang = array('{lang comment_1}', '{lang comment_2}', '{lang comment_3}', '{lang comment_4}', '{lang comment_5}', '{lang comment_6}');$i = $cmm = 0;}-->
			<!--{loop $commentitem $item}-->
			<!--{eval $item = trim($item);}-->
			<!--{if $item}-->
			<li class="flex-box">
				<div class="flex">$item</div>
				<div class="flex-2">
				<span id="clct_$i" class="clct_star z">
					<a href="javascript:;" onclick="rateStarSet('clct_$i',1,'itemc_$i')"><i class="dm-star-fill"></i></a>
					<a href="javascript:;" onclick="rateStarSet('clct_$i',2,'itemc_$i')"><i class="dm-star-fill"></i></a>
					<a href="javascript:;" onclick="rateStarSet('clct_$i',3,'itemc_$i')"><i class="dm-star-fill"></i></a>
					<a href="javascript:;" onclick="rateStarSet('clct_$i',4,'itemc_$i')"><i class="dm-star-fill"></i></a>
					<a href="javascript:;" onclick="rateStarSet('clct_$i',5,'itemc_$i')"><i class="dm-star-fill"></i></a>
				</span>
				</div>
				<input type="hidden" id="itemc_$i" name="commentitem[$item]" value="" />
			</li>
			<!--{eval $i++;}-->
			<!--{/if}-->
			<!--{/loop}-->
		</ul>
		<!--{/if}-->
		<dt class="mpt">
			<textarea rows="2" cols="50" name="message" id="commentmessage" class="pt" style="overflow: auto"></textarea>
		</dt>
		<!--{if $secqaacheck || $seccodecheck}-->
		<ul class="post-box p5 cl">
			<!--{subtemplate common/seccheck}-->
		</ul>
		<!--{/if}-->
		<button type="submit" id="commentsubmit" class="pn pnc formdialog" value="true" name="commentsubmit"{if !$seccodecheck && !$secqaacheck} onmouseover="checkpostrule('seccheck_comment', 'ac=reply&infloat=yes&handlekey=$_GET[handlekey]');this.onmouseover=null"{/if}><span>{lang publish}</span></button>
	</form>
</div>
<script type="text/javascript" reload="1">
getID('commentmessage').focus();
function rateStarSet(target, level, input) {
	if(input) getID(input).value = level;
	var stars = getID(target).children;
	for(var i = 0; i < stars.length; i++) {
		getID(target).children[i].style.color = (i < level) ? "var(--dz-BG-color)" : "#ccc";
	}
}
</script>
<style>
	.clct_star a {
		color: #ccc;
	}
</style>
<!--{template common/footer}-->