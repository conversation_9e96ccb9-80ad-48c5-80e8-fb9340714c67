<!--{template common/header}-->
<div class="header cl">
	<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div>
	<h2><!--{eval echo strip_tags($_G['forum']['name']) ? strip_tags($_G['forum']['name']) : $_G['forum']['name'];}--></h2>
	<div class="my"><a href="forum.php?mod=post&action=newthread&fid={$_G['fid']}"><i class="dm-edit"></i></a></div>
</div>
<!--{hook/forumdisplay_top_mobile}-->
<div class="forumdisplay-top cl">
	<h2>
	<!--{if $_G['forum']['icon']}--><img src="<!--{echo get_forumimg($_G['forum']['icon'])}-->" alt="$_G['forum']['name']" /><!--{else}--><svg width="48" height="44" alt="$_G['forum']['name']"><path fill="#c9c9c9" d="M48 20C48 9 37.3 0 24 0S0 8.9 0 20s10.7 20 24 20c2.4 0 4.7-.3 6.8-.8L42 44l-2.8-8.5C44.6 31.8 48 26.2 48 20z"/></svg><!--{/if}-->
	<!--{if helper_access::check_module('favorite')}-->
	<a href="home.php?mod=spacecp&ac=favorite&type=forum&id=$_G['fid']&handlekey=favoriteforum&formhash={FORMHASH}" id="a_favorite" class="dialog">{lang favorite}<span id="number_favorite" {if !$_G['forum']['favtimes']} style="display:none;"{/if}><span id="number_favorite_num"> +{$_G['forum']['favtimes']}</span></span></a>
	<!--{/if}-->
	$_G['forum']['name']
	</h2>
	<p>{lang index_today}: <span>$_G['forum']['todayposts']</span>{lang index_threads}: <span>$_G['forum']['threads']</span><!--{if $_G['forum']['rank']}-->{lang rank}: <span>$_G['forum']['rank']</span><!--{/if}--></p>
	<!--{hook/forumdisplay_nav_mobile}-->
</div>
<div class="dhnav_box">
	<div id="dhnav">
		<div id="dhnav_li">
		<ul class="flex-box">
			<li class="flex{if $_REQUEST['sortall']==1||(!$_REQUEST['typeid'] && !$_REQUEST['sortid']&& !$_REQUEST['filter'])} mon{/if}"><a href="forum.php?mod=forumdisplay&fid=$_G['fid']{if $_G['forum']['threadsorts']['defaultshow']}&filter=sortall&sortall=1{/if}{if $_GET['archiveid']}&archiveid={$_GET['archiveid']}{/if}">{lang all}</a></li>
			<li class="flex{if $_GET['filter'] == 'lastpost'} mon{/if}"><a href="forum.php?mod=forumdisplay&fid=$_G['fid']&filter=lastpost&orderby=lastpost$forumdisplayadd['lastpost']{if $_GET['archiveid']}&archiveid={$_GET['archiveid']}{/if}">{lang latest}</a></li>
			<li class="flex{if $_GET['filter'] == 'heat'} mon{/if}"><a href="forum.php?mod=forumdisplay&fid=$_G['fid']&filter=heat&orderby=heats$forumdisplayadd['heat']{if $_GET['archiveid']}&archiveid={$_GET['archiveid']}{/if}">{lang order_heats}</a></li>
			<li class="flex{if $_GET['filter'] == 'hot'} mon{/if}"><a href="forum.php?mod=forumdisplay&fid=$_G['fid']&filter=hot">{lang hot_thread}</a></li>
			<li class="flex{if $_GET['filter'] == 'digest'} mon{/if}"><a href="forum.php?mod=forumdisplay&fid=$_G['fid']&filter=digest&digest=1$forumdisplayadd['digest']{if $_GET['archiveid']}&archiveid={$_GET['archiveid']}{/if}">{lang digest_posts}</a></li>
		</ul>
		</div>
	</div>
</div>
<!--{hook/forumdisplay_middle}-->
<!--{if $_G['forum']['threadsorts'] || ($_G['forum']['threadtypes'] && $_G['forum']['threadtypes']['listable'])}-->
<div class="dhnavs_box">
	<div id="dhnavs">
		<div id="dhnavs_li">
			<ul class="swiper-wrapper">
				<!--{if ($_G['forum']['threadtypes'] && $_G['forum']['threadtypes']['listable'])}-->
					<!--{if $_G['forum']['threadtypes']}-->
						<!--{loop $_G['forum']['threadtypes']['types'] $id $name}-->
							<li class="swiper-slide{if $_GET['typeid'] == $id} mon{/if}"><a href="forum.php?mod=forumdisplay&fid=$_G['fid']&filter=typeid&typeid=$id$forumdisplayadd['typeid']{if $_GET['archiveid']}&archiveid={$_GET['archiveid']}{/if}">$name</a></li>
						<!--{/loop}-->
					<!--{/if}-->
				<!--{/if}-->
				<!--{if $_G['forum']['threadsorts']}-->
					<!--{loop $_G['forum']['threadsorts']['types'] $id $name}-->
						<!--{if $_GET['sortid'] == $id}-->
						<li class="swiper-slide mon"><a href="forum.php?mod=forumdisplay&fid=$_G['fid']{if $_GET['typeid']}&filter=typeid&typeid=$_GET['typeid']{/if}{if $_GET['archiveid']}&archiveid={$_GET['archiveid']}{/if}">$name</a></li>
						<!--{else}-->
						<li class="swiper-slide"><a href="forum.php?mod=forumdisplay&fid=$_G['fid']&filter=sortid&sortid=$id$forumdisplayadd['sortid']{if $_GET['archiveid']}&archiveid={$_GET['archiveid']}{/if}">$name</a></li>
						<!--{/if}-->
					<!--{/loop}-->
				<!--{/if}-->
			</ul>
		</div>
	</div>
</div>
<script>
	if($("#dhnavs_li .mon").length > 0) {
		var discuz_nav = $("#dhnavs_li .mon").offset().left + $("#dhnavs_li .mon").width() >= $(window).width() ? $("#dhnavs_li .mon").index() : 0;
	}else{
		var discuz_nav = 0;
	}	
	new Swiper('#dhnavs_li', {
		freeMode : true,
		slidesPerView : 'auto',
		initialSlide : discuz_nav,
		onTouchMove: function(swiper){
			Discuz_Touch_on = 0;
		},
		onTouchEnd: function(swiper){
			Discuz_Touch_on = 1;
		},
	});
</script>
<!--{/if}-->
<!--{if $subexists && $_G['page'] == 1}--><!--{template forum/forumdisplay_subforum}--><!--{/if}-->
<!--{if !$subforumonly}-->
<div class="threadlist_box<!--{if ((in_array($thread['displayorder'], array(1, 2, 3, 4))) || !empty($announcement)) && $_G['page'] == 1}--> mt10<!--{/if}--> cl">
	<div class="threadlist cl">
		<ul>
		<!--{if !empty($announcement)}-->
			<li class="list_top"><!--{if empty($announcement['type'])}--><a href="forum.php?mod=announcement&id=$announcement['id']#$announcement['id']"><!--{else}--><a href="$announcement['message']"><!--{/if}--><span class="micon gonggao">{lang announcement}</span>$announcement['subject']</a></li>
		<!--{/if}-->
		<!--{if $_G['forum_threadcount']}-->
			<!--{loop $_G['forum_threadlist'] $key $thread}-->
			<!--{if !$_G['setting']['mobile']['forum']['displayorder3'] && $thread['displayorder'] > 0}-->
				{eval continue;}
			<!--{/if}-->
			<!--{if $thread['displayorder'] > 0 && !$displayorder_thread}-->
				{eval $displayorder_thread = 1;}
			<!--{/if}-->
			<!--{if $thread['moved']}-->
				<!--{eval $thread['tid']=$thread['closed'];}-->
			<!--{/if}-->
			<!--{if in_array($thread['displayorder'], array(1, 2, 3, 4))}-->
			<li class="list_top">
				<a href="forum.php?mod=viewthread&tid=$thread['tid']&extra=$extra">
					<!--{if in_array($thread['displayorder'], array(1, 2, 3, 4))}-->
						<span class="micon">{lang mobzhiding}</span>
					<!--{/if}-->
					<em $thread['highlight']>{$thread['subject']}</em>					
				</a>
			</li>
			<!--{else}-->
			<li class="list">
				<!--{hook/forumdisplay_thread_mobile $key}-->
				<div class="threadlist_top cl">
					<!--{if $thread['authorid'] && $thread['author']}-->
					<a href="home.php?mod=space&uid={$thread['authorid']}" class="mimg"><img src="<!--{avatar($thread['authorid'], 'middle', true)}-->"></a>
					<!--{else}-->
					<a href="javascript:;" class="mimg"><img src="<!--{avatar(0, 'middle', true)}-->" /></a>
					<!--{/if}-->
					<div class="muser">
						<h3>
							<!--{if $thread['authorid'] && $thread['author']}-->
								<a href="home.php?mod=space&uid={$thread['authorid']}" class="mmc">{$thread['author']}</a>
							<!--{else}-->
								{$_G['setting']['anonymoustext']}
							<!--{/if}-->
						</h3>
						<span class="mtime">{$thread['dateline']}</span>
					</div>
				</div>
				<a href="forum.php?mod=viewthread&tid=$thread['tid']&extra=$extra">
				<div class="threadlist_tit cl">
					<!--{if $thread['folder'] == 'lock'}-->
						<span class="micon lock">{lang closed_thread}</span>
					<!--{elseif $thread['special'] == 1}-->
						<span class="micon">{lang thread_poll}</span>
					<!--{elseif $thread['special'] == 2}-->
						<span class="micon">{lang thread_trade}</span>
					<!--{elseif $thread['special'] == 3}-->
						<span class="micon">{lang thread_reward}</span>
					<!--{elseif $thread['special'] == 4}-->
						<span class="micon">{lang thread_activity}</span>
					<!--{elseif $thread['special'] == 5}-->
						<span class="micon">{lang thread_debate}</span>
					<!--{/if}-->
					<!--{if $thread['attachment'] == 2 && $_G['setting']['mobile']['mobilesimpletype'] == 1}-->
						<span class="micon">{lang mobtu}</span>
					<!--{/if}-->
					<!--{if in_array($thread['displayorder'], array(1, 2, 3, 4))}-->
						<span class="micon top">{lang thread_sticky}</span>
					<!--{/if}-->
					<!--{if $thread['digest'] > 0}-->
						<span class="micon digest">{lang thread_digest}</span>
					<!--{/if}-->
					<em $thread['highlight']>{$thread['subject']}</em>
				</div>
				</a>
				<a href="forum.php?mod=viewthread&tid=$thread['tid']&extra=$extra"><div class="threadlist_mes cl">{$threadlist_data[$thread['tid']]['message']}</div></a>
				<!--{if $threadlist_data[$thread['tid']]['attachment']}-->
				<!--{eval $attach_on = 0;}-->
				<a href="forum.php?mod=viewthread&tid=$thread['tid']&extra=$extra">
					<div class="{if count($threadlist_data[$thread['tid']]['attachment']) == 1}threadlist_imgs1 {elseif count($threadlist_data[$thread['tid']]['attachment']) == 2} threadlist_imgs threadlist_imgs2{else} threadlist_imgs{/if} cl">
						<ul>
							<!--{loop $threadlist_data[$thread['tid']]['attachment'] $value}-->
							<!--{eval $attach_on++; if($attach_on > 9) break;}-->
							<li><!--{if count($threadlist_data[$thread['tid']]['attachment']) > 9 && $attach_on == 9}--><em>{echo count($threadlist_data[$thread['tid']]['attachment'])}{lang mobtu}</em><!--{/if}--><img src="$value" class="vm"></li>
							<!--{/loop}-->
						</ul>
					</div>
				</a>
				<!--{/if}-->
				<!--{hook/forumdisplay_thread_content_mobile $key}-->
				
				<div class="threadlist_foot cl">
					<ul>
						<!--{if $thread['typeid']}-->
						<li class="mr"><a href="forum.php?mod=forumdisplay&fid=$thread['fid']&filter=typeid&typeid=$thread['typeid']">#{$_G['forum']['threadtypes']['types'][$thread['typeid']]}</a></li>
						<!--{/if}-->
						<li><i class="dm-eye-fill"></i>{$thread['views']}</li>
						<li><i class="dm-chat-s-fill"></i>{$thread['replies']}</li>
						<!--{hook/forumdisplay_thread_foot_mobile $key}-->
					</ul>
				</div>
			</li>
			<!--{/if}-->
			<!--{/loop}-->
		<!--{else}-->
			<h4>{lang forum_nothreads}</h4>
		<!--{/if}-->
		</ul>
	</div>
	$multipage
</div>
<!--{/if}-->
<!--{hook/forumdisplay_bottom_mobile}-->
<!--{template common/footer}-->