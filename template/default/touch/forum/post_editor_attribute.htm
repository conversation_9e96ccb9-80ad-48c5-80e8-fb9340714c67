<div class="discuz_x cl"></div>
<li id="post_extra" class="cl">
	<div class="dhnavs_box">
		<div id="dhnavs">
			<div id="dhnavs_li">
				<ul class="swiper-wrapper" id="post_extra_tb" onselectstart="return false">
					<li class="swiper-slide" id="extra_additional_b" onclick="showExtra('extra_additional')"><a>{lang post_additional_options}</a></li>
					<!--{if $_GET['action'] == 'newthread' || $_GET['action'] == 'edit' && $isfirstpost}-->
						<!--{if $_G['group']['allowsetreadperm']}-->
							<li class="swiper-slide" id="extra_readperm_b" onclick="showExtra('extra_readperm')"><a>{lang readperm}</a></li>
						<!--{/if}-->
						<!--{if $_G['group']['allowreplycredit'] && !in_array($special, array(2, 3))}-->
							<!--{if $_GET[action] == 'newthread'}-->
								<!--{eval $extcreditstype = $_G['setting']['creditstransextra'][10];}-->
							<!--{else}-->
								<!--{eval $extcreditstype = !empty($replycredit_rule['extcreditstype']) ? $replycredit_rule['extcreditstype'] : $_G['setting']['creditstransextra'][10];}-->
							<!--{/if}-->
							<!--{eval $userextcredit = getuserprofile('extcredits'.$extcreditstype);}-->
							<!--{if ($_GET['action'] == 'newthread' && $userextcredit > 0) || ($_GET['action'] == 'edit' && $isorigauthor && $isfirstpost)}-->
								<li class="swiper-slide" id="extra_replycredit_b" onclick="showExtra('extra_replycredit')"><a>{lang replycredit}</a></li>
							<!--{/if}-->
						<!--{/if}-->
						<!--{if ($_GET['action'] == 'newthread' && $_G['group']['allowpostrushreply'] && $special != 2) || ($_GET['action'] == 'edit' && getstatus($thread['status'], 3))}-->
							<li class="swiper-slide" id="extra_rushreplyset_b" onclick="showExtra('extra_rushreplyset')"><a>{lang rushreply_thread}</a></li>
						<!--{/if}-->
						<!--{if $_G['group']['maxprice'] && !$special}-->
							<li class="swiper-slide" id="extra_price_b" onclick="showExtra('extra_price')"><a>{lang thread_pricepay}</a></li>
						<!--{/if}-->
						<!--{if $_G['group']['allowposttag']}-->
							<li class="swiper-slide" id="extra_tag_b" onclick="showExtra('extra_tag')"><a>{lang posttag}</a></li>
						<!--{/if}-->
						<!--{if $_G['group']['allowsetpublishdate'] && ($_GET['action'] == 'newthread' || ($_GET['action'] == 'edit' && $isfirstpost && $thread['displayorder'] == -4))}-->
							<li class="swiper-slide" id="extra_pubdate_b" onclick="showExtra('extra_pubdate')"><a>{lang post_timer}</a></li>
						<!--{/if}-->
					<!--{/if}-->
					<!--{hook/post_attribute_extra_mobile}-->
				</ul>
			</div>
		</div>
	</div>
</li>

<div class="setbox" id="post_extra_c">
	<!--{if $_GET['action'] == 'newthread' || $_GET[action] == 'edit' && $isfirstpost}-->
		<!--{if !empty($userextcredit)}-->
			<div id="extra_replycredit_c" class="exfm cl" style="display: none;">
				<ul class="cl">
					<li class="flex-box mli">
						<div class="tit">{lang replycredit_reward}</div>
						<div class="flex input"><input type="text" name="replycredit_times" id="replycredit_times" class="px pxs vm" value="{if !empty($replycredit_rule['lasttimes'])}{$replycredit_rule['lasttimes']}{else}1{/if}" onkeyup="javascript:getreplycredit();" /></div>
						<div class="tit">{lang replycredit_time}</div>
					</li>
					<li class="flex-box mli">
						<div class="tit">{lang replycredit_once}</div>
						<div class="flex input"><input type="text" name="replycredit_extcredits" id="replycredit_extcredits" class="px pxs vm" value="{if !empty($replycredit_rule['extcredits']) && $thread['replycredit'] > 0}{$replycredit_rule['extcredits']}{else}0{/if}" onkeyup="javascript:getreplycredit();" /></div>
						<div class="tit">{$_G['setting']['extcredits'][$extcreditstype][unit]}{$_G['setting']['extcredits'][$extcreditstype][title]}<span class="xg1">{lang replycredit_empty}</span> </div>
					</li>
					<li class="flex-box mli">
						<div class="tit">{lang replycredit_member} </div>
						<div class="flex input">
							<select id="replycredit_membertimes" name="replycredit_membertimes" class="sort_sel vm">
							<!--{eval for($i=1;$i<11;$i++) {;}-->
							<option value="$i"{if isset($replycredit_rule['membertimes']) && $replycredit_rule['membertimes'] == $i} selected="selected"{/if}>$i</option>
							<!--{eval };}-->
							</select>
						</div>
						<div class="tit">{lang replycredit_time}</div>
					</li>
					<li class="flex-box mli">
						<div class="tit">{lang replycredit_rate}</div>
						<div class="flex input">
							<select id="replycredit_random" name="replycredit_random" class="sort_sel vm">
							<!--{eval for($i=100;$i>9;$i=$i-10) {;}-->
							<option value="$i"{if isset($replycredit_rule['random']) && $replycredit_rule['random'] == $i} selected="selected"{/if}>$i</option>
							<!--{eval };}-->
							</select>
						</div>
						<div class="tit">%</div>
					</li>
					<li class="mtit p10">{lang replycredit_total} <span id="replycredit_sum"><!--{if !empty($thread['replycredit'])}-->{$thread['replycredit']}<!--{else}-->0<!--{/if}--></span> {$_G['setting']['extcredits'][$extcreditstype][unit]}{$_G['setting']['extcredits'][$extcreditstype][title]}<!--{if !empty($thread['replycredit'])}--><span class="xg1">({lang replycredit_however} {$thread['replycredit']} {$_G['setting']['extcredits'][$extcreditstype][unit]}{$_G['setting']['extcredits'][$extcreditstype][title]})</span><!--{/if}-->, <span id="replycredit">{lang replycredit_revenue} {$_G['setting']['extcredits'][$extcreditstype][title]} 0</span> {$_G['setting']['extcredits'][$extcreditstype][unit]}, {lang you_have} {$_G['setting']['extcredits'][$extcreditstype][title]} $userextcredit {$_G['setting']['extcredits'][$extcreditstype][unit]}</li>
				</ul>
			</div>
		<!--{/if}-->

		<!--{if $_G['group']['allowsetreadperm']}-->
			<div id="extra_readperm_c" class="exfm cl" style="display:none">
				<ul class="cl">
					<li class="flex-box mli">
						<div class="tit">{lang readperm}:</div>
						<div class="flex input">
						<select name="readperm" id="readperm" class="sort_sel">
							<option value="">{lang unlimited}</option>
							<!--{loop $_G['cache']['groupreadaccess'] $val}-->
								<option value="$val[readaccess]"{if $thread['readperm'] == $val['readaccess']} selected="selected"{/if}>$val['grouptitle']</option>
							<!--{/loop}-->
							<option value="255"{if $thread['readperm'] == 255} selected="selected"{/if}>{lang highest_right}</option>
						</select>
						</div>
					</li>
					<li class="mtit p10"><span class="xg1">{lang post_select_usergroup_readacces}</span></li>
				</ul>
			</div>
		<!--{/if}-->

		<!--{if $_G['group']['maxprice'] && !$special}-->
			<div id="extra_price_c" class="exfm cl" style="display:none">
				<ul class="cl">
					<li class="flex-box mli">
						<div class="tit">{lang price}:</div>
						<div class="flex input">
							<input type="text" id="price" name="price" class="px pxs" value="$thread[pricedisplay]" />
						</div>
						<div class="pipe">
							{$_G['setting']['extcredits'][$_G['setting']['creditstransextra'][1]][unit]}{$_G['setting']['extcredits'][$_G['setting']['creditstransextra'][1]][title]}
						</div>
					</li>
					<li class="mtit p10"><span class="xg1">{lang post_price_comment}</span></li>
					<!--{if $_G['group']['maxprice'] && ($_GET[action] == 'newthread' || $_GET[action] == 'edit' && $isfirstpost)}-->
						<!--{if $_G['setting']['maxincperthread']}--><li class="mtit p10"><span class="xg1">{lang post_price_income_comment}</span></li><!--{/if}-->
						<!--{if $_G['setting']['maxchargespan']}--><li class="mtit p10"><span class="xg1">{lang post_price_charge_comment}<!--{if $_GET[action] == 'edit' && $freechargehours}-->{lang post_price_free_chargehours}<!--{/if}--></span></li><!--{/if}-->
					<!--{/if}-->
				</ul>
			</div>
		<!--{/if}-->

		<!--{if $_G['group']['allowposttag']}-->
			<div id="extra_tag_c" class="exfm cl" style="display: none;">
				<ul class="cl">
					<li class="flex-box mli">
						<div class="tit">{lang post_tag}:</div>
						<div class="flex input"><input type="text" class="px vm" size="60" id="tags" name="tags" value="{$postinfo[tag] or ''}" /></div>
					</li>
					<li class="mtit p10">
						<p class="xg1">{lang posttag_comment}</p>
					</li>
				</ul>
			</div>
		<!--{/if}-->

		<!--{if ($_GET['action'] == 'newthread' && $_G['group']['allowpostrushreply'] && $special != 2) || ($_GET[action] == 'edit' && getstatus($thread['status'], 3))}-->
			<div id="extra_rushreplyset_c" class="exfm cl" style="display: none;">
				<ul class="cl">
					<li class="mli"><label for="rushreply"><input type="checkbox" name="rushreply" id="rushreply" class="pc vm" value="1" {if $_GET['action'] == 'edit' && getstatus($thread['status'], 3)}disabled="disabled" checked="checked"{/if} /> {lang rushreply_change}</label></li>
					<li class="flex-box mli">
						<div class="tit">{lang thread_rushreply_start}</div>
						<div class=""><input type="text" name="rushreplyfrom" id="rushreplyfrom" class="px" autocomplete="off" value="{$postinfo['rush']['starttimefrom'] or ''}" onkeyup="getID('rushreply').checked = true;" /></div>
					</li>
					<li class="flex-box mli">
						<div class="tit">{lang thread_rushreply_over}</div>
						<div class=""><input type="text" autocomplete="off" id="rushreplyto" name="rushreplyto" class="px" value="{$postinfo['rush']['starttimeto'] or ''}" onkeyup="getID('rushreply').checked = true;" /></div>
					</li>
					<li class="flex-box mli">
						<div class="tit">{lang rushreply_rewardfloor} </div>
						<div class=""><input type="text" name="rewardfloor" id="rewardfloor" class="px oinf" value="{$postinfo[rush][rewardfloor] or ''}" onkeyup="$('rushreply').checked = true;" /></div>
					</li>
					<li class="mtit p10">{lang rushreply_rewardfloor_comment}</li>
					<li class="flex-box mli">
						<div class="tit">{lang stopfloor}: </div>
						<div class=""><input type="text" name="replylimit" id="replylimit" class="px" autocomplete="off" value="{$postinfo[rush][replylimit] or ''}" onkeyup="$('rushreply').checked = true;" /></div>
					</li>
					<li class="mtit p10">{lang replylimit} </li>
					<li class="flex-box mli">
						<div class="tit">{lang rushreply_end}</div>
						<div class=""><input type="text" name="stopfloor" id="stopfloor" class="px" autocomplete="off" value="{$postinfo[rush][stopfloor] or ''}" onkeyup="$('rushreply').checked = true;" /></div>
					</li>
					<li class="flex-box mli">
						<div class="tit"><!--{if !empty($_G['setting']['creditstransextra'][11])}-->{$_G['setting']['extcredits'][$_G['setting']['creditstransextra'][11]][title]}<!--{else}-->{lang credits}<!--{/if}-->{lang min_limit}: </div>
						<div class=""><input type="text" name="creditlimit" id="creditlimit" class="px" autocomplete="off" value="{$postinfo[rush][creditlimit] or ''}" onkeyup="$('rushreply').checked = true;" /></div>
					</li>
					<li class="mtit p10"><!--{if !empty($_G['setting']['creditstransextra'][11])}-->({$_G['setting']['extcredits'][$_G['setting']['creditstransextra'][11]][title]})<!--{else}-->{lang total_credits}<!--{/if}-->{lang post_rushreply_credit}</li>
				</ul>
			</div>
		<!--{/if}-->
		<!--{if $_G['group']['allowsetpublishdate'] && ($_GET['action'] == 'newthread' || ($_GET['action'] == 'edit' && $isfirstpost && $thread['displayorder'] == -4))}-->
			<div id="extra_pubdate_c" class="exfm cl" style="display: none;">
				<label><input type="checkbox" name="cronpublish" onclick="if(this.checked) {getID('cronpublishdate').click();doane(event,false);};hidenFollowBtn(this.checked);" id="cronpublish" value="true" class="pc"{if $cronpublish} checked="checked"{/if} />{lang post_timer}</label>
				<input type="text" name="cronpublishdate" id="cronpublishdate" class="px" autocomplete="off" value="{$cronpublishdate}" onchange="if(this.value) getID('cronpublish').checked = true;">
			</div>
		<!--{/if}-->
	<!--{/if}-->

	<div id="extra_additional_c" class="exfm p5 cl" style="display: none;">
		<li class="mtit">{lang basic_attr}</li>
		<ul class="flex-box flex-wrap p10 cl">
			<li class="mli flex-half">
				<!--{if $_GET['action'] != 'edit'}-->
					<!--{if $_G['group']['allowanonymous']}--><label for="isanonymous"><input type="checkbox" name="isanonymous" id="isanonymous" class="pc" value="1" />{lang post_anonymous}</label><!--{/if}-->
				<!--{else}-->
					<!--{if $_G['group']['allowanonymous'] || (!$_G['group']['allowanonymous'] && $orig['anonymous'])}--><label for="isanonymous"><input type="checkbox" name="isanonymous" id="isanonymous" class="pc" value="1" {if $orig['anonymous']}checked="checked"{/if} />{lang post_anonymous}</label><!--{/if}-->
				<!--{/if}-->
			</li>
			<!--{if $_GET['action'] == 'newthread' || $_GET['action'] == 'edit' && $isfirstpost}-->
			<li class="mli flex-half"><label for="hiddenreplies"><input type="checkbox" name="hiddenreplies" id="hiddenreplies" class="pc"{if $thread['hiddenreplies']} checked="checked"{/if} value="1">{lang hiddenreplies}</label></li>
			<!--{/if}-->
			<!--{if $_G['uid'] && ($_GET['action'] == 'newthread' || $_GET[action] == 'edit' && $isfirstpost) && $special != 3}-->
			<li class="mli flex-half"><label for="ordertype"><input type="checkbox" name="ordertype" id="ordertype" class="pc" value="1" $ordertypecheck />{lang post_descviewdefault}</label></li>
			<!--{/if}-->
			<!--{if ($_GET['action'] == 'newthread' || $_GET['action'] == 'edit' && $isfirstpost)}-->
			<li class="mli flex-half"><label for="allownoticeauthor"><input type="checkbox" name="allownoticeauthor" id="allownoticeauthor" class="pc" value="1"{if $allownoticeauthor} checked="checked"{/if} />{lang post_noticeauthor}</label></li>
			<!--{/if}-->
			<!--{if $_GET['action'] != 'edit' && helper_access::check_module('feed') && $_G['forum']['allowfeed']}-->
			<li class="mli flex-half"><label for="addfeed"><input type="checkbox" name="addfeed" id="addfeed" class="pc" value="1" $addfeedcheck>{lang addfeed}</label></li>
			<!--{/if}-->
			<li class="mli flex-half"><label for="usesig"><input type="checkbox" name="usesig" id="usesig" class="pc" value="1" {if !$_G['group']['maxsigsize']}disabled {else}$usesigcheck {/if}/>{lang post_show_sig}</label></li>
		</ul>
		<li class="mtit">{lang text_feature}</li>
		<ul class="flex-box flex-wrap p10 cl">
			<li class="mli flex-half">
			<!--{if ($_G['forum']['allowhtml'] || ($_GET['action'] == 'edit' && ($orig['htmlon'] & 1))) && $_G['group']['allowhtml']}-->
				<label for="htmlon"><input type="checkbox" name="htmlon" id="htmlon" class="pc" value="1" $htmloncheck />{lang post_html}</label>
			<!--{else}-->
				<label for="htmlon"><input type="checkbox" name="htmlon" id="htmlon" class="pc" value="0" $htmloncheck disabled="disabled" />{lang post_html}</label>
			<!--{/if}-->
			</li>
			<li class="mli flex-half">
				<label for="allowimgcode"><input type="checkbox" id="allowimgcode" class="pc" disabled="disabled"{if $_G['forum']['allowimgcode']} checked="checked"{/if} />{lang post_imgcode}</label>
			</li>
			<!--{if $_G['forum']['allowimgcode']}-->
			<li class="mli flex-half">
				<label for="allowimgurl"><input type="checkbox" id="allowimgurl" class="pc" checked="checked" />{lang post_imgurl}</label>
			</li>
			<!--{/if}-->
			<li class="mli flex-half">
				<label for="parseurloff"><input type="checkbox" name="parseurloff" id="parseurloff" class="pc" value="1" $urloffcheck />{lang disable}{lang post_parseurl}</label>
			</li>
			<li class="mli flex-half">
				<label for="smileyoff"><input type="checkbox" name="smileyoff" id="smileyoff" class="pc" value="1" $smileyoffcheck />{lang disable}{lang smilies}</label>
			</li>
			<li class="mli flex-half">
				<label for="bbcodeoff"><input type="checkbox" name="bbcodeoff" id="bbcodeoff" class="pc" value="1" $codeoffcheck />{lang disable}{lang discuzcode}</label>
			</li>
			<li class="mli flex-half">
			<!--{if $_G['group']['allowimgcontent']}-->
				<label for="imgcontent"><input type="checkbox" name="imgcontent" id="imgcontent" class="pc" value="1" $imgcontentcheck onclick="switchEditor(this.checked?0:1);$('e_switchercheck').checked=this.checked;" />{lang content_to_pic}</label>
			<!--{else}-->
				<label for="imgcontent"><input type="checkbox" name="imgcontent" id="imgcontent" class="pc" value="0" $imgcontentcheck disabled="disabled"/>{lang content_to_pic}</label>
			<!--{/if}-->
			</li>
		</ul>
			<!--{if $_GET['action'] == 'newthread' && $_G['forum']['ismoderator'] && ($_G['group']['allowdirectpost'] || !$_G['forum']['modnewposts'])}-->
				<!--{if $_GET['action'] == 'newthread' && $_G['forum']['ismoderator'] && ($_G['group']['allowdirectpost'] || !$_G['forum']['modnewposts']) && ($_G['group']['allowstickthread'] || $_G['group']['allowdigestthread'])}-->
				<li class="mtit">{lang manage_operation}</li>
				<ul class="flex-box flex-wrap p10 cl">
							<!--{if $_G['group']['allowstickthread']}-->
								<li class="mli flex-half"><label for="sticktopic"><input type="checkbox" name="sticktopic" id="sticktopic" class="pc" value="1" $stickcheck />{lang post_stick_thread}</label></li>
							<!--{/if}-->
							<!--{if $_G['group']['allowdigestthread']}-->
								<li class="mli flex-half"><label for="addtodigest"><input type="checkbox" name="addtodigest" id="addtodigest" class="pc" value="1" $digestcheck />{lang post_digest_thread}</label></li>
							<!--{/if}-->
				</ul>
				<!--{/if}-->
			<!--{elseif $_GET[action] == 'edit' && $_G['forum_auditstatuson']}-->
				<li class="mtit">{lang manage_operation}</li>
				<ul class="flex-box flex-wrap p10 cl">
					<li class="mli flex-half"><label for="audit"><input type="checkbox" name="audit" id="audit" class="pc" value="1">{lang auditstatuson}</label></li>
				</ul>
			<!--{/if}-->
	</div>
	<!--{hook/post_attribute_extra_body_mobile}-->
</div>
<script type="text/javascript">
	if($("#dhnavs_li .mon").length > 0) {
		var discuz_nav = $("#dhnavs_li .mon").offset().left + $("#dhnavs_li .mon").width() >= $(window).width() ? $("#dhnavs_li .mon").index() : 0;
	}else{
		var discuz_nav = 0;
	}	
	new Swiper('#dhnavs_li', {
		freeMode : true,
		slidesPerView : 'auto',
		initialSlide : discuz_nav,
		onTouchMove: function(swiper){
			Discuz_Touch_on = 0;
		},
		onTouchEnd: function(swiper){
			Discuz_Touch_on = 1;
		},
	});
	function showExtra(id) {
		if ($('#'+id+'_c').css('display') == 'block') {
			$('#'+id+'_b').attr("class","swiper-slide");
			$('#'+id+'_c').css({'display':'none'});
		} else {
			var extraButton = $('#post_extra_tb').children('li');
			var extraForm = $('#post_extra_c').children('div');
			
			$.each($('#post_extra_tb > li'), function(){
				$(this).attr("class","swiper-slide");
			});

			$.each($('#post_extra_c > div'), function(){
				if($(this).hasClass('exfm')) {
					$(this).css({'display':'none'});
				}
			});
			$('#'+id+'_b').addClass('mon');
			$('#'+id+'_c').css({'display':'block'});
		}
	}
</script>