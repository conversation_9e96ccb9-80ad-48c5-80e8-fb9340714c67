<!--{if $sortid}-->
	<input type="hidden" name="sortid" value="$sortid" />
<!--{/if}-->
<!--{if !$isfirstpost && $thread['special'] == 5 && empty($firststand) && $_GET['action'] != 'edit'}-->
<ul class="cl">
	<li class="mli">
		<select name="stand" id="stand" class="sort_sel pl5">
			<option value="">{lang debate_viewpoint}</option>
			<option value="0">{lang debate_neutral}</option>
			<option value="1"{if $stand == 1} selected="selected"{/if}>{lang debate_square}</option>
			<option value="2"{if $stand == 2} selected="selected"{/if}>{lang debate_opponent}</option>
		</select>
	</li>
</ul>
<!--{/if}-->

<!--{if $showthreadsorts}-->
	<div class="exfm cl">
		<!--{template forum/post_sortoption}-->
	</div>
<!--{elseif $adveditor}-->
	<!--{if $special == 1}--><!--{template forum/post_poll}-->
	<!--{elseif $special == 2 && ($_GET['action'] != 'edit' || ($_GET['action'] == 'edit' && ($thread['authorid'] == $_G['uid'] && $_G['group']['allowposttrade'] || $_G['group']['allowedittrade'])))}--><!--{template forum/post_trade}-->
	<!--{elseif $special == 3}--><!--{template forum/post_reward}-->
	<!--{elseif $special == 4}--><!--{template forum/post_activity}-->
	<!--{elseif $special == 5}--><!--{template forum/post_debate}-->
	<!--{elseif $specialextra}--><div class="specialpost s_clear">$threadplughtml</div>
	<!--{/if}-->
<!--{/if}-->