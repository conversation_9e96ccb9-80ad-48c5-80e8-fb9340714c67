<!--{template common/header}-->
<div class="header cl">
	<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div>
	<h2><a href="forum.php?mod=viewthread&tid=$_G['tid']{if $_GET['from']}&from=$_GET['from']{/if}">{lang trade_order}</a></h2>
	<div class="my"><a href="index.php"><i class="dm-house"></i></a></div>
</div>

<div class="viewthread">
	<div class="discuz_x cl"></div>
	<div class="txtlist cl"><div class="mtit cl"><!--{if !$tradelog['offline']}-->{lang trade_pay_alipay}<!--{else}-->{lang trade_pay_offline}<!--{/if}--></div></div>
	<form method="post" autocomplete="off" id="tradepost" name="tradepost" action="forum.php?mod=trade&orderid=$orderid">
	<div class="plc p15 cl">
	<!--{if !empty($_GET['modthreadkey']) && daddslashes($_GET['modthreadkey'])}-->
		<input type="hidden" name="modthreadkey" value="$_GET['modthreadkey']" />
	<!--{/if}-->
	<!--{if !empty($_GET['tid']) && daddslashes($_GET['tid'])}-->
		<input type="hidden" name="tid" value="$_GET['tid']" />
	<!--{/if}-->
		<input type="hidden" name="formhash" value="{FORMHASH}" />
		<ul class="post_box cl">
			<li class="flex-box mli">
				<div class="flex">{lang status}</div>
				<div class="flex-3"><span class="z">$tradelog['statusview']</span></div>
			</li>
			<li class="flex-box mli">
				<div class="flex"></div>
				<div class="flex-3"><span class="z">($tradelog['lastupdate'])</span></div>
			</li>
			<!--{if $tradelog['offline'] && $offlinenext}-->
			<li class="flex-box mli">
				<div class="flex">{lang trade_password}</div>
				<div class="flex-3"><input id="password" name="password" type="password" class="px" /></div>
			</li>
			<li class="flex-box mli mtext">
				<div class="flex"><textarea id="buyermsg" id="message" name="message" class="px pxbg flw_replybox" placeholder="{lang trade_message} {lang trade_seller_remark_comment}"></textarea></div>
			</li>
			<li class="flex-box mli mtit">$trade_message</li>
			<!--{/if}-->
		</ul>
		<!--{if $tradelog['offline'] && $offlinenext}-->
		<dd class="mt10 cl">
		<!--{loop $offlinenext $nextid $nextbutton}-->
			<button class="button mb10" type="button" onclick="getID('tradepost').offlinestatus.value = '$nextid';getID('offlinesubmit').click();"><em>$nextbutton</em></button>
		<!--{/loop}-->
		</dd>
		<input type="hidden" name="offlinestatus" value="" />
		<input type="submit" id="offlinesubmit" name="offlinesubmit" style="display: none" />
		<!--{/if}-->
		<!--{if trade_typestatus('successtrades', $tradelog['status']) || trade_typestatus('refundsuccess', $tradelog['status'])}-->
		<ul class="post_box cl">
			<li class="flex-box mli">
			<!--{if $tradelog['ratestatus'] == 3}-->
				<div class="flex"></div>
				<div class="flex-3">{lang eccredit_post_between}</div>
			</li>
			<!--{elseif ($_G['uid'] == $tradelog['buyerid'] && $tradelog['ratestatus'] == 1) || ($_G['uid'] == $tradelog['sellerid'] && $tradelog['ratestatus'] == 2)}-->
				<div class="flex"></div>
				<div class="flex-3">{lang eccredit_post_waiting}</div>
			</li>
			<!--{else}-->
				<!--{if ($_G['uid'] == $tradelog['buyerid'] && $tradelog['ratestatus'] == 2) || ($_G['uid'] == $tradelog['sellerid'] && $tradelog['ratestatus'] == 1)}-->
				<div class="flex"></div>
				<div class="flex-3">{lang eccredit_post_already}</div>
			</li>
				<!--{else}-->
			</li>
				<!--{/if}-->
		</ul>
		<dd>
				<!--{if $_G['uid'] == $tradelog['buyerid']}-->
			<a class="button media_tips dialog" href="home.php?mod=spacecp&ac=eccredit&op=rate&orderid=$tradelog['orderid']&type=0">{lang eccredit1}</a>
				<!--{elseif $_G['uid'] == $tradelog['sellerid']}-->
			<a class="button media_tips dialog" href="home.php?mod=spacecp&ac=eccredit&op=rate&orderid=$tradelog['orderid']&type=1">{lang eccredit1}</a>
				<!--{/if}-->
		</dd>
		<ul class="post_box cl">
			<!--{/if}-->
		</ul>
		<!--{elseif !$tradelog['offline']}-->
			<dd>
				{lang trade_online_tradeurl}
				<!--{if $tradelog['status'] == 0 && $tradelog['buyerid'] == $_G['uid']}-->
					<!--{if $tradelog['tenpayaccount']}-->
						<button class="pn" type="button" name="" onclick="window.open('forum.php?mod=trade&orderid=$orderid&pay=yes&apitype=tenpay','','')"><span>{lang trade_online_tenpay}</span></button>
					<!--{/if}-->
				<!--{else}-->
					<!--{if $tradelog['paytype'] == 1}-->
						<button class="pn" type="button" onclick="window.open('$loginurl', '', '')"><span>{lang trade_order_status}</span></button>
					<!--{/if}-->
					<!--{if $tradelog['paytype'] == 2}-->
						<button class="pn" type="button" onclick="window.open('$loginurl', '', '')"><span>{lang tenpay_trade_order_status}</span></button>
					<!--{/if}-->
				<!--{/if}-->
			</dd>
		<!--{/if}-->
	</div>
	<div class="discuz_x cl"></div>
	<div class="txtlist cl"><div class="mtit cl">{lang trade_order}</div></div>
	<div class="plc p15 cl">
		<ul class="post_box cl">
			<li class="flex-box mli">
				<div class="flex xs1">{lang post_trade_name}</div>
				<div class="flex-3"><a href="forum.php?mod=viewthread&tid=$trade['tid']&do=tradeinfo&pid=$trade['pid']{if !empty($_GET['modthreadkey'])}&modthreadkey=$_GET['modthreadkey']{/if}">$tradelog['subject']</a></div>
			</li>
			<!--{if $trade['price'] > 0}-->
			<li class="flex-box mli">
				<div class="flex xs1">{lang post_trade_price}</div>
				<div class="flex-3">&yen; <em class="xi1">$tradelog['price']</em>&nbsp;{lang payment_unit}</div>
			</li>
			<!--{/if}-->
			<!--{if $_G['setting']['creditstransextra'][5] != -1 && $tradelog['credit']}-->
			<li class="flex-box mli">
				<div class="flex xs1"></div>
				<div class="flex-3"><!--{if $tradelog['price'] > 0}-->{lang trade_additional} <!--{/if}--><em class="xi1">$tradelog['credit']</em>&nbsp;{$_G[setting]['extcredits'][$_G['setting']['creditstransextra'][5]]['unit']}{$_G[setting]['extcredits'][$_G['setting']['creditstransextra'][5]]['title']}</div>
			</li>
			<!--{/if}-->
			<!--{if $tradelog['status'] == 0}--><li class="flex-box mli mtit">{lang trade_payment_comment}</li><!--{/if}-->
			<!--{if $tradelog['locus']}-->
			<li class="flex-box mli">
				<div class="flex xs1">{lang post_trade_locus}</div>
				<div class="flex-3">$tradelog['locus']</div>
			</li>
			<!--{/if}-->
			<li class="flex-box mli">
				<div class="flex xs1">{lang trade_seller}</div>
				<div class="flex-3"><a href="home.php?mod=space&uid=$tradelog['sellerid']" target="_blank">$tradelog['seller']</a></div>
			</li>
			<li class="flex-box mli">
				<div class="flex xs1">{lang trade_buyer}</div>
				<div class="flex-3"><a href="home.php?mod=space&uid=$tradelog['buyerid']" target="_blank">$tradelog['buyer']</a></div>
			</li>
			<!--{if $tradelog['tradeno']}-->
			<li class="flex-box mli">
				<div class="flex xs1">{lang trade_order_no}</div>
				<div class="flex-3"><a href="$loginurl" class="xg2" target="_blank">$tradelog['tradeno']</a></div>
			</li>
			<!--{/if}-->
			<!--{if $tradelog['status'] == 0 && $tradelog['sellerid'] == $_G['uid']}-->
			<li class="flex-box mli">
				<div class="flex xs1">{lang trade_baseprice}</div>
				<div class="flex-3"><input type="text" id="newprice" name="newprice" value="$tradelog['baseprice']" class="px" /></div>
			</li>
			<!--{if $_G['setting']['creditstransextra'][5] != -1 && $tradelog[credit]}-->
			<li class="flex-box mli">
				<div class="flex xs1">{$_G['setting']['extcredits'][$_G['setting']['creditstransextra'][5]]['title']}</div>
				<div class="flex-2"><input type="text" id="newcredit" name="newcredit" value="$tradelog['basecredit']" class="px" /></div>
				<div class="flex"><span class="y xg1">{$_G['setting']['extcredits'][$_G['setting']['creditstransextra'][5]]['unit']}</span></div>
			</li>
			<!--{/if}-->
			<!--{/if}-->
			<li class="flex-box mli">
				<div class="flex xs1">{lang trade_nums}</div>
				<div class="flex-2"><!--{if $tradelog['status'] == 0 && $tradelog['buyerid'] == $_G['uid']}--><input type="text" id="newnumber" name="newnumber" value="$tradelog[number]" class="px" /><!--{else}-->$tradelog['number']<!--{/if}--></div>
				<div class="flex"><span class="y xg1">{$_G['setting']['extcredits'][$_G['setting']['creditstransextra'][5]]['unit']}</span></div>
			</li>
			<li class="flex-box mli">
				<div class="flex xs1">{lang post_trade_transport}</div>
				<div class="flex-3">
					<!--{if $tradelog['transport'] == 0}-->{lang post_trade_transport_offline}<!--{/if}-->
					<!--{if $tradelog['transport'] == 1}-->{lang post_trade_transport_seller}<!--{/if}-->
					<!--{if $tradelog['transport'] == 2}-->{lang post_trade_transport_buyer}<!--{/if}-->
					<!--{if $tradelog['transport'] == 3}-->{lang post_trade_transport_virtual}<!--{/if}-->
					<!--{if $tradelog['transport'] == 4}-->{lang post_trade_transport_physical}<!--{/if}-->
				</div>
			</li>
			<!--{if $tradelog['transport']}-->
			<li class="flex-box mli">
				<div class="flex xs1">{lang trade_transportfee}</div>
				<div class="flex-2"><!--{if $tradelog['status'] == 0 && $tradelog['sellerid'] == $_G['uid']}--><input type="text" name="newfee" value="$tradelog['transportfee']" class="px" placeholder="" /><!--{else}-->$tradelog['transportfee']<!--{/if}--></div>
				<div class="flex"><span class="y xg1">{lang payment_unit}</span></div>
			</li>
			<!--{/if}-->
			<!--{if $tradelog['transport'] != 3}-->
			<li class="flex-box mli">
				<div class="flex xs1">{lang trade_buyername}</div>
				<div class="flex-3"><!--{if $tradelog['status'] == 0 && $tradelog['buyerid'] == $_G['uid']}--><input type="text" id="newbuyername" name="newbuyername" value="$tradelog['buyername']" maxlength="50" class="px" /><!--{else}-->$tradelog['buyername']<!--{/if}--></div>
			</li>
			<li class="flex-box mli">
				<div class="flex xs1">{lang trade_buyercontact}</div>
				<div class="flex-3"><!--{if $tradelog['status'] == 0 && $tradelog['buyerid'] == $_G['uid']}--><input type="text" id="newbuyercontact" name="newbuyercontact" value="$tradelog['buyercontact']" maxlength="100" class="px" /><!--{else}-->$tradelog['buyercontact']<!--{/if}--></div>
			</li>
			<li class="flex-box mli">
				<div class="flex xs1">{lang trade_buyerzip}</div>
				<div class="flex-3"><!--{if $tradelog['status'] == 0 && $tradelog['buyerid'] == $_G['uid']}--><input type="text" id="newbuyerzip" name="newbuyerzip" value="$tradelog['buyerzip']" maxlength="10" class="px" /><!--{else}-->$tradelog['buyerzip']<!--{/if}--></div>
			</li>
			<li class="flex-box mli">
				<div class="flex xs1">{lang trade_buyerphone}</div>
				<div class="flex-3"><!--{if $tradelog['status'] == 0 && $tradelog['buyerid'] == $_G['uid']}--><input type="text" id="newbuyerphone" name="newbuyerphone" value="$tradelog['buyerphone']" maxlength="20" class="px" /><!--{else}-->$tradelog['buyerphone']<!--{/if}--></div>
			</li>
			<li class="flex-box mli">
				<div class="flex xs1">{lang trade_buyermobile}</div>
				<div class="flex-3"><!--{if $tradelog['status'] == 0 && $tradelog['buyerid'] == $_G['uid']}--><input type="text" id="newbuyermobile" name="newbuyermobile" value="$tradelog['buyermobile']" maxlength="20" class="px" /><!--{else}-->$tradelog['buyermobile']<!--{/if}--></div>
			</li>
			<!--{else}-->
				<input type="hidden" name="newbuyername" value="" />
				<input type="hidden" name="newbuyercontact" value="" />
				<input type="hidden" name="newbuyerzip" value="" />
				<input type="hidden" name="newbuyerphone" value="" />
				<input type="hidden" name="newbuyermobile" value="" />
			<!--{/if}-->
			<li class="flex-box mli mtext">
				<div class="flex xs1">{lang trade_seller_remark}</div>
				<div class="flex-3"><!--{if $tradelog['status'] == 0 && $tradelog['buyerid'] == $_G['uid']}--><textarea id="newbuyermsg" name="newbuyermsg" rows="5" class="pt">$tradelog['buyermsg']</textarea><!--{else}-->$tradelog['buyermsg']<!--{/if}--></div>
			</li>
		</ul>
		<!--{if $tradelog['status'] == 0 && ($_G['uid'] == $tradelog['sellerid'] || $_G['uid'] == $tradelog['buyerid'])}-->
		<dd>
			<button class="pn" type="submit" name="tradesubmit" value="true"><span>{lang trade_submit_order}</span></button>
		</dd>
		<!--{/if}-->
	</div>
	</form>

	<!--{if $tradelog['offline'] && !empty($messagelist)}-->
	<div class="discuz_x cl"></div>
	<div class="txtlist cl"><div class="mtit cl">{lang trade_message}</div></div>
	<div class="plc p15 cl">
			<div class="xld xlda">
			<!--{loop $messagelist $message}-->
				<dl class="bbda cl">
					<dd class="m avt"><!--{avatar($message[0], 'small')}--></dd>
					<dt><a href="home.php?mod=space&uid=$message[0]" target="_blank">$message[1]</a>&nbsp;<span class="xg1 xw0">$message[2]</span></dt>
					<dd>$message[3]</dd>
				</dl>
			<!--{/loop}-->
			</div>
	</div>
	<!--{/if}-->
	<!--{if $usertrades}-->
	<div class="discuz_x cl"></div>
	<div class="txtlist cl"><div class="mtit cl">$trade['seller'] {lang trade_recommended_goods}</div></div>
	<div class="plc p15 cl">
		<div class="xld cl">
			<!--{loop $usertrades $usertrade}-->
			<dl class="cl">
				<dd class="m">
					<a href="forum.php?mod=viewthread&tid=$usertrade['tid']&do=tradeinfo&pid=$usertrade['pid']{if !empty($_GET['modthreadkey'])}&modthreadkey=$_GET['modthreadkey']{/if}" class="tn" style="text-decoration:none !important;">
						<!--{if $usertrade['aid']}--><img src="{echo getforumimg($usertrade['aid'])}" width="60" /><!--{else}--><div class="nophoto" style="width:60px;height:60px;line-height:60px;border:1px solid #EEE;"></div><!--{/if}-->
					</a>
				</dd>
				<dt><a href="forum.php?mod=viewthread&tid=$usertrade['tid']&do=tradeinfo&pid=$usertrade['pid']{if !empty($_GET['modthreadkey'])}&modthreadkey=$_GET['modthreadkey']{/if}">$usertrade['subject']</a></dt>
				<dd>
					<!--{if $usertrade[price] > 0}-->
					<p class="p">&yen; <em class="xi1">$usertrade['price']</em></p>
					<!--{/if}-->
					<!--{if $_G['setting']['creditstransextra'][5] != -1 && $usertrade['credit']}-->
					<p class="{if $usertrade['price'] > 0}xg1{else}p{/if}"><!--{if $usertrade['price'] > 0}-->{lang trade_additional} <!--{/if}--><em class="xi1">$usertrade['credit']</em>&nbsp;{$_G[setting]['extcredits'][$_G['setting']['creditstransextra'][5]]['unit']}{$_G[setting]['extcredits'][$_G['setting']['creditstransextra'][5]]['title']}</p>
					<!--{/if}-->
				</dd>
			</dl>
			<!--{/loop}-->
		</div>
	</div>
	<!--{/if}-->
</div>
<!--{template common/footer}-->