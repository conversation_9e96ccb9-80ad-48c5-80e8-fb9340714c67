<!--{eval $_G['forum_thread']['special'] = 0;}-->
<!-- main postlist start -->
	<!--{eval $needhiddenreply = ($hiddenreplies && $_G['uid'] != $post['authorid'] && $_G['uid'] != $_G['forum_thread']['authorid'] && !$post['first'] && !$_G['forum']['ismoderator']);}-->
	<div class="plc cl">
		<span class="avatar"><img src="<!--{avatar($post['authorid'], 'small', true)}-->" style="width:32px;height:32px;" /></span>
		<div class="pi">
			<ul class="authi">
				<li class="grey">
					<em>
						<!--{if isset($post['isstick'])}-->
							<img src ="{IMGDIR}/settop.png" class="vm" /> {lang from} {$post['number']}{$postnostick}
						<!--{elseif $post['number'] == -1}-->
							{lang recommend_post}
						<!--{else}-->
							<!--{if !empty($postno[$post['number']])}-->$postno[$post['number']]<!--{else}-->{$post['number']}{$postno[0]}<!--{/if}-->
						<!--{/if}-->
					</em><b>
					<!--{if $post['authorid'] && $post['username'] && !$post['anonymous']}-->
						<a href="home.php?mod=space&uid=$post['authorid']" class="blue">$post['author']</a></b>
						<!--{if $post['authorid'] != $_G['uid'] && $_G['uid']}-->
						<a href="forum.php?mod=post&action=reply&fid=$_G['fid']&tid=$_G['tid']&repquote=$post['pid']&extra=$_GET['extra']&page=$page" class="xg1">{lang reply}</a>
						<!--{/if}-->
					<!--{else}-->
						<!--{if !$post['authorid']}-->
						<a href="javascript:;">{lang guest} <em>$post['useip']{if $post['port']}:$post['port']{/if}</em></a>
						<!--{elseif $post['authorid'] && $post['username'] && $post['anonymous']}-->
						<!--{if $_G['forum']['ismoderator']}--><a href="home.php?mod=space&uid=$post['authorid']" target="_blank">{$_G['setting']['anonymoustext']}</a><!--{else}-->{$_G['setting']['anonymoustext']}<!--{/if}-->
						<!--{else}-->
						$post['author'] <em>{lang member_deleted}</em>
						<!--{/if}-->
					<!--{/if}-->
				</li>
				<li class="grey rela">
					$post['dateline']
				</li>
			</ul>
			<div class="message">
					<!--{if $post['warned']}-->
						<span class="grey quote">{lang warn_get}</span>
					<!--{/if}-->
					<!--{if !$post['first'] && !empty($post['subject'])}-->
						<h2><strong>$post['subject']</strong></h2>
					<!--{/if}-->
					<!--{if $_G['adminid'] != 1 && $_G['setting']['bannedmessages'] & 1 && (($post['authorid'] && !$post['username']) || ($post['groupid'] == 4 || $post['groupid'] == 5) || $post['status'] == -1 || $post['memberstatus'])}-->
						<div class="grey quote">{lang message_banned}</div>
					<!--{elseif $_G['adminid'] != 1 && $post['status'] & 1}-->
						<div class="grey quote">{lang message_single_banned}</div>
					<!--{elseif $needhiddenreply}-->
						<div class="grey quote">{lang message_ishidden_hiddenreplies}</div>
					<!--{elseif $post['first'] && $_G['forum_threadpay']}-->
						<!--{template forum/viewthread_pay}-->
					<!--{else}-->

						<!--{if $_G['setting']['bannedmessages'] & 1 && (($post['authorid'] && !$post['username']) || ($post['groupid'] == 4 || $post['groupid'] == 5))}-->
							<div class="grey quote">{lang admin_message_banned}</div>
						<!--{elseif $post['status'] & 1}-->
							<div class="grey quote">{lang admin_message_single_banned}</div>
						<!--{/if}-->
						<!--{if $_G['forum_thread']['price'] > 0 && $_G['forum_thread']['special'] == 0}-->
							{lang pay_threads}: <strong>$_G['forum_thread']['price'] {$_G['setting']['extcredits'][$_G['setting']['creditstransextra'][1]]['unit']}{$_G['setting']['extcredits'][$_G['setting']['creditstransextra'][1]]['title']} </strong> <a href="forum.php?mod=misc&action=viewpayments&tid=$_G['tid']" >{lang pay_view}</a>
						<!--{/if}-->

						<!--{if $post['first'] && $threadsort && $threadsortshow}-->
							<!--{if $threadsortshow['optionlist'] && !($post['status'] & 1) && !$_G['forum_threadpay']}-->
								<!--{if $threadsortshow['optionlist'] == 'expire'}-->
									{lang has_expired}
								<!--{else}-->
									<div class="box_ex2 viewsort">
										<h4>$_G['forum']['threadsorts']['types'][$_G['forum_thread']['sortid']]</h4>
									<!--{loop $threadsortshow['optionlist'] $option}-->
										<!--{if $option['type'] != 'info'}-->
											$option['title']: <!--{if $option['value']}-->$option['value'] $option['unit']<!--{else}--><span class="xg1">--</span><!--{/if}--><br />
										<!--{/if}-->
									<!--{/loop}-->
									</div>
								<!--{/if}-->
							<!--{/if}-->
						<!--{/if}-->
						<!--{if $post['first']}-->
							<!--{if !$_G['forum_thread']['special']}-->
								$post['message']
							<!--{elseif $_G['forum_thread']['special'] == 1}-->
								<!--{template forum/viewthread_poll}-->
							<!--{elseif $_G['forum_thread']['special'] == 2}-->
								<!--{template forum/viewthread_trade}-->
							<!--{elseif $_G['forum_thread']['special'] == 3}-->
								<!--{template forum/viewthread_reward}-->
							<!--{elseif $_G['forum_thread']['special'] == 4}-->
								<!--{template forum/viewthread_activity}-->
							<!--{elseif $_G['forum_thread']['special'] == 5}-->
								<!--{template forum/viewthread_debate}-->
							<!--{elseif $threadplughtml}-->
								$threadplughtml
								$post['message']
							<!--{else}-->
								$post['message']
							<!--{/if}-->
						<!--{else}-->
							$post['message']
						<!--{/if}-->
						
					<!--{/if}-->
			</div>
			<!--{if $_G['setting']['mobile']['mobilesimpletype'] == 0}-->
			<!--{if $post['attachment']}-->
				<div class="grey quote">
				{lang attachment}: <em><!--{if $_G['uid']}-->{lang attach_nopermission}<!--{else}-->{lang attach_nopermission_login}<!--{/if}--></em>
				</div>
			<!--{elseif $post['imagelist'] || $post['attachlist']}-->
				<!--{if $post['imagelist']}-->
				<ul class="img_one">{echo showattach($post, 1)}</ul>
				<!--{/if}-->
				<!--{if $post['attachlist']}-->
				<ul class="post_attlist">{echo showattach($post)}</ul>
				<!--{/if}-->
			<!--{/if}-->
			<!--{/if}-->
			
			<!--{if $_GET['from'] != 'preview' && !empty($post['ratelog'])}-->
				<h3 class="psth xs1"><span class="icon_ring vm"></span>{lang rate}</h3>
				<dl id="ratelog_$post[pid]" class="rate{if !empty($_G['cookie']['ratecollapse'])} rate_collapse{/if}">
					<!--{if $_G['setting']['ratelogon']}-->
						<dd style="margin:0">
					<!--{else}-->
						<dt>
							<!--{if !empty($postlist[$post[pid]]['totalrate'])}-->
								<strong><a href="forum.php?mod=misc&action=viewratings&tid=$_G['tid']&pid=$post['pid']" class="dialog"><!--{echo count($postlist[$post['pid']]['totalrate']);}--></a></strong>
								<p><a href="forum.php?mod=misc&action=viewratings&tid=$_G['tid']&pid=$post['pid']" class="dialog">{lang rate_view}</a></p>
							<!--{/if}-->
						</dt>
						<dd>
					<!--{/if}-->
						<div id="post_rate_$post['pid']"></div>
						<!--{if $_G['setting']['ratelogon']}-->
							<table class="ratl">
								<tr>
									<th class="xw1" width="120"><a href="forum.php?mod=misc&action=viewratings&tid=$_G['tid']&pid=$post['pid']" class="dialog"> {lang number_of_participants} <span class="xi1"><!--{echo count($postlist[$post['pid']]['totalrate']);}--></span></a></th>
									<!--{loop $post['ratelogextcredits'] $id $score}-->
										<!--{if $score > 0}-->
											<th class="xw1" width="80">{$_G['setting']['extcredits'][$id]['title']} <i><span class="xi1">+$score</span></i></th>
										<!--{else}-->
											<th class="xw1" width="80">{$_G['setting']['extcredits'][$id]['title']} <i><span class="xi1">$score</span></i></th>
										<!--{/if}-->
									<!--{/loop}-->
									<th>
										<a href="javascript:;" onclick="toggleRatelogCollapse('ratelog_$post[pid]', this);" class="y xi2 op"><!--{if !empty($_G['cookie']['ratecollapse'])}-->{lang open}<!--{else}-->{lang pack}<!--{/if}--></a>
										<i class="txt_h">{lang reason}</i>
									</th>
								</tr>
								<tbody class="ratl_l">
									<!--{loop $post['ratelog'] $uid $ratelog}-->
									<tr id="rate_{$post[pid]}_{$uid}">
										<td>
											<a href="home.php?mod=space&uid=$uid" target="_blank"><!--{echo avatar($uid, 'small');}--></a> <a href="home.php?mod=space&uid=$uid" target="_blank">$ratelog[username]</a>
										</td>
										<!--{loop $post['ratelogextcredits'] $id $score}-->
											<!--{if $ratelog['score'][$id] > 0}-->
												<td class="xi1"> + $ratelog[score][$id]</td>
											<!--{else}-->
												<td class="xg1">$ratelog[score][$id]</td>
											<!--{/if}-->
										<!--{/loop}-->
										<td class="xg1">$ratelog[reason]</td>
									</tr>
									<!--{/loop}-->
								</tbody>
							</table>
							<p class="ratc">
								<a href="forum.php?mod=misc&action=viewratings&tid=$_G['tid']&pid=$post['pid']" class="xi2 dialog">{lang rate_view}</a>
							</p>
						<!--{else}-->
							<ul class="cl">
								<!--{loop $post['ratelog'] $uid $ratelog}-->
									<li>
										<p id="rate_{$post[pid]}_{$uid}" onmouseover="showTip(this)" tip="<strong>$ratelog['reason']</strong>&nbsp;
												<!--{loop $ratelog['score'] $id $score}-->
													<!--{if $score > 0}-->
														<em class='xi1'>{$_G['setting']['extcredits'][$id]['title']} + $score $_G['setting']['extcredits'][$id]['unit']</em>
													<!--{else}-->
														<span>{$_G['setting']['extcredits'][$id]['title']} $score $_G['setting']['extcredits'][$id]['unit']</span>
													<!--{/if}-->
												<!--{/loop}-->" class="mtn mbn"><a href="home.php?mod=space&uid=$uid" target="_blank" class="avt"><!--{echo avatar($uid, 'small');}--></a></p>
										<p><a href="home.php?mod=space&uid=$uid" target="_blank">$ratelog['username']</a></p>
									</li>
								<!--{/loop}-->
							</ul>
						<!--{/if}-->
					</dd>
				</dl>
			<!--{else}-->
				<div id="post_rate_div_$post['pid']"></div>
			<!--{/if}-->
		</div>
	</div>
<!-- main postlist end -->
