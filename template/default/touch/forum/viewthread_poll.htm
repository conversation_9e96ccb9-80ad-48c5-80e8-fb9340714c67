<div id="postmessage_$post['pid']" class="postmessage">$post['message']</div>
<div class="poll cl">
<form id="poll" name="poll" method="post" autocomplete="off" action="forum.php?mod=misc&action=votepoll&fid=$_G['fid']&tid=$_G['tid']&pollsubmit=yes{if $_GET['from']}&from=$_GET['from']{/if}&quickforward=yes&mobile=2" >
	<input type="hidden" name="formhash" value="{FORMHASH}" />
	<div class="poll_txt">
		<!--{if $multiple}-->{lang poll_multiple}{lang thread_poll}<!--{if $maxchoices}-->: ( {lang poll_more_than} )<!--{/if}--><!--{else}-->{lang poll_single}{lang thread_poll}<!--{/if}--><!--{if $visiblepoll && $_G['group']['allowvote']}--> , {lang poll_after_result}<!--{/if}-->, {lang poll_voterscount}
	</div>
	<!--{if $_G['forum_thread']['remaintime']}-->
	<div class="poll_txt">
		{lang poll_count_down}:
		<!--{if $_G['forum_thread']['remaintime'][0]}-->$_G['forum_thread']['remaintime'][0] {lang days} <!--{/if}-->
		<!--{if $_G['forum_thread']['remaintime'][1]}-->$_G['forum_thread']['remaintime'][1] {lang poll_hour} <!--{/if}-->
		$_G['forum_thread']['remaintime'][2] {lang poll_minute}
	</div>
	<!--{elseif $expiration && $expirations < TIMESTAMP}-->
	<div class="poll_txt">{lang poll_end}</div>
	<!--{/if}-->
	<!--{if !$visiblepoll && ($overt || $_G['adminid'] == 1 || $thread['authorid'] == $_G['uid']) && $post['invisible'] == 0}-->
		<div class="poll_txt"><a href="forum.php?mod=misc&action=viewvote&tid=$_G['tid']" class="dialog">{lang poll_view_voters}</a></div>
	<!--{/if}-->
	<div class="poll_box">
	<!--{if $isimagepoll}-->
			<!--{loop $polloptions $key $option}-->
			<!--{eval $imginfo=$option['imginfo'];}-->
			<p>
			<!--{if $imginfo}-->
				<a href="javascript:;" class="mimg vm">
					<img src="$imginfo['small']" alt="$imginfo['filename']" />
				</a>
			<!--{else}-->
				<a href="javascript:;" class="mimg vm"><img src="{STATICURL}image/common/nophoto.gif" alt="$imginfo['filename']" /></a>
			<!--{/if}-->
			<!--{if $_G['group']['allowvote']}-->
				<input type="$optiontype" id="option_$key" name="pollanswers[]" value="$option['polloptionid']" {if $_G['forum_thread']['is_archived']}disabled="disabled"{/if}  />
			<!--{/if}-->
			<label for="option_$key">$key.$option['polloption']</label>
			<!--{if !$visiblepoll}-->
				<em style="color:#$option['color']" class="y">$option['percent']% ($option['votes']{lang debate_poll})</em>
			<!--{/if}-->
			</p>
			<hr class="l">
			<!--{/loop}-->
		</tr>	
	<!--{else}-->
		<!--{loop $polloptions $key $option}-->
			<p>
			<!--{if $_G['group']['allowvote']}-->
				<input type="$optiontype" id="option_$key" name="pollanswers[]" value="$option['polloptionid']" {if $_G['forum_thread']['is_archived']}disabled="disabled"{/if}  />
			<!--{/if}-->
			<label for="option_$key">$key.$option['polloption']</label>
			<!--{if !$visiblepoll}-->
				<em style="color:#$option['color']" class="y">$option['percent']% ($option['votes']{lang debate_poll})</em>
			<!--{/if}-->
			</p>
			<hr class="l">
		<!--{/loop}-->
	<!--{/if}-->
		<!--{if $_G['group']['allowvote'] && !$_G['forum_thread']['is_archived']}-->
			<input type="submit" name="pollsubmit" id="pollsubmit" value="{lang submit}" class="formdialog btn_pn" />
			<!--{if $overt}-->
				<span class="xg2">{lang poll_msg_overt}</span>
			<!--{/if}-->
		<!--{elseif !$allwvoteusergroup}-->
			<!--{if !$_G['uid']}-->
			<span class="xi1">{lang poll_msg_allwvote_user}</span>
			<!--{else}-->
			<span class="xi1">{lang poll_msg_allwvoteusergroup}</span>
			<!--{/if}-->
		<!--{elseif !$allowvotepolled}-->
			<span class="xi1">{lang poll_msg_allowvotepolled}</span>
		<!--{elseif !$allowvotethread}-->
			<span class="xi1">{lang poll_msg_allowvotethread}</span>
		<!--{/if}-->
	</div>
</form>
</div>