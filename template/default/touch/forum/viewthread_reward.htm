<div class="reward cl">
	<!--{if $_G['forum_thread']['price'] > 0 && !$_G['forum_thread']['is_archived']}-->
	<a href="forum.php?mod=post&action=reply&fid=$_G['fid']&tid=$_G['tid']{if $_GET['from']}&from=$_GET['from']{/if}" class="mhd pns">{lang reward_answer}</a>
	<!--{/if}-->
	<div class="reward_txt">{lang thread_reward}<strong>$rewardprice</strong>{$_G['setting']['extcredits'][$_G['setting']['creditstransextra'][2]]['unit']}{$_G['setting']['extcredits'][$_G['setting']['creditstransextra'][2]]['title']} {if $_G['forum_thread']['price'] > 0}<span>{lang unresolved}</span>{elseif $_G['forum_thread']['price'] < 0}<span>{lang resolved}</span>{/if}</div>
</div>
<!--{if $bestpost}-->
	<div class="rwdbst">
		<h3><a href="forum.php?mod=redirect&goto=findpost&ptid=$bestpost['tid']&pid=$bestpost['pid']">{lang view_full_content}</a>{lang reward_bestanswer}</h3>
		<div class="pstl">
			<div class="muser">				
				$bestpost['avatar']				
				<a href="home.php?mod=space&uid=$bestpost['authorid']">$bestpost['author']</a>				
			</div>
			<div class="mtxt"><!--{if $hiddenreplies && $_G['uid'] != $bestpost['authorid'] && $_G['uid'] != $_G['forum_thread']['authorid'] && !$_G['forum']['ismoderator']}-->{lang message_ishidden_hiddenreplies}<!--{else}-->$bestpost[message]<!--{/if}--></div>
		</div>
	</div>
<!--{/if}-->
<!--{if $post['attachment']}-->
	<div class="quote">{lang attachment}: <em><!--{if $_G['uid']}-->{lang attach_nopermission}<!--{else}-->{lang attach_nopermission_login}<!--{/if}--></em></div>
<!--{elseif $post['imagelist'] || $post['attachlist']}-->
	<!--{if $post['imagelist']}-->
		 {echo showattach($post, 1)}
	<!--{/if}-->
	<!--{if $post['attachlist']}-->
		 {echo showattach($post)}
	<!--{/if}-->
<!--{/if}-->
<!--{eval $post['attachment'] = $post['imagelist'] = $post['attachlist'] = '';}-->
<div id="postmessage_$post['pid']" class="postmessage">$post['message']</div>