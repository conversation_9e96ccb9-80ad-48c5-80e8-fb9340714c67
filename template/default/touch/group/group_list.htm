<div class="dhnav_box">
	<div id="dhnav">
		<div id="dhnav_li">
		<ul class="flex-box">
			<li class="flex mon"><a href="forum.php?mod=forumdisplay&action=list&fid=$_G['fid']">{lang forum_viewall}</a></li>
			<li class="flex"><a href="forum.php?mod=group&action=memberlist&fid=$_G['fid']">{lang group_member_list}</a></li>
			<!--{if $_G['forum']['ismoderator']}--><li class="flex"><a href="forum.php?mod=group&action=manage&fid=$_G['fid']">{lang group_admin}</a></li><!--{/if}-->
		</ul>
		</div>
	</div>
</div>
<!--{if $_G['forum']['threadtypes']}-->
<div class="dhnavs_box">
	<div id="dhnavs">
		<div id="dhnavs_li">
			<ul class="swiper-wrapper">
				<!--{if $_G['forum']['threadtypes']}-->
					<!--{if $_G['forum']['threadtypes']}-->
						<!--{loop $_G['forum']['threadtypes']['types'] $id $name}-->
							<li class="swiper-slide{if $_GET['typeid'] == $id} mon{/if}"><a href="forum.php?mod=forumdisplay&fid=$_G['fid']&filter=typeid&typeid=$id$forumdisplayadd['typeid']{if $_GET['archiveid']}&archiveid={$_GET['archiveid']}{/if}">$name</a></li>
						<!--{/loop}-->
					<!--{/if}-->
				<!--{/if}-->
				<!--{if $_G['forum']['threadsorts']}-->
					<!--{loop $_G['forum']['threadsorts']['types'] $id $name}-->
						<!--{if $_GET['sortid'] == $id}-->
						<li class="swiper-slide mon"><a href="forum.php?mod=forumdisplay&fid=$_G['fid']{if $_GET['typeid']}&filter=typeid&typeid=$_GET['typeid']{/if}{if $_GET['archiveid']}&archiveid={$_GET['archiveid']}{/if}">$name</a></li>
						<!--{else}-->
						<li class="swiper-slide"><a href="forum.php?mod=forumdisplay&fid=$_G['fid']&filter=sortid&sortid=$id$forumdisplayadd['sortid']{if $_GET['archiveid']}&archiveid={$_GET['archiveid']}{/if}">$name</a></li>
						<!--{/if}-->
					<!--{/loop}-->
				<!--{/if}-->
			</ul>
		</div>
	</div>
</div>
<script>
	if($("#dhnavs_li .mon").length > 0) {
		var discuz_nav = $("#dhnavs_li .mon").offset().left + $("#dhnavs_li .mon").width() >= $(window).width() ? $("#dhnavs_li .mon").index() : 0;
	}else{
		var discuz_nav = 0;
	}	
	new Swiper('#dhnavs_li', {
		freeMode : true,
		slidesPerView : 'auto',
		initialSlide : discuz_nav,
		onTouchMove: function(swiper){
			Discuz_Touch_on = 0;
		},
		onTouchEnd: function(swiper){
			Discuz_Touch_on = 1;
		},
	});
</script>
<!--{/if}-->
<form class="searchform" method="post" autocomplete="off" action="search.php?mod=group&srchfid=$_G['fid']">
	<input type="hidden" name="formhash" value="{FORMHASH}" />
	<div class="search flex-box">
		<input value="$keyword" autocomplete="off" class="mtxt flex" name="srchtxt" id="scform_srchtxt" value="" placeholder="{lang mobsearchtxt}">
		<input type="hidden" name="searchsubmit" value="yes"><input type="submit" value="{lang search}" class="mbtn" id="scform_submit">
	</div>
	<!--{eval $policymsgs = $p = '';}-->
	<!--{loop $_G['setting']['creditspolicy']['search'] $id $policy}-->
	<!--{block policymsg}--><!--{if $_G['setting']['extcredits'][$id]['img']}-->$_G['setting']['extcredits'][$id]['img'] <!--{/if}-->$_G['setting']['extcredits'][$id]['title'] $policy $_G['setting']['extcredits'][$id]['unit']<!--{/block}-->
	<!--{eval $policymsgs .= $p.$policymsg;$p = ', ';}-->
	<!--{/loop}-->
	<!--{if $policymsgs}--><p>{lang search_credit_msg}</p><!--{/if}-->
</form>
<div class="threadlist_box<!--{if ((in_array($thread['displayorder'], array(1, 2, 3, 4)))) && $_G['page'] == 1}--> mt10<!--{/if}--> cl">
	<div class="threadlist cl">
		<ul>
		<!--{if $_G['forum_threadcount']}-->
			<!--{eval $threadlist_data = get_attach($_G['forum_threadlist']);}-->
			<!--{loop $_G['forum_threadlist'] $key $thread}-->
			<!--{if !$_G['setting']['mobile']['forum']['displayorder3'] && $thread['displayorder'] > 0}-->
				{eval continue;}
			<!--{/if}-->
			<!--{if $thread['displayorder'] > 0 && !$displayorder_thread}-->
				{eval $displayorder_thread = 1;}
			<!--{/if}-->
			<!--{if $thread['moved']}-->
				<!--{eval $thread['tid']=$thread['closed'];}-->
			<!--{/if}-->
			<!--{if in_array($thread['displayorder'], array(1, 2, 3, 4))}-->
			<li class="list_top">
				<a href="forum.php?mod=viewthread&tid=$thread['tid']&extra=$extra">
					<!--{if in_array($thread['displayorder'], array(1, 2, 3, 4))}-->
						<span class="micon">{lang mobzhiding}</span>
					<!--{/if}-->
					<em $thread['highlight']>{$thread['subject']}</em>					
				</a>
			</li>
			<!--{else}-->
			<li class="list">
				<!--{hook/forumdisplay_thread_mobile $key}-->
				<div class="threadlist_top cl">
					<a href="home.php?mod=space&uid={$thread['authorid']}" class="mimg"><img src="<!--{avatar($thread['authorid'], 'middle', true)}-->"></a>
					<div class="muser">
						<h3><a href="home.php?mod=space&uid={$thread['authorid']}" class="mmc">{$thread['author']}</a></h3>
						<span class="mtime">{$thread['dateline']}</span>
					</div>
				</div>
				<a href="forum.php?mod=viewthread&tid=$thread['tid']&extra=$extra">
				<div class="threadlist_tit cl">
					<!--{if $thread['folder'] == 'lock'}-->
						<span class="micon lock">{lang closed_thread}</span>
					<!--{elseif $thread['special'] == 1}-->
						<span class="micon">{lang thread_poll}</span>
					<!--{elseif $thread['special'] == 2}-->
						<span class="micon">{lang thread_trade}</span>
					<!--{elseif $thread['special'] == 3}-->
						<span class="micon">{lang thread_reward}</span>
					<!--{elseif $thread['special'] == 4}-->
						<span class="micon">{lang thread_activity}</span>
					<!--{elseif $thread['special'] == 5}-->
						<span class="micon">{lang thread_debate}</span>
					<!--{/if}-->
					<!--{if $thread['attachment'] == 2 && $_G['setting']['mobile']['mobilesimpletype'] == 1}-->
						<span class="micon">{lang mobtu}</span>
					<!--{/if}-->
					<!--{if in_array($thread['displayorder'], array(1, 2, 3, 4))}-->
						<span class="micon top">{lang thread_sticky}</span>
					<!--{/if}-->
					<!--{if $thread['digest'] > 0}-->
						<span class="micon digest">{lang thread_digest}</span>
					<!--{/if}-->
					<em $thread['highlight']>{$thread['subject']}</em>
				</div>
				</a>
				<a href="forum.php?mod=viewthread&tid=$thread['tid']&extra=$extra"><div class="threadlist_mes cl">{$threadlist_data[$thread['tid']]['message']}</div></a>
				<!--{if $threadlist_data[$thread['tid']]['attachment']}-->
				<!--{eval $attach_on = 0;}-->
				<a href="forum.php?mod=viewthread&tid=$thread['tid']&extra=$extra">
					<div class="{if count($threadlist_data[$thread['tid']]['attachment']) == 1}threadlist_imgs1 {elseif count($threadlist_data[$thread['tid']]['attachment']) == 2} threadlist_imgs threadlist_imgs2{else} threadlist_imgs{/if} cl">
						<ul>
							<!--{loop $threadlist_data[$thread['tid']]['attachment'] $value}-->
							<!--{eval $attach_on++; if($attach_on > 9) break;}-->
							<li><!--{if count($threadlist_data[$thread['tid']]['attachment']) > 9 && $attach_on == 9}--><em>{echo count($threadlist_data[$thread['tid']]['attachment'])}{lang mobtu}</em><!--{/if}--><img src="$value" class="vm"></li>
							<!--{/loop}-->
						</ul>
					</div>
				</a>
				<!--{/if}-->
				
				<div class="threadlist_foot cl">
					<ul>
						<!--{if $thread['typeid']}-->
						<li class="mr"><a href="forum.php?mod=forumdisplay&fid=$thread['fid']&filter=typeid&typeid=$thread['typeid']">#{$_G['forum']['threadtypes']['types'][$thread['typeid']]}</a></li>
						<!--{/if}-->
						<li><i class="dm-eye-fill"></i>{$thread['views']}</li>
						<li><i class="dm-chat-s-fill"></i>{$thread['replies']}</li>
					</ul>
				</div>
			</li>
			<!--{/if}-->
			<!--{/loop}-->
		<!--{else}-->
			<h4>{lang forum_nothreads}</h4>
		<!--{/if}-->
		</ul>
	</div>
	$multipage
</div>