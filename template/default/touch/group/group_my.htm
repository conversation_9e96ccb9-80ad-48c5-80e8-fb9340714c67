<!--{template common/header}-->
<div class="header cl">
	<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div>
	<h2>{$_G[username]}{lang somebody_group}</h2>
	<div class="my"></div>
</div>
<div class="dhnav_box">
	<div id="dhnav">
		<div id="dhnav_li">
		<ul class="flex-box">
			<li class="flex{if $actives['groupthread']} mon{/if}"><a href="group.php?mod=my&view=groupthread">{lang group_thread}</a></li>
			<li class="flex{if $actives['mythread']} mon{/if}"><a href="group.php?mod=my&view=mythread">{lang my_thread}</a></li>
			<li class="flex{if $actives['join']} mon{/if}"><a href="group.php?mod=my&view=join">{lang my_join}</a></li>
			<li class="flex{if $actives['manager']} mon{/if}"><a href="group.php?mod=my&view=manager">{lang my_manage}</a></li>
		</ul>
		</div>
	</div>
</div>

<!--{if $view == 'groupthread' || $view == 'mythread'}-->
<!--{if $usergroups['grouptype']}-->
<div class="dhnavs_box">
	<div id="dhnavs">
		<div id="dhnavs_li">
			<ul class="swiper-wrapper">
				<li class="swiper-slide{if empty($typeid)} mon{/if}"><a href="group.php?mod=my&view={$view}">{lang all}</a></li>
				<!--{loop $usergroups['grouptype'] $type}-->
				<li class="swiper-slide{if $typeid == $type['fid']} mon{/if}"><a href="group.php?mod=my&view={$view}{if $typeid != $type['fid']}&typeid={$type['fid']}{/if}">{$type['name']}</a></li>
				<!--{/loop}-->
			</ul>
		</div>
	</div>
</div>
<script>
	if($("#dhnavs_li .mon").length > 0) {
		var discuz_nav = $("#dhnavs_li .mon").offset().left + $("#dhnavs_li .mon").width() >= $(window).width() ? $("#dhnavs_li .mon").index() : 0;
	}else{
		var discuz_nav = 0;
	}	
	new Swiper('#dhnavs_li', {
		freeMode : true,
		slidesPerView : 'auto',
		initialSlide : discuz_nav,
		onTouchMove: function(swiper){
			Discuz_Touch_on = 0;
		},
		onTouchEnd: function(swiper){
			Discuz_Touch_on = 1;
		},
	});
</script>
<!--{/if}-->
<!--{if $attentionthread}-->
<div class="threadlist_box cl">
	<!--{loop $attentionthread $groupid $threads}-->
	<div class="threadlist cl">
		<h2 class="mt10 cl">{$usergroups['groups'][$groupid]}<a href="forum.php?mod=group&fid=$groupid" class="y">{lang more}</a></h2>
		<ul>
		<!--{loop $threads $tid $thread}-->
		<li class="list">
			<a href="forum.php?mod=viewthread&tid=$tid&extra=$extra">
			<div class="threadlist_tit cl">
				<em>{$thread['subject']}</em>					
			</div>
			</a>
			<div class="threadlist_mes cl">
				<span><!--{if $thread['lastposter']}-->$thread[lastposter]<!--{else}-->{$_G['setting']['anonymoustext']}<!--{/if}--></span>
				<span>$thread[lastpost]</span>				
			</div>
			<div class="threadlist_foot cl">
				<ul>
					<li class="mr"><a href="forum.php?mod=forumdisplay&fid=$thread['fid']">#{$thread['groupname']}</a></li>
					<li><i class="dm-eye-fill"></i>{$thread['views']}</li>
					<li><i class="dm-chat-s-fill"></i>{$thread['replies']}</li>
				</ul>
			</div>
		</li>
		<!--{/loop}-->
		</ul>
	</div>
	<!--{/loop}-->
</div>
<!--{/if}-->
<!--{if $groupthreadlist}-->
<div class="threadlist_box cl">
    <div class="threadlist cl">
		<h2 class="mt10 cl"><!--{if $view == 'groupthread'}-->{lang last_topic_in_group}<!--{else}-->{lang my_last_topic_in_group}<!--{/if}--></h2>
		<ul>
        <!--{loop $groupthreadlist $tid $thread}-->
        <li class="list">
			<a href="forum.php?mod=viewthread&tid=$tid&extra=$extra">
			<div class="threadlist_tit cl">
				<em>{$thread['subject']}</em>					
			</div>
			</a>
			<div class="threadlist_mes cl">
				<span><!--{if $thread['lastposter']}-->$thread[lastposter]<!--{else}-->{$_G['setting']['anonymoustext']}<!--{/if}--></span>
				<span>$thread[lastpost]</span>				
			</div>
			<div class="threadlist_foot cl">
				<ul>
					<li class="mr"><a href="forum.php?mod=forumdisplay&fid=$thread['fid']">#{$thread['groupname']}</a></li>
					<li><i class="dm-eye-fill"></i>{$thread['views']}</li>
					<li><i class="dm-chat-s-fill"></i>{$thread['replies']}</li>
				</ul>
			</div>
        </li>
        <!--{/loop}-->
    </ul>
</div>
<!--{/if}-->

<!--{if !$attentionthread && !$groupthreadlist}-->
<div class="threadlist_box cl">{lang no_related_posts}</div>
<!--{/if}-->

<!--{elseif $view == 'manager' || $view == 'join'}-->
<!--{if $grouplist}-->
<div class="forumlist mt10 cl">
	<div class="sub-forum mlist1 cl">
	<ul>
	<!--{loop $grouplist $groupid $group}-->
	<li>
		<span class="micon"><a href="forum.php?mod=forumdisplay&action=list&fid={$group['fid']}"><img src="{if strstr($group['icon'],'groupicon.gif')}{STATICURL}image/mobile/touch/forum.png{else}{$group['icon']}{/if}" alt="{$group['name']}"></a></span>
		<a href="forum.php?mod=forumdisplay&action=list&fid={$group['fid']}" class="murl">
		<p class="mtit">$group['name']</p>
		<!--{if $group['description']}-->
		<p class="mtxt">$group['description']</p>
		<!--{else}-->
		<p class="mtxt">{lang threads} {$group[threads]}</p>
		<!--{/if}-->
		</a>
	</li>
	<!--{/loop}-->
	</ul>
	</div>
</div>
<!--{if $multipage}-->$multipage<!--{/if}-->

<!--{else}-->
<div class="forumlist cl">{lang no_group_join}</div>
<!--{/if}-->
<!--{/if}-->

<!--{template common/footer}-->
