<!--{template common/header}-->
<!--{eval
	$space_uid = $space['uid'];
	$_G['home_tpl_spacemenus'][] = "<a href=\"home.php?mod=space&do=trade\">{lang trade}</a>";
}-->
<!--{if $_GET['view'] == 'eccredit'}-->
	<!--{eval
			$_G['home_tpl_spacemenus'][] = "<a href=\"home.php?mod=space&uid=$space_uid&do=$do&view=eccredit\">{lang they_credit_rating}</a>";
	}-->
<!--{else}-->
	<!--{eval
		$_G['home_tpl_spacemenus'][] = "<a href=\"home.php?mod=space&uid=$space_uid&do=$do&view=onlyuser\">{lang they_trade}</a>";
	}-->
<!--{/if}-->

<div class="header cl">
	<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div>
	<h2>{lang trade}</h2>
	<div class="my"><a href="index.php"><i class="dm-house"></i></a></div>
</div>

<!--{if $space['self']}-->
<div class="dhnav_box">
	<div id="dhnav">
		<div id="dhnav_li">
			<ul class="swiper-wrapper">
				<li class="swiper-slide {if $actives['we']}mon{/if}"><a href="home.php?mod=space&do=trade&view=we">{lang friend_trade}</a></li>
				<li class="swiper-slide {if $actives['me']}mon{/if}"><a href="home.php?mod=space&do=trade&view=me">{lang my_trade}</a></li>
				<li class="swiper-slide {if $actives['tradelog']}mon{/if}"><a href="home.php?mod=space&do=trade&view=tradelog">{lang trade_log}</a></li>
				<li class="swiper-slide"><a href="home.php?mod=space&do=trade&view=eccredit">{lang credit_rating}</a></li>
				<!--{if $_G['group']['allowposttrade']}-->
					<!--{if $_G['setting']['tradeforumid']}-->
					<li class="swiper-slide"><a href="forum.php?mod=post&action=newthread&fid=$_G['setting']['tradeforumid']&special=2">{lang create_new_trade}</a></li>
					<!--{else}-->
					<li class="swiper-slide"><a href="forum.php?mod=misc&action=nav&special=2">{lang create_new_trade}</a></li>
					<!--{/if}-->
				<!--{/if}-->
			</ul>
		</div>
	</div>
</div>
<script>
	if($("#dhnav_li .mon").length > 0) {
		var discuz_nav = $("#dhnav_li .mon").offset().left + $("#dhnav_li .mon").width() >= $(window).width() ? $("#dhnav_li .mon").index() : 0;
	}else{
		var discuz_nav = 0;
	}	
	new Swiper('#dhnav_li', {
		freeMode : true,
		slidesPerView : 'auto',
		initialSlide : discuz_nav,
		onTouchMove: function(swiper){
			Discuz_Touch_on = 0;
		},
		onTouchEnd: function(swiper){
			Discuz_Touch_on = 1;
		},
	});
</script>
<!--{else}-->
<!--{if $_GET['view']=='me'}-->
	<!--{eval $actives['onlyuser'] = true;}-->
<!--{/if}-->
<div class="dhnavs_box">
	<div id="dhnavs">
		<div id="dhnavs_li">
			<ul class="swiper-wrapper">
			<!--{if $space['uid'] > 0}-->
				<li class="swiper-slide {if $actives['onlyuser']}mon{/if}"><a href="home.php?mod=space&do=trade&view=onlyuser">{lang sale_of_goods}</a></li>
				<li class="swiper-slide {if $actives['eccredit']}mon{/if}"><a href="home.php?mod=space&do=trade&view=eccredit">{lang credit_rating}</a></li>
			<!--{/if}-->
			</ul>
		</div>
	</div>
</div>
<!--{/if}-->

<!--{if $_GET['view'] == 'tradelog' && $space['uid'] > 0}-->
<div class="dhnavs_box">
	<div id="dhnavs">
		<div id="dhnavs_li">
			<ul class="swiper-wrapper">
			<!--{if $space['uid'] > 0}-->
				<li class="swiper-slide {if $orderactives['sell']}mon{/if}"><a href="home.php?mod=space&do=trade&view=tradelog">{lang sell_trade}</a></li>
				<li class="swiper-slide {if $orderactives['buy']}mon{/if}"><a href="home.php?mod=space&do=trade&view=tradelog&type=buy">{lang buy_trade}</a></li>
			<!--{/if}-->
			</ul>
		</div>
	</div>
</div>
<!--{/if}-->
<div class="bodybox cl">
<!--{if $userlist}-->
	<ul class="post_box cl">
		<li class="flex-box mli">
			<div class="flex">{lang view_by_friend}</div>
			<div class="flex-3">
				<select name="fuidsel" onchange="fuidgoto(this.value);" class="sort_sel">
					<option value="">{lang all_friends}</option>
					<!--{loop $userlist $value}-->
					<option value="$value['fuid']"{$fuid_actives[$value['fuid']]}>$value['fusername']</option>
					<!--{/loop}-->
				</select>
			</div>
		</li>
	</ul>
<!--{/if}-->
<!--{if $_GET['view'] == 'tradelog'}-->
<!--{eval
	$selltrades = array('all' => '{lang trade_all}', 'trading' => '{lang trade_trading}', 'attention' => '{lang trade_attention}', 'eccredit' => '{lang trade_eccredit}', 'success' => '{lang trade_success}', 'refund' => '{lang trade_refund}', 'closed' => '{lang trade_closed}', 'unstart' => '{lang trade_unstart}');
}-->
	<ul class="post_box cl">
		<li class="flex-box mli">
			<div class="flex">
				<select onchange="filterTrade(this.value)" class="sort_sel">
					<!--{loop $selltrades $key $langstr}-->
					<option value="$key" {if $filter == $key} selected="selected"{/if}>$langstr</option>
					<!--{/loop}-->
				</select>
			</div>
		</li>
	</ul>
	<script type="text/javascript">
		function filterTrade(value) {
			window.location = 'home.php?mod=space&do=trade&view=tradelog&type=$viewtype&filter='+value;
		}
	</script>
	<!--{if $tradeloglist}-->
	<ul class="wzlist mt10 cl">
	<!--{loop $tradeloglist $tradelog}-->
		<li class="cl">
			<div class="mimg">
				<a href="forum.php?mod=trade&orderid=$tradelog['orderid']"><!--{if $tradelog['aid']}--><img src="{echo getforumimg($tradelog['aid'])}" /><!--{else}--><div class="nophoto"></div><!--{/if}--></a>
			</div>
			<div class="minfo">
				<p class="mtit"><a href="forum.php?mod=trade&orderid=$tradelog['orderid']">$tradelog['subject']</a></p>
				<p class="mtime xg1 xs1">{lang post}:&nbsp;<a href="forum.php?mod=viewthread&tid=$tradelog['tid']&do=tradeinfo&pid=$tradelog['pid']">$tradelog['threadsubject']</a></p>
				<p class="mtime xg1 xs1">
				<!--{if $viewtype == 'sell'}-->{lang trade_buyer}<!--{else}-->{lang trade_seller}<!--{/if}-->:&nbsp;
				<!--{if $item == 'buytrades'}-->
					<a target="_blank" href="home.php?mod=space&uid=$tradelog['sellerid']">$tradelog['seller']</a>
				<!--{else}-->
					<!--{if $tradelog['buyerid']}--><a target="_blank" href="home.php?mod=space&uid=$tradelog['buyerid']">$tradelog['buyer']</a><!--{else}-->$tradelog['buyer']<!--{/if}-->
				<!--{/if}-->
				</p>
				<p class="mtime xg1 xs1">
				{lang trade_amount}:&nbsp;
				<!--{if $tradelog['price'] > 0}-->
					$tradelog['price'] {lang trade_units}
				<!--{/if}-->
				<!--{if $tradelog['credit'] > 0}-->
					<em class="pl5">{$extcredits[$creditid]['title']} $tradelog['credit'] {$extcredits[$creditid]['unit']}</em>
				<!--{/if}-->
				</p>
				<p class="mtime xg1 xs1">
					<cite><a target="_blank" href="forum.php?mod=trade&orderid=$tradelog['orderid']">
						<!--{if $tradelog['attend']}-->
							<b>$tradelog['status']</b>
						<!--{else}-->
							$tradelog['status']
						<!--{/if}-->
					</a></cite>
					<em class="pl5">$tradelog['lastupdate']</em>
				</p>
				<!--{if $filter == 'success' || $filter == 'refund' || $filter == 'eccredit'}-->
				<p class="mtime xg1 xs1">
					<!--{if $tradelog['ratestatus'] == 3}-->
						{lang eccredit_post_between}
					<!--{elseif ($item == 'buytrades' && $tradelog['ratestatus'] == 1) || ($item == 'selltrades' && $tradelog['ratestatus'] == 2)}-->
						{lang eccredit_post_waiting}
					<!--{else}-->
						<!--{if ($item == 'buytrades' && $tradelog['ratestatus'] == 2) || ($item == 'selltrades' && $tradelog['ratestatus'] == 1)}-->{lang eccredit_post_already}<!--{/if}-->
						<!--{if $item == 'buytrades'}-->
							<em class="pl5"><a href="home.php?mod=spacecp&ac=eccredit&op=rate&orderid=$tradelog['orderid']&type=0" target="_blank">{lang eccredit1}</a></em>
						<!--{else}-->
							<em class="pl5"><a href="home.php?mod=spacecp&ac=eccredit&op=rate&orderid=$tradelog['orderid']&type=1" target="_blank">{lang eccredit1}</a></em>
						<!--{/if}-->
					<!--{/if}-->
				</p>
				<!--{/if}-->
			</div>
		</li>
	<!--{/loop}-->
	</ul>
	<!--{/if}-->
<!--{else}-->
	<!--{if $list}-->
	<ul class="wzlist cl">
	<!--{loop $list $key $value}-->
		<li>
			<div class="mimg">
				<a href="forum.php?mod=viewthread&tid=$value['tid']&do=tradeinfo&pid=$value['pid']" target="_blank"><!--{if $value['aid']}--><img src="{echo getforumimg($value['aid'])}" /><!--{else}--><div class="nophoto"></div><!--{/if}--></a>
			</div>
			<div class="minfo">
				<p class="mtit"><a href="forum.php?mod=viewthread&tid=$value['tid']&do=tradeinfo&pid=$value['pid']" target="_blank">$value['subject']</a></p>
				<p class="mtime"><a href="home.php?mod=space&uid=$value['sellerid']" target="_blank">$value['seller']</a></p>
				<!--{if $value['expiration'] && $value['expiration'] < $_G['timestamp'] || $value['closed']}-->
				<p class="mtime">- {lang trade_closing} -</p>
				<!--{else}-->
				<p class="mtime">
					<!--{if $value['price'] > 0}-->
						&yen; <em class="xi1">$value['price']</em>
					<!--{/if}-->
					<!--{if $_G['setting']['creditstransextra'][5] != -1 && $value['credit']}-->
						<em class="pl5"><!--{if $value['price'] > 0}-->{lang additional} <!--{/if}--><em class="xi1">$value[credit]</em> {$_G[setting][extcredits][$_G['setting']['creditstransextra'][5]][unit]}{$_G[setting][extcredits][$_G['setting']['creditstransextra'][5]][title]}</em>
					<!--{/if}-->
				</p>
				<!--{/if}-->
			</div>
		</li>
	<!--{/loop}-->
	<!--{if $emptyli}-->
		<!--{loop $emptyli $value}-->
			<li>&nbsp;</li>
		<!--{/loop}-->
	<!--{/if}-->
	</ul>
	<!--{else}-->
	<div class="empty-box"><h4>{lang no_trade}</h4></div>
	<!--{/if}-->
<!--{/if}-->

<!--{if $hiddennum}-->
	<p class="mt10">{lang hide_trade}</p>
<!--{/if}-->
<!--{if $multi}--><div class="pgs cl">$multi</div><!--{/if}-->

</div>

<script type="text/javascript">
function fuidgoto(fuid) {
	var parameter = fuid != '' ? '&fuid='+fuid : '';
	window.location.href = 'home.php?mod=space&do=trade&view=we'+parameter;
}
</script>
<!--{template common/footer}-->