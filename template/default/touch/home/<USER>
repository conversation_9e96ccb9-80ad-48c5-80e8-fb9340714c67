<div class="dhnavs_box">
	<div id="dhnavs">
		<div id="dhnavs_li">
			<ul class="swiper-wrapper">
				<li class="swiper-slide<!--{if $_GET['op'] == 'log' && empty($_GET['income'])}--> mon<!--{/if}-->"><a href="home.php?mod=spacecp&ac=credit&op=log">{lang all}</a></li>
				<li class="swiper-slide<!--{if $_GET['op'] == 'log' && $_GET['income'] == 1}--> mon<!--{/if}-->"><a href="home.php?mod=spacecp&ac=credit&op=log&income=1">{lang credit_income_1}</a></li>
				<li class="swiper-slide<!--{if $_GET['op'] == 'log' && $_GET['income'] == -1}--> mon<!--{/if}-->"><a href="home.php?mod=spacecp&ac=credit&op=log&income=-1">{lang credit_income_2}</a></li>
				<!--{if $_G['setting']['ec_ratio'] || $_G['setting']['card']['open']}--><li class="swiper-slide<!--{if $_GET['op'] == 'buy'}--> mon<!--{/if}-->"><a href="home.php?mod=spacecp&ac=credit&op=buy">{lang home_credit_buy}</a></li><!--{/if}-->
				<!--{if $_G[setting][transferstatus] && $_G['group']['allowtransfer']}--><li class="swiper-slide<!--{if $_GET['op'] == 'transfer'}--> mon<!--{/if}-->"><a href="home.php?mod=spacecp&ac=credit&op=transfer">{lang transfer_credits}</a></li><!--{/if}-->
				<!--{if $_G[setting][exchangestatus]}--><li class="swiper-slide<!--{if $_GET['op'] == 'exchange'}--> mon<!--{/if}-->"><a href="home.php?mod=spacecp&ac=credit&op=exchange">{lang exchange_credits}</a></li><!--{/if}-->
				<li class="swiper-slide<!--{if $_GET['op'] == 'rule'}--> mon<!--{/if}-->"><a href="home.php?mod=spacecp&ac=credit&op=rule">{lang credit_rule}</a></li>
				<!--{if !empty($_G['setting']['plugins']['spacecp_credit'])}-->
					<!--{loop $_G['setting']['plugins']['spacecp_credit'] $id $module}-->
						<!--{if in_array($module['adminid'], array(0, -1)) || ($module['adminid'] && $_G['adminid'] > 0 && $module['adminid'] >= $_G['adminid'])}--><li class="swiper-slide{if $_GET[id] == $id} mon{/if}"><a href="home.php?mod=spacecp&ac=plugin&op=credit&id=$id">$module[name]</a></li><!--{/if}-->
					<!--{/loop}-->
				<!--{/if}-->
			</ul>
		</div>
	</div>
</div>
<script>if($("#dhnavs_li .mon").length>0){var discuz_nav=$("#dhnavs_li .mon").offset().left+$("#dhnavs_li .mon").width()>=$(window).width()?$("#dhnavs_li .mon").index():0}else{var discuz_nav=0}new Swiper('#dhnavs_li',{freeMode:true,slidesPerView:'auto',initialSlide:discuz_nav,onTouchMove:function(swiper){Discuz_Touch_on=0},onTouchEnd:function(swiper){Discuz_Touch_on=1},});</script>