<!--{eval $_G['home_tpl_titles'] = array($album['albumname'], '{lang album}');}-->
<!--{template common/header}-->


<!--{if $_GET['op'] == 'edit'}-->

	<form method="post" autocomplete="off" id="theform" name="theform" action="home.php?mod=spacecp&ac=album&op=edit&albumid=$albumid">
		<table cellspacing="0" cellpadding="0" class="tfm">
			<tr>
				<th><label for="albumname">{lang album_name}</label></th>
				<td><input type="text" id="albumname" name="albumname" value="$album['albumname']" size="20" class="px" /></td>
			</tr>
			<tr>
				<th><label for="depict">{lang album_depict}</label></th>
				<td><textarea name="depict" id="depict" class="pt" cols="40" rows="3">$album[depict]</textarea></td>
			</tr>
			<!--{if $categoryselect}-->
				<tr>
					<th>{lang site_categories}</th>
					<td>
						$categoryselect
						({lang select_site_album_categories})
					</td>
				</tr>
			<!--{/if}-->
			<tr>
				<th>{lang privacy_settings}</th>
				<td>
					<select name="friend" onchange="passwordShow(this.value);" class="ps">
						<option value="0"$friendarr[0]>{lang friendname_0}</option>
						<option value="1"$friendarr[1]>{lang friendname_1}</option>
						<option value="2"$friendarr[2]>{lang friendname_2}</option>
						<option value="3"$friendarr[3]>{lang friendname_3}</option>
						<option value="4"$friendarr[4]>{lang friendname_4}</option>
					</select>
				</td>
			</tr>
			<tbody id="span_password" style="$passwordstyle">
				<tr>
					<th>{lang password}</th>
					<td><input type="text" name="password" value="$album['password']" size="10" class="px" /></td>
				</tr>
			</tbody>
			<tbody id="tb_selectgroup" style="$selectgroupstyle">
				<tr>
					<th>{lang specified_friends}</th>
					<td>
						<select name="selectgroup" onchange="getgroup(this.value);" class="ps">
							<option value="">{lang from_friends_group}</option>
							<!--{loop $groups $key $value}-->
								<option value="$key">$value</option>
							<!--{/loop}-->
						</select>
						<p class="d">{lang choices_following_friends_list}</p>
					</td>
				</tr>
				<tr>
					<th>&nbsp;</th>
					<td>
						<textarea name="target_names" id="target_names" rows="3" class="pt">$album[target_names]</textarea>
						<p class="d">{lang friend_name_space}</p>
					</td>
				</tr>
			</tbody>
			<tr>
				<th>&nbsp;</th>
				<td>
					<input type="hidden" name="referer" value="{echo dreferer()}" />
					<input type="hidden" name="editsubmit" value="true" />
					<button name="submit" type="submit" class="pn" value="true"><strong>{lang determine}</strong></button>
				</td>
			</tr>
			<tr>
				<th>&nbsp;</th>
				<td><a href="home.php?mod=spacecp&ac=album&op=delete&albumid=$album['albumid']&handlekey=delalbumhk_{$album['albumid']}" id="album_delete_$album['albumid']" class="dialog">{lang delete_album}</a></td>
			</tr>
		</table>
		<input type="hidden" name="formhash" value="{FORMHASH}" />
	</form>

<!--{elseif $_GET['op'] == 'editpic'}-->

	<!--{if $list}-->
		<form method="post" autocomplete="off" id="theform" name="theform" action="home.php?mod=spacecp&ac=album&op=editpic&albumid=$albumid">
			<table cellspacing="0" cellpadding="0" class="tfm">
				<caption>{lang album_cover_notice}</caption>
				<!--{eval $common = '';}-->
				<!--{loop $list $value}-->
					<tr>
						<td width="20"><input type="checkbox" name="ids[{$value['picid']}]" value="{$value['picid']}" {$value['checked']} class="pc"></td>
						<td width="150" align="center" class="gt">
							<a href="$value[bigpic]" target="_blank"><img src="$value[pic]" alt="" width="140" /></a>
							<!--{eval $ids .= $common.$value['picid'].':'.$value['picid'];}-->
							<!--{eval $common = ',';}-->
							<!--{if $album['albumname']}--><p><a href="home.php?mod=spacecp&ac=album&op=setpic&albumid=$value['albumid']&picid=$value['picid']&handlekey=setpichk" id="a_picid_$value['picid']" class="dialog">{lang set_to_conver}</a></p><!--{/if}-->
						</td>
						<td><textarea name="title[{$value['picid']}]" rows="4" cols="70" class="pt">$value['title']</textarea><input type="hidden" name="oldtitle[{$value['picid']}]" value="$value[title]"></td>
					</tr>
				<!--{/loop}-->
				<tr>
					<td colspan="3">
						<label for="chkall" onclick="checkAll(this.form, 'ids')"><input type="checkbox" name="chkall" id="chkall" class="pc" />{lang select_all}</label>
						<button type="submit" name="editpicsubmit" value="true" class="pn" onclick="this.form.action+='&subop=update';"><strong>{lang update_explain}</strong></button>
						<button type="submit" name="editpicsubmit" value="true" class="pn" onclick="this.form.action+='&subop=delete';return ischeck('theform', 'ids')"><strong>{lang delete}</strong></button>

						<!--{if $albumlist}-->
							<button type="submit" name="editpicsubmit" value="true" class="pn" onclick="this.form.action+='&subop=move';return ischeck('theform', 'ids')"><strong>{lang move_to}</strong></button>
							<select name="newalbumid" class="ps vm">
							<!--{loop $albumlist $key $value}-->
								<!--{if $albumid != $value['albumid']}--><option value="$value['albumid']">$value['albumname']</option><!--{/if}-->
							<!--{/loop}-->
							<!--{if $albumid>0}--><option value="0">{lang default_album}</option><!--{/if}-->
							</select>
						<!--{/if}-->

						<p class="d">{lang delete_pic_notice}</p>
					</td>
				</tr>
			</table>
			<input type="hidden" name="page" value="$page" />
			<input type="hidden" name="editpicsubmit" value="true" />
			<input type="hidden" name="formhash" value="{FORMHASH}" />
		</form>
		<!--{if $multi}--><div class="pgs cl">$multi</div><!--{/if}-->
		<script type="text/javascript">
			var picObj = {{$ids}};
			function succeedhandle_setpichk(url, msg, values) {
				for(var id in picObj) {
					$('a_picid_' + picObj[id]).innerHTML = "{lang set_to_conver}";
				}
				if(values['picid']) {
					$('a_picid_' + values['picid']).innerHTML = "{lang cover_pic}";
				}
			}
		</script>
	<!--{else}-->
		<div class="emp">{lang no_pics}</div>
	<!--{/if}-->

<!--{elseif $_GET['op'] == 'delete'}-->
	<div class="tip">
		<form method="post" autocomplete="off" id="theform" name="theform" action="home.php?mod=spacecp&ac=album&op=delete&albumid=$albumid&uid=$_GET[uid]">
			<input type="hidden" name="referer" value="{echo dreferer()}" />
			<input type="hidden" name="deletesubmit" value="true" />
			<input type="hidden" name="formhash" value="{FORMHASH}" />
			<div class="medal_tip_top">
				<a href="javascript:;" class="author">
					<p>{lang delete_album_message}</p>
				</a>
			</div>

			<div class="medal_tip_box album_tip">
				<ul>
					<li>
						<span>{lang the_album_pic}</span>
						<div class="medal_tip_input">
							<select name="moveto" class="ps">
								<option value="-1">{lang completely_remove}</option>
								<option value="0">{lang move_to_default_album}</option>
								<!--{loop $albums $value}-->
									<option value="$value['albumid']">{lang move_to} $value['albumname']</option>
								<!--{/loop}-->
							</select>
						</div>
						<i class="dm-c-right icon-arrow"></i>
					</li>
				</ul>
			</div>
			<div class="tip_btn">
				<button type="submit" name="submit" class="pn pnc" value="true"><span>{lang determine}</span></button>
			</div>
		</form>
	</div>
<!--{elseif $_GET['op'] == 'edittitle'}-->
	<h3 class="flb">
		<em id="return_$_GET['handlekey']">{lang edit_description}</em>
		<!--{if $_G[inajax]}--><span><a href="javascript:;" onclick="hideWindow('$_GET['handlekey']');" class="flbc" title="{lang close}">{lang close}</a></span><!--{/if}-->
	</h3>
	<form id="titleform" name="titleform" action="home.php?mod=spacecp&ac=album&op=editpic&subop=update&albumid=$pic['albumid']" method="post" autocomplete="off" {if $_G[inajax]}onsubmit="ajaxpost(this.id, 'return_$_GET['handlekey']');"{/if}>
		<input type="hidden" name="referer" value="{echo dreferer()}" />
		<input type="hidden" name="formhash" value="{FORMHASH}" />
		<input type="hidden" name="editpicsubmit" value="true" />
		<!--{if $_G[inajax]}--><input type="hidden" name="handlekey" value="$_GET['handlekey']" /><!--{/if}-->
		<div class="c">
			<textarea name="title[{$pic['picid']}]" cols="50" rows="7" class="pt">$pic[title]</textarea>
		</div>
		<p class="o pns">
			<button type="submit" name="editpicsubmit_btn" class="pn pnc" value="true"><strong>{lang update}</strong></button>
		</p>
	</form>
	<script type="text/javascript">
		function succeedhandle_$_GET['handlekey'] (url, message, values) {
			$('$_GET['handlekey']').innerHTML = values['title'];
		}
	</script>
<!--{elseif $_GET[op] == 'edithot'}-->
	<h3 class="flb">
		<em>{lang adjust_hot}</em>
		<!--{if $_G[inajax]}--><span><a href="javascript:;" onclick="hideWindow('$_GET['handlekey']');" class="flbc" title="{lang close}">{lang close}</a></span><!--{/if}-->
	</h3>
	<form method="post" autocomplete="off" action="home.php?mod=spacecp&ac=album&op=edithot&picid=$picid">
		<input type="hidden" name="referer" value="{echo dreferer()}" />
		<input type="hidden" name="hotsubmit" value="true" />
		<input type="hidden" name="formhash" value="{FORMHASH}" />
		<div class="c">
			{lang new_hot}:<input type="text" name="hot" value="$pic['hot']" size="10" class="px" />
		</div>
		<p class="o pns">
			<button type="submit" name="btnsubmit" value="true" class="pn pnc"><strong>{lang determine}</strong></button>
		</p>
	</form>
<!--{elseif $_GET[op] == 'saveforumphoto'}-->
	<h3 class="flb">
		<em id="return_$_GET['handlekey']">{lang save_to_album}</em>
		<!--{if $_G[inajax]}--><span><a href="javascript:;" onclick="hideWindow('$_GET['handlekey']');" class="flbc" title="{lang close}">{lang close}</a></span><!--{/if}-->
	</h3>
	<form id="saveforumphoto" method="post" autocomplete="off" action="home.php?mod=spacecp&ac=album&op=saveforumphoto&aid=$_GET['aid']" {if $_G[inajax]}onsubmit="ajaxpost(this.id, 'return_$_GET['handlekey']');return false;"{/if}>
		<input type="hidden" name="referer" value="{echo dreferer()}" />
		<input type="hidden" name="savephotosubmit" value="true" />
		<input type="hidden" name="formhash" value="{FORMHASH}" />
		<input type="hidden" name="aid" value="$_GET['aid']" />
		<!--{if $_G[inajax]}--><input type="hidden" name="handlekey" value="$_GET['handlekey']" /><!--{/if}-->
		<div class="c">
			{lang save_to}: <select name="albumid" class="ps vm">
			<!--{loop $albumlist $key $value}-->
				<option value="$value['albumid']">$value['albumname']</option>
			<!--{/loop}-->
			<option value="0">{lang default_album}</option>
			</select>
		</div>
		<p class="o pns">
			<button type="submit" name="btnsubmit" value="true" class="pn pnc"><strong>{lang determine}</strong></button>
		</p>
	</form>
	<script type="text/javascript">
		function succeedhandle_$_GET['handlekey'] (url, message, values) {
			
		}
	</script>
<!--{/if}-->

<!--{template common/footer}-->
