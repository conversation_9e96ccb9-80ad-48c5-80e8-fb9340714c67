<!--{if $_GET['mycenter'] && !$_G['uid']}-->
	<!--{eval dheader('Location:member.php?mod=logging&action=login');exit;}-->
<!--{elseif $_GET['mycenter'] && $_G['uid'] != $space['uid']}-->
	<!--{eval dheader('Location:home.php?mod=space&uid='.$_G['uid'].'&do=profile&mycenter=1');exit;}-->
<!--{/if}-->

<!--{template common/header}-->
<style>.user_avatar {background-image:url(<!--{avatar($space['uid'], 'big', true)}-->) !important}</style>
<div class="header cl">
	<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div>
	<h2><!--{if $_G['uid'] == $space['uid']}-->{lang myprofile}<!--{else}-->$space['username']{lang otherprofile}<!--{/if}--></h2>
	<div class="my"><a href="index.php"><i class="dm-house"></i></a></div>
</div>
<div class="userinfo">
	<div class="user_avatar">
		<div class="avatar_bg">
			<div class="avatar_m"><img src="<!--{avatar($space['uid'], 'middle', true)}-->" /></div>
			<h2 class="name">$space['username']</h2>
		</div>
	</div>
	<div class="user_box cl" onclick="window.location.href='home.php?mod=spacecp&ac=credit&op=log'">
		<ul>
			<li><span>$space['credits']</span>{lang credits}</li>
			<!--{loop $_G['setting']['extcredits'] $key $value}-->
			<!--{if $value['title']}-->
			<li><span>{$space["extcredits$key"]} $value['unit']</span>$value['title']</li>
			<!--{/if}-->
			<!--{/loop}-->
		</ul>
	</div>
	<!--{if $_G['setting']['magicstatus'] && $_G['setting']['magics']['gift']}-->
	<!--{eval $magicgiftinfo = !empty($space['magicgift']) ? dunserialize($space['magicgift']) : array();}-->
	<!--{if $magicgiftinfo['left'] && !in_array($_G['uid'], (array)$magicgiftinfo['receiver']) && $_G['uid'] != $space['uid']}-->
	<!--{eval $percredit = min($magicgiftinfo['percredit'], $magicgiftinfo['left']);}-->
	<!--{eval $extcredits = str_replace('extcredits', '', $magicgiftinfo['credittype']);}-->
	<!--{eval $credittype = $_G['setting']['extcredits'][$extcredits]['title'];}-->
	<div class="bodybox dzcell-group-inset m15 p10 cl">
		<a href="home.php?mod=spacecp&ac=magic&op=receivegift&uid={$space['uid']}" class="dialog">
		<div class="dzcell-item">
			<div><img src="{STATICURL}image/magic/gift.small.gif" class="vm" /><!--{eval echo lang('magic/gift', 'gift_receive_gift', array('percredit'=>$percredit,'credittype'=>$credittype))}--></div>
		</div>
		</a>
	</div>
	<!--{/if}-->
	<!--{/if}-->
	<!--{hook/space_profile_top_mobile}-->
	<div class="myinfo_list_ico cl">
		<ul>
		<!--{if helper_access::check_module('forum')}-->
			<li><a href="home.php?mod=space&uid={$space['uid']}&do=thread<!--{if $_G['uid'] == $space['uid']}-->&view=me<!--{/if}-->"><i class="dm-chat-s-fill"></i><!--{if $_G['uid'] == $space['uid']}-->{lang mythread}<!--{else}-->{lang mobta}{lang mobthread}<!--{/if}--></a></li>
		<!--{/if}-->
		<!--{if helper_access::check_module('blog')}-->
			<li><a href="home.php?mod=space&uid={$space['uid']}&do=blog<!--{if $_G['uid'] == $space['uid']}-->&view=me<!--{/if}-->"><i class="dm-chat-s-fill"></i><!--{if $_G['uid'] == $space['uid']}-->{lang my}{lang blog}<!--{else}-->{lang mobta}{lang blog}<!--{/if}--></a></li>
		<!--{/if}-->
		<!--{if helper_access::check_module('favorite') && $_G['uid'] == $space['uid']}-->
			<li><a href="home.php?mod=space&uid={$space['uid']}&do=favorite&view=me&type=thread"><i class="dm-star-fill"></i>{lang myfavorite}</a></li>
		<!--{/if}-->
		<!--{if $_G['uid'] == $space['uid']}-->
			<li><a href="home.php?mod=space&do=pm"><i class="dm-chat-t-fill"></i>{lang pm_center}{lang remind}<!--{if $_G['member']['newpm'] || $_G['member']['newprompt']}--><em>NEW</em><!--{/if}--></a></li>
		<!--{else}-->
			<li><a href="home.php?mod=space&do=pm&subop=view&touid={$space['uid']}"><i class="dm-chat-t-fill"></i>{lang mobfa}{lang pm}</a></li>
		<!--{/if}-->
		<!--{if helper_access::check_module('friend')}-->
			<!--{if $_G['uid'] == $space['uid']}-->
				<li><a href="home.php?mod=space&do=friend"><i class="dm-person-fill"></i>{lang my_friends}</a></li>
			<!--{else}-->
			<!--{eval require_once libfile('function/friend');$isfriend=friend_check($space['uid']);}-->
			<!--{if !$isfriend}-->
				<li><a href="home.php?mod=spacecp&ac=friend&op=add&uid=$space['uid']&handlekey=addfriendhk_{$space['uid']}" id="a_friend_li_{$space['uid']}" class="dialog"><i class="dm-person-fill"></i>{lang add_friend}</a></li>
			<!--{else}-->
				<li><a href="home.php?mod=spacecp&ac=friend&op=ignore&uid=$space['uid']&handlekey=ignorefriendhk_{$space['uid']}" id="a_ignore_{$space['uid']}" class="dialog"><i class="dm-person-fill"></i>{lang ignore_friend}</a></li>
			<!--{/if}-->
			<!--{/if}-->
		<!--{/if}-->
		<!--{if ($_G['setting']['ec_ratio'] || $_G['setting']['card']['open']) && $_G['uid'] == $space['uid']}-->
			<li><a href="home.php?mod=spacecp&ac=credit&op=buy"><i class="dm-person-fill"></i>{lang credit_recharge}</a></li>
		<!--{/if}-->
		<!--{if $_G['setting']['magicstatus'] && $_G['setting']['magics']['gift']}-->
			<!--{if $_G['uid'] == $space['uid']}-->
				<!--{if $magicgiftinfo}-->
					<li><a href="home.php?mod=spacecp&ac=magic&op=retiregift" class="dialog"><i><img src="{STATICURL}image/magic/gift.small.gif" class="vm"></i><!--{eval echo lang('magic/gift', 'gift_gc');}--></a></li>
				<!--{else}-->
					<li><a href="home.php?mod=magic&mid=gift" class="dialog"><i><img src="{STATICURL}image/magic/gift.small.gif" class="vm"></i><!--{eval echo lang('magic/gift', 'gift_use');}--></a></li>
				<!--{/if}-->
			<!--{/if}-->
		<!--{/if}-->
		<!--{hook/space_profile_nav_extra_mobile}-->
		</ul>
	</div>
	<!--{if $space['group']['maxsigsize'] && $space['sightml']}-->
	<div class="myinfo_list cl">
		<ul>
			<li><b>{lang personal_signature}</b></li>
			<li class="sig">$space['sightml']</li>
		</ul>
	</div>
	<!--{/if}-->
	<div class="myinfo_list cl">
		<ul>
			<!--{hook/space_profile_baseinfo_top_mobile}-->
			<li><b>{lang memcp_profile}</b><!--{if $_G['uid'] == $space['uid']}--><span class="mtxt"><a href="home.php?mod=spacecp" style="color: #fff;">{lang setup}</a></span><!--{else}--><!--{if $_G['ols'][$space['uid']]}--><span class="mtxt">{lang online}</span><!--{/if}--><!--{/if}--></li>
			<li>UID<span>{$space['uid']}</span></li>
			<li>{lang usergroup}<!--{if !empty($space['groupexpiry'])}--><em>{lang group_useful_life}<!--{date($space['groupexpiry'], 'Y-m-d H:i')}--></em><!--{/if}--><span style="color:{$space['group']['color']}">{$space['group']['grouptitle']}</span></li>
			<!--{if $space['adminid']}--><li>{lang management_team}<span style="color:{$space['admingroup']['color']}">{$space['admingroup']['grouptitle']}</span></li><!--{/if}-->
			<!--{if $space['extgroupids']}--><li>{lang group_expiry_type_ext}<span>$space['extgroupids']</span></li><!--{/if}-->
			<!--{loop $profiles $value}-->
			<li>$value['title']<span>$value['value']</span></li>
			<!--{/loop}-->
			<!--{if in_array($_G['adminid'], array(1, 2))}--><li>Email<span>$space['email']</span></li><!--{/if}-->
			<!--{if $space['spacenote']}--><li>{lang spacenote}<span>$space['spacenote']</span></li><!--{/if}-->
			<!--{if $space['customstatus']}--><li>{lang permission_basic_status}<span>$space['customstatus']</span></li><!--{/if}-->
			<!--{if $space['oltime'] && !$_G['setting']['sessionclose']}--><li>{lang online_time}<span>$space['oltime'] {lang hours}</span></li><!--{/if}-->
			<li>{lang regdate}<span>$space['regdate']</span></li>
			<li>{lang last_visit}<span>$space['lastvisit']</span></li>
			<!--{hook/space_profile_baseinfo_bottom_mobile}-->
		</ul>
	</div>
	<!--{hook/space_profile_extrainfo_mobile}-->
	<!--{if $_G['uid'] && getstatus($_G['member']['allowadmincp'], 1)}-->
	<div class="btn_admincp"><a href="admin.php" class="pn">{lang admincp_mobile}</a></div>
	<!--{/if}-->
	<!--{if $space['uid'] == $_G['uid']}-->
	<div class="btn_exit"><a href="member.php?mod=logging&action=logout&formhash={FORMHASH}" class="pn">{lang logout_mobile}</a></div>
	<!--{/if}-->
</div>
<!--{if !$_GET['mycenter']}--><!--{eval $nofooter = true;}--><!--{/if}-->
<!--{template common/footer}-->