<!--{template common/header}-->

<!--{if $op == 'bkname'}-->
	<h3 class="flb">
		<em id="return_$_GET[handlekey]">{lang follow_for}$followuser['fusername']{lang follow_add_bkname}</em>
		<!--{if $_G[inajax]}--><span><a href="javascript:;" onclick="hideWindow('$_GET[handlekey]');" class="flbc" title="{lang close}">{lang close}</a></span><!--{/if}-->
	</h3>
	<!--{if !submitcheck('editbkname')}-->
	<form method="post" autocomplete="off" id="bknameform_{$_GET[handlekey]}" name="bknameform_{$_GET[handlekey]}" action="home.php?mod=spacecp&ac=follow&op=bkname&fuid=$followuser['followuid']" {if $_G[inajax]}onsubmit="ajaxpost(this.id, 'return_$_GET[handlekey]');"{/if}>
		<input type="hidden" name="referer" value="{echo dreferer()}" />
		<input type="hidden" name="editbkname" value="true" />
		<!--{if $_G[inajax]}--><input type="hidden" name="handlekey" value="$_GET[handlekey]" /><!--{/if}-->
		<input type="hidden" name="formhash" value="{FORMHASH}" />
		<div class="c">
			<table>
				<tr>
					<th valign="top" width="60" class="avt"><a href="home.php?mod=space&uid=$followuser['followuid']"><!--{avatar($followuser['followuid'], 'small')}--></a></th>
					<td valign="top">{lang follow_editnote}: <input type="text" name="bkname" value="$followuser['bkname']" size="35" class="px"  onkeydown="ctrlEnter(event, 'editsubmit_btn');" />
					</td>
				</tr>
			</table>
		</div>
		<p class="o pns">
			<button type="submit" name="editsubmit_btn" id="editsubmit_btn" value="true" class="pn pnc"><strong>{lang save}</strong></button>
		</p>
	</form>
	<!--{/if}-->
	<script type="text/javascript" reload="1">
		function succeedhandle_$_GET[handlekey](url, msg, values) {
			$('$_GET[handlekey]').innerHTML = values['bkname'];
			$('fbkname_$followuser[followuid]').innerHTML = values['btnstr'];
		}
	</script>
<!--{elseif $op == 'relay'}-->
	<!--{if $_GET['from'] == 'forum'}-->
<div class="tip loginbox loginpop p5" id="floatlayout_follow">
	<h2 class="log_tit" id="return_rate"><a href="javascript:;" onclick="popup.close();"><span class="icon_close y">&nbsp;</span></a>{lang follow_reply}</h2>
	<form method="post" autocomplete="off" id="relayform_{$tid}" name="relayform_{$tid}" action="home.php?mod=spacecp&ac=follow&op=relay&tid=$tid">
		<input type="hidden" name="relaysubmit" value="true">
		<input type="hidden" name="referer" value="{echo dreferer()}">
		<input type="hidden" name="formhash" value="{FORMHASH}" />
		<input type="hidden" name="tid" value="$tid" />
		<!--{if $_G[inajax]}--><input type="hidden" name="handlekey" value="$_GET[handlekey]" /><!--{/if}-->
		<ul class="post_box cl">
			<dt class="mpt">
				<textarea id="note_{$tid}" name="note" cols="50" rows="5" class="pt mtn" placeholder="{lang follow_add_note}"></textarea>
			</dt>
			<!--{if $secqaacheck || $seccodecheck}-->
			<li class="flex-box mli">
				<div class="flex"><!--{block sectpl}--><sec> <span id="sec<hash>" onclick="showMenu({'ctrlid':this.id,'win':'{$_GET[handlekey]}'})"><sec></span><div id="sec<hash>_menu" class="p_pop p_opt" style="display:none"><sec></div><!--{/block}--></div>
				<div class="flex"><div class="mtm sec"><!--{subtemplate common/seccheck}--></div></div>
			</li>
			<!--{/if}-->
			<label>
			<li class="flex-box mli">
				<div class="flex">{lang post_add_inonetime}</div>
				<div class="flex"></div>
				<div class="flex"><input type="checkbox" name="addnewreply" checked="checked" class="pc" value="1" /></div>
			</li>
			</label>
		</ul>
		<button type="submit" name="relaysubmit_btn" id="relaysubmit_btn" class="pn pnc formdialog" value="true"><strong>{lang determine}</strong></button>
	</form>
</div>
	<!--{else}-->
		<div class="moodfm">
			<div class="moodfm_post">
				<form method="post" autocomplete="off" id="postform_{$tid}" action="home.php?mod=spacecp&ac=follow&op=relay&tid=$tid">
					<input type="hidden" name="relaysubmit" value="true">
					<input type="hidden" name="referer" value="{echo dreferer()}">
					<input type="hidden" name="formhash" value="{FORMHASH}" />
					<input type="hidden" name="tid" value="$tid" />
					<div class="moodfm_text">
						<textarea id="note_{$tid}" name="note" cols="80" rows="4" class="pts" placeholder="{lang follow_can_enter}140{lang follow_word}">{$comment['message']}</textarea>
					</div>

					<!--{if $secqaacheck || $seccodecheck}-->
					<!--{block sectpl}--><sec> <span id="sec<hash>" onclick="showMenu({'ctrlid':this.id,'win':'{$_GET[handlekey]}'})"><sec></span><div id="sec<hash>_menu" class="p_pop p_opt" style="display:none"><sec></div><!--{/block}-->
					<div class="mtm sec post_box"><ul class="cl"><!--{subtemplate common/seccheck}--></ul></div>
					<!--{/if}-->

					<div class="moodfm_f">
						<div class="moodfm_btn">
							<button type="submit" name="relaysubmit_btn" id="relaysubmit_btn" class="pgsbtn button" value="true" name="{if $_GET[action] == 'newthread'}topicsubmit{elseif $_GET[action] == 'reply'}replysubmit{/if}">{lang follow_reply}</button>
						</div>
						<!--{if $_G['group']['maxsigsize']}-->
						<div class="moodfm_signature">
							<label>
								<input type="checkbox" name="to_signhtml" class="pc" value="1" checked="checked" />
								<span>{lang post_add_inonetime}</span>
							</label>
						</div>
						<!--{/if}-->
					</div>
					<div id="return_$_GET[handlekey]"></div>
				</form>
			</div>
		</div>

		<div class="cl">
			<a href="javascript:;" onclick="replybox_close('relaybox_{$_GET['feedid']}')" class="replybox_close">{lang close}</a>
		</div>
	<!--{/if}-->
<!--{elseif $op == 'getfeed'}-->
	<!--{if !empty($list)}-->
	<!--{subtemplate home/follow_feed_li}-->
	<!--{else}-->
	false
	<!--{/if}-->
<!--{elseif $op == 'delete'}-->
	<div class="tip">
		<form method="post" autocomplete="off" id="deletefeed_{$_GET['feedid']}" name="deletefeed_{$_GET['feedid']}" action="home.php?mod=spacecp&ac=follow&op=delete&feedid=$_GET['feedid']">
			<input type="hidden" name="referer" value="{echo dreferer()}" />
			<input type="hidden" name="deletesubmit" value="true" />
			<input type="hidden" name="formhash" value="{FORMHASH}" />
			<!--{if $_G[inajax]}--><input type="hidden" name="handlekey" value="$_GET[handlekey]" /><!--{/if}-->
			<dt>
				<p>{lang follow_del_feed_confirm}</p>
			</dt>
			<dd>
				<button type="submit" name="btnsubmit" value="true" class="button z">{lang determine}</button>
				<a href="javascript:;" onclick="popup.close();" class="button y">{lang cancel}</a>
			</dd>
		</form>
	</div>
<!--{/if}-->

<!--{template common/footer}-->