<!--{eval $carray = array();}-->
<!--{eval $beforeuser = 0;}-->
<!--{eval $hiddennum = 0;}-->
<!--{loop $list['feed'] $feed}-->
	<!--{eval $content = $list['content'][$feed['tid']];}-->
	<!--{eval $thread = $list['threads'][$content['tid']];}-->
	<!--{if !empty($thread) && $thread['displayorder'] >= 0 || !empty($feed['note'])}-->
	<li class="list cl{if $lastviewtime && $feed[dateline] > $lastviewtime} unread{/if}" id="feed_li_$feed['feedid']">
		<div class="threadlist_top cl">
			<a href="home.php?mod=space&uid=$feed['uid']" class="mimg"><!--{avatar($feed['uid'],'small')}--></a>
			<div class="muser">
				<h3><a href="home.php?mod=space&uid={$thread['authorid']}" class="mmc">{$feed['username']}</a></h3>
				<span class="mtime">
					<!--{if $feed['uid'] == $_G['uid'] || $_G['adminid'] == 1}-->
					<em class="mgl y"><a href="home.php?mod=spacecp&ac=follow&feedid=$feed['feedid']&op=delete" id="c_delete_$feed['feedid']" class="dialog color">{lang delete}</a></em>
					<!--{/if}-->
					<!--{eval echo dgmdate($feed['dateline'], 'u');}-->
				</span>
			</div>
		</div>
		<div class="flw_article" {if $_GET['do'] == 'view' || $_GET['banavatar']}style="margin-left:0"{/if}>
			<!--{if $feed['note']}-->
				<div class="flw_quotenote xs2 pbw">
					$feed['note']
				</div>
				<div class="flw_quote">
			<!--{/if}-->
			<!--{if !empty($thread) && $thread['displayorder'] >= 0}-->
			<div class="wx pbn">
				<!--{if isset($carray[$feed['cid']])}-->
					<a href="javascript:;" onclick="vieworiginal(this, 'original_content_$feed[feedid]');return false;" class="flw_readfull y xw0 xs1 xi2">+ {lang follow_open_feed}</a>
				<!--{/if}-->
				<!--{if $thread['fid'] != $_G['setting']['followforumid']}-->
					<a href="forum.php?mod=viewthread&tid=$content['tid']&extra=page%3D1" target="_blank">$thread['subject']</a>
				<!--{/if}-->
			</div>

			<div class="flw_content pbm cl" id="original_content_$feed['feedid']" {if isset($carray[$feed['cid']])} style="display: none"{/if}>
				$content['content']
				<!--{if $thread['special'] && $thread['fid'] != $_G['setting']['followforumid']}-->
				<br/>
				<a href="forum.php?mod=viewthread&tid=$content['tid']&extra=page%3D1" target="_blank">{lang follow_special_thread_tip}</a>
				<!--{/if}-->
			</div>
			<div class="txtlist flw_foot cl">
				<!--{if $feed['note']}--><a href="home.php?mod=space&uid=$feed['uid']" class="flw_zbus">$thread['author']</a> {lang follow_post_by_time} <!--{date($thread['dateline'])}-->&nbsp;<!--{/if}-->
				<div class="mtit cl">
					<!--{if $thread['fid'] != $_G['setting']['followforumid'] && $_G['cache']['forums'][$thread['fid']]['name']}--><a href="forum.php?mod=forumdisplay&fid=$thread['fid']" class="ztxt">#{$_G['cache']['forums'][$thread['fid']]['name']}</a><!--{/if}-->
					<!--{if helper_access::check_module('follow')}--><a href="javascript:;" id="relay_$feed['feedid']" onclick="quickrelay($feed['feedid'], $thread['tid']);" class="ytxt">{lang follow_reply}($content['relay'])</a>&nbsp;<!--{/if}-->
					<a href="javascript:;" onclick="quickreply($thread['fid'], $thread['tid'], $feed['feedid'])" class="ytxt">{lang reply}($thread['replies'])</a>
				</div>
			</div>
			<!--{else}-->
			<div class="pbm c cl" id="original_content_$feed['feedid']" {if isset($carray[$feed['cid']])} style="display: none"{/if}>
				{lang follow_thread_deleted}
			</div>

			<!--{/if}-->
			<!--{if $feed['note']}--></div><!--{/if}-->
		</div>
		<div id="replybox_$feed['feedid']" class="flw_replybox cl" style="display: none;"></div>
		<div id="relaybox_$feed['feedid']" class="flw_replybox cl" style="display: none;"></div>
	</li>
	<!--{else}-->
		<!--{eval $hiddennum++;}-->
	<!--{/if}-->
	<!--{if !isset($carray[$feed['cid']])}-->
		<!--{eval $carray[$feed['cid']] = $feed['cid'];}-->
	<!--{/if}-->
<!--{/loop}-->
