<!--{template common/header}-->
<!--{eval $_G['home_tpl_titles'] = array('{lang message}');}-->
<div class="header cl">
	<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div>
	<h2>{lang message}</h2>
	<div class="my"><a href="index.php"><i class="dm-house"></i></a></div>
</div>

<div class="wall_list threadlist_box cl">
	<div class="moodfm">
		<!--{if helper_access::check_module('wall')}-->
		<form id="quickcommentform_{$space['uid']}" action="home.php?mod=spacecp&ac=comment" method="post" autocomplete="off">
			<div class="moodfm_post">
				<div class="moodfm_text">
					<!--{if $_G['uid'] || $_G['group']['allowcomment']}-->
						<textarea id="comment_message" name="message" rows="3" class="" placeholder=""></textarea>
					<!--{elseif $_G['connectguest']}-->
						<div class="pt hm">{lang connect_fill_profile_to_comment}</div>
					<!--{else}-->
						<div class="pt hm">{lang login_to_wall} <a href="member.php?mod=logging&action=login" class="xi2 dialog">{lang login}</a> | <a href="member.php?mod={$_G['setting']['regname']}" class="xi2">$_G['setting']['reglinkname']</a></div>
					<!--{/if}-->
				</div>
				<div class="moodfm_f">
					<div class="moodfm_btn">
						<button type="submit" name="commentsubmit_btn" value="true" id="commentsubmit_btn" class="pgsbtn button">{lang leave_comments}</button>
					</div>
				</div>
			</div>
			<p>
				<input type="hidden" name="referer" value="home.php?mod=space&uid=$wall['uid']&do=wall" />
				<input type="hidden" name="id" value="$space['uid']" />
				<input type="hidden" name="idtype" value="uid" />
				<input type="hidden" name="handlekey" value="qcwall_{$space['uid']}" />
				<input type="hidden" name="commentsubmit" value="true" />
				<input type="hidden" name="quickcomment" value="true" />
				<span id="return_qcwall_{$space['uid']}"></span>
			</p>
			<input type="hidden" name="formhash" value="{FORMHASH}" />
		</form>
		<!--{/if}-->
		<div id="div_main_content" class="doing_list_box threadlist cl">
			<!--{if $cid}-->
				<div class="wall_i">
					{lang view_one_operation_message},<a href="home.php?mod=space&uid=$space['uid']&do=wall">{lang click_view_message}</a>
				</div>
			<!--{/if}-->
			<ul>
				<!--{loop $list $k $value}-->
					<!--{template home/space_comment_li}-->
				<!--{/loop}-->
			</ul>
			<div class="pgs cl mtm">$multi</div>
		</div>
		<script type="text/javascript">
			var elems = qSelA('div.magicflicker');
			for(var i=0; i<elems.length; i++){
				magicColor(elems[i]);
			}
			function magicColor(elem, t) {
				t = t || 10;
				elem = (elem && elem.constructor == String) ? $(elem) : elem;
				if (!elem) {
					setTimeout(function() {
						magicColor(elem, t - 1);
					}, 400);
					return;
				}
				if (window.mcHandler == undefined) {
					window.mcHandler = {
						elements: []
					};
					window.mcHandler.colorIndex = 0;
					window.mcHandler.run = function() {
						var color = ["#CC0000", "#CC6D00", "#CCCC00", "#00CC00", "#0000CC", "#00CCCC", "#CC00CC"][(window.mcHandler.colorIndex++) % 7];
						for (var i = 0, L = window.mcHandler.elements.length; i < L; i++)
							window.mcHandler.elements[i].style.color = color;
					}
				}
				window.mcHandler.elements.push(elem);
				if (window.mcHandler.timer == undefined) {
					window.mcHandler.timer = setInterval(window.mcHandler.run, 500);
				}
			}
		</script>
	</div>
</div>
<!--{template common/footer}-->