<!--{template common/header}-->
<!--{eval $friendsname = array(1 => '{lang friendname_1}',2 => '{lang friendname_2}',3 => '{lang friendname_3}',4 => '{lang friendname_4}');}-->
<div class="header cl">
	<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div>
	<h2>{lang album}</h2>
</div>

<div class="dhnv flex-box cl">
	<a href="home.php?mod=space&do=album&view=me" class="flex{if $_GET['view'] == 'me'} mon{/if}">{lang my_album}</a>
	<a href="home.php?mod=space&do=album&view=we" class="flex{if $_GET['view'] == 'we'} mon{/if}">{lang friend_album}</a>
	<a href="home.php?mod=space&do=album&view=all" class="flex{if $_GET['view'] == 'all'} mon{/if}">{lang view_all}</a>
</div>

<div class="album_list">
	<!--{if $count}-->
		<ul class="album_ul cl">
			<!--{loop $list $key $value}-->
			<!--{eval $pwdkey = 'view_pwd_album_'.$value['albumid'];}-->
				<li>
					<div class="album_pic c">
						<a href="home.php?mod=space&uid=$value['uid']&do=album&id=$value['albumid']">
							<!--{if $value['pic']}--><img src="$value['pic']"/><!--{/if}-->
							<p><!--{if $value['albumname']}-->$value['albumname']<!--{else}-->{lang default_album}<!--{/if}--></p>
						</a>
					</div>
				</li>
			<!--{/loop}-->
		</ul>
	<!--{if $multi}--><div class="pgs cl mtm">$multi</div><!--{/if}-->
	<!--{else}-->
		<div class="threadlist_box cl">
			<h4>{lang no_album}</h4>
		</div>
	<!--{/if}-->
</div>



<script type="text/javascript">
function fuidgoto(fuid) {
	var parameter = fuid != '' ? '&fuid='+fuid : '';
	window.location.href = 'home.php?mod=space&do=album&view=we'+parameter;
}
</script>

<!--{template common/footer}-->