<!--{template common/header}-->
<div class="tip loginbox loginpop p5">
<!--{if $_GET['op'] == 'delete'}-->
	<form id="shareform_{$sid}" name="shareform_{$sid}" method="post" autocomplete="off" action="home.php?mod=spacecp&ac=share&op=delete&sid=$sid&type=$_GET['type']&mobile=2">
		<input type="hidden" name="referer" value="{eval echo dreferer();}" />
		<input type="hidden" name="deletesubmit" value="true" />
		<input type="hidden" name="formhash" value="{FORMHASH}" />
		<dt>{lang delete_share_message}</dt>
		<dd>
			<button type="submit" name="deletesubmitbtn" value="true" class="pn pnc"><strong>{lang determine}</strong></button>
		</dd>
	</form>
<!--{elseif $_GET['op'] == 'edithot'}-->
	<form method="post" autocomplete="off" action="home.php?mod=spacecp&ac=share&op=edithot&sid=$sid&mobile=2">
		<input type="hidden" name="referer" value="{eval echo dreferer();}" />
		<input type="hidden" name="hotsubmit" value="true" />
		<input type="hidden" name="formhash" value="{FORMHASH}" />
		<div class="c">{lang new_hot}:<input type="text" name="hot" value="$share[hot]" size="10" class="px" /></div>
		<p class="o pns">
			<button type="submit" name="btnsubmit" value="true" class="pn pnc"><strong>{lang determine}</strong></button>
		</p>
	</form>
<!--{elseif $_GET['op']=='link'}-->
	<!--{if !$_G[inajax]}-->
		<h1 class="mt">
			<i class="fico-share fic4 fc-p vm"></i>{lang share}
		</h1>
	<!--{else}-->
		<h3 class="flb">
			<em id="return_$_GET[handlekey]">{lang share}</em>
			<!--{if $_G[inajax]}--><span><a href="javascript:;" onclick="hideWindow('$_GET[handlekey]');" class="flbc" title="{lang close}">{lang close}</a></span><!--{/if}-->
		</h3>
	<!--{/if}-->
	<form id="shareform" name="shareform" action="home.php?mod=spacecp&ac=share&type=link&mobile=2" method="post" autocomplete="off" >
		<input type="hidden" name="refer" value="home.php?mod=space&uid=$space['uid']&do=share&view=me" />
		<input type="hidden" name="topicid" value="$_GET['topicid']" />
		<input type="hidden" name="formhash" value="{FORMHASH}" />
		<input type="hidden" name="sharesubmit" value="true" />
		<div class="c tfm">
			<p>{lang share_web_music_flash}:</p>
			<p class="mtn mbm"><input type="text" size="30" class="px" name="link" onfocus="javascript:if('http://'==this.value)this.value='';" onblur="javascript:if(''==this.value)this.value='http://'" id="share_link" value="$linkdefault" /></p>
			<p>{lang description}:</p>
			<p class="mtn mbm"><textarea id="share_general" name="general" cols="30" rows="3" class="pt" onkeydown="ctrlEnter(event, 'sharesubmit_btn')">$generaldefault</textarea></p>
			<!--{if $type == 'thread'}-->
				<p><a href="javascript:;" onclick="setCopy($('share_general').value + '\n ' + $('share_link').value, '{lang share_copylink}')" />{lang share_im}</a></p>
			<!--{/if}-->
			<!--{if $secqaacheck || $seccodecheck}-->
				<!--{block sectpl}--><sec> <span id="sec<hash>" class="secq" onclick="showMenu({'ctrlid':this.id,'win':'{$_GET[handlekey]}'})"><sec></span><div id="sec<hash>_menu" class="p_pop p_opt" style="display:none"><sec></div><!--{/block}-->
				<div class="sec"><!--{subtemplate common/seccheck}--></div>
			<!--{/if}-->
		</div>
		<p class="o pns">
			<button type="submit" name="sharesubmit_btn" id="sharesubmit_btn" value="true" class="pn pnc"><strong>{lang share}</strong></button>
		</p>
	</form>
<!--{else}-->
	<form method="post" autocomplete="off" id="shareform_{$id}" name="shareform_{$id}" action="home.php?mod=spacecp&ac=share&type=$type&id=$id&mobile=2">
		<input type="hidden" name="sharesubmit" value="true">
		<input type="hidden" name="referer" value="{eval echo dreferer();}">
		<input type="hidden" name="formhash" value="{FORMHASH}" />
		<dt>
			<span class="y xg1">{lang share_count}&nbsp;&nbsp;</span>
			<span class="z">{lang share_description}:</span>
		</dt>
		<dt>
			<textarea id="general_{$id}" name="general" cols="50" rows="5" class="pt mtn" placeholder="{lang share_description}"></textarea>
			<!--{if $secqaacheck || $seccodecheck}-->
			<!--{block sectpl}--><sec> <span id="sec<hash>" onclick="showMenu({'ctrlid':this.id,'win':'{$_GET[handlekey]}'})"><sec></span><div id="sec<hash>_menu" class="p_pop p_opt" style="display:none"><sec></div><!--{/block}-->
			<div class="mtm sec"><!--{subtemplate common/seccheck}--></div>
			<!--{/if}-->
		</dt>
		<ul class="post_box cl">
			<!--{if $commentcable[$type]}-->
			<label><li class="flex-box b0"><div class="flex tit"><!--{if $type == 'thread'}-->{lang post_add_inonetime}<!--{else}-->{lang comment_add_inonetime}<!--{/if}--></div><div class="flex"></div><div class="flex y"><input type="checkbox" class="pc" name="iscomment" value="1"/></div></li></label>
			<!--{/if}-->
		</ul>
		<dd>
			<button type="submit" name="sharesubmit_btn" id="sharesubmit_btn" class="pn pnc formdialog" value="true"><strong>{lang determine}</strong></button>
		</dd>
	</form>
<!--{/if}-->
</div>
<!--{template common/footer}-->