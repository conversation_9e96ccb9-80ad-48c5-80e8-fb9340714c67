<!--{template common/header}-->

<div class="header cl">
	<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div>
	<h2>{lang share}</h2>
	<div class="my"><a href="index.php"><i class="dm-house"></i></a></div>
</div>

<div id="ct" class="cl">
	<div class="dhnv flex-box cl" style="border-top: 0;">
		<a href="{if !$_G['uid']}member.php?mod=logging&action=login{else}home.php?mod=space&do=share&view=we{/if}" class="flex{if $actives['we']} mon{/if}{if !$_G['uid']} dialog{/if}">{lang friend_share}</a>
		<a href="{if !$_G['uid']}member.php?mod=logging&action=login{else}home.php?mod=space&do=share&view=me{/if}" class="flex{if $actives['me']} mon{/if}{if !$_G['uid']} dialog{/if}">{lang my_share}</a>
		<a href="home.php?mod=space&do=share&view=all" class="flex{if $actives['all']} mon{/if}">{lang view_all}</a>
	</div>

	<div class="bodybox p10 cl">
	<!--{if helper_access::check_module('share') && $space[self]}-->
	<!--{template home/space_share_form}-->
	<!--{/if}-->

	<p class="tbmu">
		{lang order_by_type}:
		<a href="$navtheurl&type=all"$sub_actives[type_all]>{lang share_all}</a><span class="pipe">|</span>
		<a href="$navtheurl&type=link"$sub_actives[type_link]>{lang share_link}</a><span class="pipe">|</span>
		<a href="$navtheurl&type=video"$sub_actives[type_video]>{lang share_video}</a><span class="pipe">|</span>
		<a href="$navtheurl&type=music"$sub_actives[type_music]>{lang share_music}</a><span class="pipe">|</span>
		<a href="$navtheurl&type=flash"$sub_actives[type_flash]>{lang share_flash}</a><span class="pipe">|</span>
		<!--{if helper_access::check_module('blog')}-->
		<a href="$navtheurl&type=blog"$sub_actives[type_blog]>{lang share_blog}</a><span class="pipe">|</span>
		<!--{/if}-->
		<!--{if helper_access::check_module('album')}-->
		<a href="$navtheurl&type=album"$sub_actives[type_album]>{lang share_album}</a><span class="pipe">|</span>
		<a href="$navtheurl&type=pic"$sub_actives[type_pic]>{lang share_pic}</a><span class="pipe">|</span>
		<!--{/if}-->
		<a href="$navtheurl&type=poll"$sub_actives[type_poll]>{lang share_poll}</a><span class="pipe">|</span>
		<a href="$navtheurl&type=space"$sub_actives[type_space]>{lang share_space}</a><span class="pipe">|</span>
		<a href="$navtheurl&type=thread"$sub_actives[type_thread]>{lang share_thread}</a>
		<!--{if helper_access::check_module('portal')}-->
		<span class="pipe">|</span>
		<a href="$navtheurl&type=article"$sub_actives[type_article]>{lang share_article}</a>
		<!--{/if}-->
	</p>
	<!--{if $list}-->
		<ul id="share_ul" class="el sl">
			<!--{loop $list $k $value}-->
				<!--{template home/space_share_li}-->
			<!--{/loop}-->
		</ul>
		<!--{if $pricount}-->
			<p class="mt10">{lang hide_share}</p>
		<!--{/if}-->
		<!--{if $multi}--><div class="pgs cl mt10">$multi</div><!--{/if}-->
	<!--{else}-->
		<ul id="share_ul" class="el sl"></ul>
		<p class="emp">{lang not_share_yet}</p>
	<!--{/if}-->
</div>


<script type="text/javascript">
	function succeedhandle_shareadd(url, msg, values) {
		share_add(values['sid']);
	}
</script>
<!--{template common/footer}-->