<!--{template common/header}-->
<!--{if $op != ''}-->
	<div class="threadlist_box mt10 cl">
		<div class="threadlist cl">
			<ul>
				<h4>{lang user_mobile_pm_error}</h4>
			</ul>
		</div>
	</div>
<!--{else}-->
<form id="pmform_{$pmid}" name="pmform_{$pmid}" method="post" autocomplete="off" action="home.php?mod=spacecp&ac=pm&op=send&touid=$touid&pmid=$pmid&mobile=2" >
	<input type="hidden" name="referer" value="{echo dreferer();}" />
	<input type="hidden" name="pmsubmit" value="true" />
	<input type="hidden" name="formhash" value="{FORMHASH}" />
	<div class="header cl">
		<div class="mz"><a href="home.php?mod=space&do=pm"><i class="dm-c-left"></i></a></div>
		<h2>{lang send_pm}</h2>		
	</div>
	<div class="post_box">
		<ul>
			<!--{if !$touid}-->
			<li class="mli"><input type="text" value="" class="px" autocomplete="off" id="username" name="username" placeholder="{lang addressee}"></li>
			<!--{/if}-->
			<li class="mtext">
				<textarea class="pt" autocomplete="off" value="" id="sendmessage" name="message" placeholder="{lang thread_content}"></textarea>
			</li>
		</ul>
	</div>	
	<div class="post_btn"><button id="pmsubmit_btn" class="pn btn_pn mgrey" disable="true">{lang sendpm}</button></div>
</form>
<script type="text/javascript">
	(function() {
		$('#sendmessage').on('keyup input', function() {
			var obj = $(this);
			if(obj.val()) {
				$('.btn_pn').removeClass('mgrey').addClass('mblue');
				$('.btn_pn').attr('disable', 'false');
			} else {
				$('.btn_pn').removeClass('mblue').addClass('mgrey');
				$('.btn_pn').attr('disable', 'true');
			}
		});
		var form = $('#pmform_{$pmid}');
		$('#pmsubmit_btn').on('click', function() {
			var obj = $(this);
			if(obj.attr('disable') == 'true') {
				return false;
			}
			$.ajax({
				type:'POST',
				url:form.attr('action') + '&handlekey='+form.attr('id')+'&inajax=1',
				data:form.serialize(),
				dataType:'xml'
			})
			.success(function(s) {
				popup.open(s.lastChild.firstChild.nodeValue);
			})
			.error(function() {
				popup.open('{lang networkerror}', 'alert');
			});
			return false;
			});
	 })();
</script>
<!--{/if}-->
<!--{template common/footer}-->