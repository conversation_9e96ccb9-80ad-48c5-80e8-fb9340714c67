<!--{template common/header}-->
<div class="tip loginbox loginpop p5" id="floatlayout_ecrate">
	<h2 class="log_tit" id="return_ecrate">{lang eccredit}</h2>
	<form method="post" autocomplete="off" action="home.php?mod=spacecp&ac=eccredit&op=rate" id="postform">
		<input type="hidden" name="formhash" value="{FORMHASH}" />
		<input type="hidden" name="referer" value="{echo dreferer()}" />
		<ul class="post_box cl">
			<li class="flex-box mli">
				<div class="flex"><span class="z">{lang eccredit_retee}</span></div>
				<div class="flex-3">
				<!--{if $_G['uid'] == $order['buyerid']}-->
					<a href="home.php?mod=space&uid=$order['sellerid']" target="_blank">$order['seller']</a>
				<!--{else}-->
					<a href="home.php?mod=space&uid=$order['buyerid']" target="_blank">$order['buyer']</a>
				<!--{/if}-->
				</div>
			</li>
			<li class="flex-box mli">
				<div class="flex"><span class="z">{lang eccredit_tradegoods}</span></div>
				<div class="flex-3"><a href="forum.php?mod=redirect&goto=findpost&pid=$order['pid']" target="_blank">$order['subject']</a></div>
			</li>
			<li class="flex-box mli">
				<div class="flex"><span class="z">{lang rate}</span></div>
				<div class="flex-3 xs1">
					<label><input id="rate_good" name="score" value="1" type="radio" class="pr" checked="checked" /><span style="color:red"><strong>{lang eccredit_good}</strong><!--{if !$order['offline']}-->{lang eccredit_good_comment}<!--{/if}--></span></label>
					<label><input id="rate_soso" name="score" value="0" type="radio" class="pr" /><span style="color:green"><strong>{lang eccredit_soso}</strong><!--{if !$order['offline']}-->{lang eccredit_soso_comment}<!--{/if}--></span></label>
					<label><input id="rate_bad" name="score" value="-1" type="radio" class="pr" /><strong>{lang eccredit_bad}</strong><!--{if !$order['offline']}-->{lang eccredit_bad_comment}<!--{/if}--></label>
				</div>
			</li>
		</ul>
		<dt class="mpt">
			<textarea name="message" rows="5" cols="60" maxlength="50" class="px pxbg" placeholder="{lang eccredit1}"></textarea>
		</dt>
		<dd>
			<input type="hidden" name="orderid" value="$orderid">
			<input type="hidden" name="type" value="$type">
			<button type="submit" class="pn pnc" id="postsubmit" name="ratesubmit" value="true"><strong>{lang submit}</strong></button>
		</dd>
	</form>
</div>
<!--{template common/footer}-->