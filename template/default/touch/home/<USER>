<!--{eval $_G['home_tpl_titles'] = array('{lang pm}');}-->
<!--{template common/header}-->
<!--{if in_array($filter, array('privatepm')) || in_array($_GET['subop'], array('view'))}-->
	<!--{if in_array($filter, array('privatepm'))}-->
	<div class="header cl">
		<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div>
		<h2>{lang mypm}</h2>
		<div class="my"><a href="home.php?mod=spacecp&ac=pm"><i class="dm-edit"></i></a></div>
	</div>
	<div class="dhnv flex-box cl">
		<a href="home.php?mod=space&do=pm" class="flex mon">{lang mypm}<!--{if $newpmcount}--><strong>($newpmcount)</strong><!--{/if}--></a>
		<a href="home.php?mod=space&do=notice" class="flex">{lang my}{lang remind}<!--{if $_G['member']['newprompt']}--><strong>($_G['member']['newprompt'])</strong><!--{/if}--></a>
	</div>
	<div id="pmlist" class="imglist mt10 cl">
		<ul>
			<!--{loop $list $key $value}-->
			<li>
				<span class="mimg"><a href="{if $value['touid']}home.php?mod=space&do=pm&subop=view&touid=$value['touid']{else}home.php?mod=space&do=pm&subop=view&plid={$value['plid']}&type=1{/if}"><img src="<!--{if $value['pmtype'] == 2}-->{STATICURL}image/common/grouppm.png<!--{else}--><!--{avatar($value['touid'] ? $value['touid'] : ($value['lastauthorid'] ? $value['lastauthorid'] : $value['authorid']), 'small', true)}--><!--{/if}-->"></a></span>
				<a href="{if $value['touid']}home.php?mod=space&do=pm&subop=view&touid=$value['touid']{else}home.php?mod=space&do=pm&subop=view&plid={$value['plid']}&type=1{/if}">
					<p class="mtit">
						<span class="mtime"><!--{date($value['dateline'], 'u')}--></span>
						<!--{if $value['new']}--><span class="mnum">$value['pmnum']</span><!--{/if}-->
						<!--{if $value['touid']}-->
							<!--{if $value['msgfromid'] == $_G['uid']}-->
								{lang me}{lang you_to} {$value['tousername']} {lang say}:
							<!--{else}-->
								{$value['tousername']} {lang you_to}{lang me} {lang say}:
							<!--{/if}-->
						<!--{elseif $value['pmtype'] == 2}-->
							{lang chatpm_author}:$value['firstauthor']
						<!--{/if}-->
					</p>
					<p class="mtxt">
						<!--{if $value['pmtype'] == 2}-->[{lang chatpm}]<!--{if $value['subject']}-->$value['subject']<br><!--{/if}--><!--{/if}--><!--{if $value['pmtype'] == 2 && $value['lastauthor']}-->$value['lastauthor'] : $value['message']<!--{else}-->$value['message']<!--{/if}-->
					</p>
				</a>
			</li>
			<!--{/loop}-->
		</ul>
	</div>
	<!--{elseif in_array($_GET['subop'], array('view'))}-->
		<!--{eval $msguser = $tousername;}-->
		<div class="header cl">
			<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div>
			<h2>{if $chatpmmember}{lang viewmypm}{else}{lang taking_with_user}{/if}</h2>
			<div class="my"><a href="index.php"><i class="dm-house"></i></a></div>
		</div>
		<div class="msgbox b_m">
			<!--{if !$list}-->
				<div class="threadlist_box mt10 cl">
					<h4>{lang no_corresponding_pm}</h4>
				</div>
			<!--{else}-->
				<!--{loop $list $key $value}-->
					<!--{subtemplate home/space_pm_node}-->
				<!--{/loop}-->
				$multi
				<div id="dumppage" style="display:none">
			<!--{/if}-->
		</div>
		<form id="pmform" class="pmform" name="pmform" method="post" action="home.php?mod=spacecp&ac=pm&op=send&pmid=$pmid&daterange=$daterange&pmsubmit=yes&mobile=2" >
			<input type="hidden" name="formhash" value="{FORMHASH}" />
			<!--{if !$touid}-->
			<input type="hidden" name="plid" value="$plid" />
			<!--{else}-->
			<input type="hidden" name="touid" value="$touid" />
			<!--{/if}-->
			<div class="foot_height"></div>
			<div class="foot msg_post flex-box">
				<input type="text" value="" class="flex px" autocomplete="off" id="replymessage" name="message" placeholder="{lang mobsendpm}" />
				<input type="button" name="pmsubmit" id="pmsubmit" class="formdialog pns" value="{lang sendpm}" />
			</div>
		</form>
		<!--{eval $nofooter = true;}-->
	<!--{/if}-->
<!--{else}-->
	<div class="threadlist_box mt10 cl">
		<div class="threadlist cl">
			<h4>{lang user_mobile_pm_error}</h4>
		</div>
	</div>
<!--{/if}-->
<!--{template common/footer}-->
