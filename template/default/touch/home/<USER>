<!--{template common/header}-->
<div class="header cl">
	<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div>
	<h2>{lang medals}</h2>
</div>


<div class="dhnv flex-box cl">
	<a href="home.php?mod=medal" class="flex{if empty($_GET['action'])} mon{/if}">{lang medals_center}</a>
	<a href="home.php?mod=medal&action=log" class="flex{if $_GET['action'] == 'log'} mon{/if}">{lang my_medals}</a>
</div>

<div id="medal_ul" class="medallist threadlist cl">
	<!--{if empty($_GET['action'])}-->
		<!--{if $medallist}-->
			<!--{if $medalcredits}-->
				<div class="tbmu">
					{lang you_have_now}
					<!--{eval $i = 0;}-->
					<!--{loop $medalcredits $id}-->
						<!--{if $i != 0}-->, <!--{/if}-->{$_G['setting']['extcredits'][$id]['img']} {$_G['setting']['extcredits'][$id]['title']} <span class="xi1"><!--{echo getuserprofile('extcredits'.$id);}--></span> {$_G['setting']['extcredits'][$id]['unit']}
						<!--{eval $i++;}-->
					<!--{/loop}-->
				</div>
			<!--{/if}-->
			<ul>
				<!--{loop $medallist $key $medal}-->
				<li class="list">
					<div class="medal_top cl">
						<div id="medal_$medal['medalid']" class="mg_img">
							<img src="$medal['image']" alt="$medal['name']"/>
						</div>
						<div class="mg_tit">
							<h3>$medal['name']</h3>
							<span>
							<!--{if $medal['expiration']}-->
								{lang expire} $medal['expiration'] {lang days},
							<!--{/if}-->
							<!--{if $medal['permission'] && !$medal['price']}-->
								$medal['permission']
							<!--{else}-->
								<!--{if $medal['type'] == 0}-->
									{lang medals_type_0}
								<!--{elseif $medal['type'] == 1}-->
									<!--{if $medal['price']}-->
										<!--{if {$_G['setting']['extcredits'][$medal['credit']]['unit']}}-->
											{$_G['setting']['extcredits'][$medal['credit']]['title']} <strong class="xi1 xw1 xs2">$medal['price']</strong> {$_G['setting']['extcredits'][$medal['credit']]['unit']}
										<!--{else}-->
											<strong class="xi1 xw1 xs2">$medal['price']</strong> {$_G['setting']['extcredits'][$medal['credit']]['title']}
										<!--{/if}-->
									<!--{else}-->
										{lang medals_type_1}
									<!--{/if}-->
								<!--{elseif $medal['type'] == 2}-->
									{lang medals_type_2}
								<!--{/if}-->
							<!--{/if}-->
							</span>
						</div>
						<div class="medal_btn">
							<!--{if in_array($medal['medalid'], $membermedal)}-->
								<span class="hui">
									{lang space_medal_have}
								</span>
							<!--{else}-->
								<!--{if $medal['type'] && $_G['uid']}-->
									<!--{if in_array($medal['medalid'], $mymedals)}-->
									<span class="hui">
										<!--{if $medal['price']}-->
											{lang space_medal_purchased}
										<!--{else}-->
											<!--{if !$medal['permission']}-->
												{lang space_medal_applied}
											<!--{else}-->
												{lang space_medal_receive}
											<!--{/if}-->
										<!--{/if}-->
									</span>
									<!--{else}-->
										<a href="home.php?mod=medal&action=confirm&medalid=$medal['medalid']" class="dialog">
											<!--{if $medal['price']}-->
												{lang space_medal_buy}
											<!--{else}-->
												<!--{if !$medal['permission']}-->
													{lang medals_apply}
												<!--{else}-->
													{lang medals_draw}
												<!--{/if}-->
											<!--{/if}-->
										</a>
									<!--{/if}-->
								<!--{/if}-->
							<!--{/if}-->
						</div>
					</div>
					<div id="medal_$medal['medalid']_msg" class="medal_msg">
						<p>$medal['description']</p>
					</div>
				</li>
				<!--{/loop}-->
			</ul>
		<!--{else}-->
			<!--{if $medallogs}-->
				<div class="threadlist_box mt10 cl">
					<h4>{lang medals_nonexistence}</h4>
				</div>
			<!--{else}-->
				<div class="threadlist_box mt10 cl">
					<h4>{lang medals_noavailable}</h4>
				</div>
			<!--{/if}-->
		<!--{/if}-->

		<!--{if $lastmedals}-->
			<div class="discuz_x cl"></div>
			<div class="txtlist cl">
				<div class="mtit cl">{lang medals_record}</div>
			</div>
			<div class="last_medal">
				<ul>
					<!--{loop $lastmedals $lastmedal}-->
					<li>
						<a href="home.php?mod=space&uid=$lastmedal['uid']&do=profile">
							<!--{avatar($lastmedal['uid'],'small')}-->
							<div class="last_med_name">
								<span class="name">$lastmedalusers[$lastmedal['uid']]['username']</span>
							</div>
							<div class="last_medal_msg">
								<span> {lang medals_message1} $lastmedal['dateline'] {lang medals_message2} <strong>$medallist[$lastmedal['medalid']]['name']</strong> {lang medals}</span>
							</div>
						</a>
					</li>
					<!--{/loop}-->
				</ul>
			</div>
		<!--{/if}-->
	<!--{elseif $_GET['action'] == 'log'}-->

		<!--{if $mymedals}-->
			<ul class="mgcl cl">
				<!--{loop $mymedals $mymedal}-->
				<li>
					<div class="mgcl_box">
						<div class="mg_img"><img src="$mymedal['image']" alt="$mymedal['name']" class="vm" /></div>
						<p>$mymedal['name']</p>
					</div>
				</li>
				<!--{/loop}-->
			</ul>
		<!--{/if}-->

		<!--{if $medallogs}-->
			<div class="txtlist cl">
				<div class="mtit cl">{lang medals_record}</div>
			</div>
			<div class="last_medal">
				<ul>
					<!--{loop $medallogs $medallog}-->
					<li>
						<!--{if $medallog['type'] == 2 || $medallog['type'] == 3}-->
							{lang medals_message3} $medallog['dateline'] {lang medals_message4} <strong>$medallog['name']</strong> {lang medals},<!--{if $medallog['type'] == 2}-->{lang medals_operation_2}<!--{elseif $medallog['type'] == 3}-->{lang medals_operation_3}<!--{/if}-->
						<!--{elseif $medallog['type'] != 2 && $medallog['type'] != 3}-->
							{lang medals_message3} $medallog['dateline'] {lang medals_message5} <strong>$medallog['name']</strong> {lang medals},<!--{if $medallog['expiration']}-->{lang expire}: $medallog['expiration']<!--{else}-->{lang medals_noexpire}<!--{/if}-->
						<!--{/if}-->
					</li>
					<!--{/loop}-->
				</ul>
			</div>
			<!--{if $multipage}--><div class="pgs cl mtm">$multipage</div><!--{/if}-->
		<!--{else}-->
			<div class="threadlist_box mt10 cl">
				<h4>{lang medals_nonexistence_own}</h4>
			</div>
		<!--{/if}-->
	<!--{/if}-->
</div>

<!--{template common/footer}-->