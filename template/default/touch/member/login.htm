<!--{template common/header}-->
<!--{if !$_GET['infloat']}-->
<div class="header cl">
	<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div><h2></h2><div class="my"></div>
</div>
<div class="header_toplogo">{$_G['style']['touchlogo']}<p><!--{if !isset($_GET['viewlostpw'])}-->{lang login}<!--{else}-->{lang getpassword}<!--{/if}--></p></div>
<!--{/if}-->
<!--{hook/logging_top_mobile}-->
<!--{eval $loginhash = 'L'.random(4);}-->
<div class="loginbox <!--{if $_GET['infloat']}-->login_pop<!--{/if}-->">
<!--{if $_GET['infloat']}-->
	<h2 class="log_tit"><a href="javascript:;" onclick="popup.close();"><span class="icon_close y">&nbsp;</span></a><!--{if !isset($_GET['viewlostpw'])}-->{lang login}<!--{else}-->{lang getpassword}<!--{/if}--></h2>
<!--{/if}-->
<!--{if !isset($_GET['viewlostpw'])}-->
	<form id="loginform" method="post" action="member.php?mod=logging&action=login&loginsubmit=yes&loginhash=$loginhash&mobile=2" onsubmit="{if $_G['setting']['pwdsafety']}pwmd5('password3_$loginhash');{/if}" >
	<input type="hidden" name="formhash" id="formhash" value='{FORMHASH}' />
	<input type="hidden" name="referer" id="referer" value="<!--{if dreferer()}-->{echo dreferer()}<!--{else}-->forum.php?mobile=2<!--{/if}-->" />
	<input type="hidden" name="fastloginfield" value="username">
	<input type="hidden" name="cookietime" value="2592000">
	<!--{if $auth}-->
		<input type="hidden" name="auth" value="$auth" />
	<!--{/if}-->
	<div class="login_from">
		<ul>
			<li><input type="text" value="" class="px" autocomplete="off" value="" name="username" placeholder="{lang inputyourname}" fwin="login"></li>
			<li><input type="password" class="px" value="" name="password" placeholder="{lang login_password}" fwin="login"></li>
			<li class="questionli">
				<div class="login_select">
				<span class="login-btn-inner">
					<span class="login-btn-text">
						<span class="span_question">{lang security_question}</span>
					</span>
					<i class="dm-c-right icon-arrow"></i>
				</span>
				<select id="questionid_{$loginhash}" name="questionid" class="sel_list">
					<option value="0" selected="selected">{lang security_question}</option>
					<option value="1">{lang security_question_1}</option>
					<option value="2">{lang security_question_2}</option>
					<option value="3">{lang security_question_3}</option>
					<option value="4">{lang security_question_4}</option>
					<option value="5">{lang security_question_5}</option>
					<option value="6">{lang security_question_6}</option>
					<option value="7">{lang security_question_7}</option>
				</select>
				</div>
			</li>
			<li class="answerli" style="display:none;"><input type="text" name="answer" id="answer_{$loginhash}" class="px" placeholder="{lang security_a}"></li>
		</ul>
		<!--{if $seccodecheck || $secqaacheck}-->
		<!--{subtemplate common/seccheck}-->
		<!--{/if}-->
	</div>
	<div class="btn_login"><button value="true" name="submit" type="submit" class="formdialog pn">{lang login}</button></div>
	</form>
	<!--{if $_G['setting']['connect']['allow'] && !$_G['setting']['bbclosed']}-->
	<div class="btn_qqlogin"><a href="$_G['connect']['login_url']&statfrom=login_simple" class="pn">{lang qqconnect:connect_mobile_login}</a></div>
	<!--{/if}-->
	<!--{if $_G['setting']['regstatus']}-->
	<div class="reg_link"><a href="member.php?mod=logging&action=login&lostpasswd=yes&viewlostpw=1" class="y">{lang getpassword}</a><a href="member.php?mod={$_G['setting']['regname']}" class="reg_now">{lang noregister}</a></div>
	<!--{/if}-->
<!--{else}-->
	<form id="lostpwform" method="post" action="member.php?mod=lostpasswd&lostpwsubmit=yes&infloat=yes&mobile=2" autocomplete="off">
		<input type="hidden" name="formhash" value="{FORMHASH}" />
		<input type="hidden" name="handlekey" value="lostpwform" />
		<div class="login_from">
			<ul>
				<li><input type="text" class="px" value="" name="email" placeholder="{lang email}" fwin="login"></li>
				<li><input type="text" class="px" value="" name="username" placeholder="{lang inputyourname}" autocomplete="off" fwin="login"></li>
			</ul>
		</div>
		<div class="btn_login"><button value="true" name="lostpwsubmit" type="submit" class="formdialog pn">{lang submit}</button></div>
	</form>
<!--{/if}-->
	<!--{hook/logging_bottom_mobile}-->
</div>
<!--{if $_G['setting']['pwdsafety']}-->
	<script type="text/javascript" src="{$_G['setting']['jspath']}md5.js?{VERHASH}" reload="1"></script>
<!--{/if}-->
<!--{eval updatesession();}-->
<script type="text/javascript">
	(function() {
		document.addEventListener("change", function(event) {
			if (!event.target.matches(".sel_list")) return;
			var obj = event.target;
			var span_question = qSel(".span_question");
			span_question.textContent = obj.options[obj.selectedIndex].text;
			var answerli = qSel(".answerli");
			var questionli = qSel(".questionli");
			if (obj.value == 0) {
				answerli.style.display = "none";
				questionli.classList.add("bl_none");
			} else {
				answerli.style.display = "block";
				questionli.classList.remove("bl_none");
			}
		});
	})();
</script>
<!--{template common/footer}-->