<!--{template common/header}-->
<div class="header cl">
	<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div><h2></h2><div class="my"></div>
</div>
<div class="header_toplogo">{$_G['style']['touchlogo']}<p>$_G['setting']['reglinkname']</p></div>
<!--{hook/register_top_mobile}-->
<div class="loginbox registerbox">
	<div class="login_from post_box">
		<form method="post" autocomplete="off" name="register" id="registerform" action="member.php?mod={$_G['setting']['regname']}&mobile=2">
		<input type="hidden" name="regsubmit" value="yes" />
		<input type="hidden" name="formhash" value="{FORMHASH}" />
		<!--{eval $dreferer = str_replace('&amp;', '&', dreferer());}-->
		<input type="hidden" name="referer" value="$dreferer" />
		<input type="hidden" name="activationauth" value="{if $_GET['action'] == 'activation'}$activationauth{/if}" />
		<!--{if $_G['setting']['sendregisterurl']}-->
			<input type="hidden" name="hash" value="$_GET['hash']" />
		<!--{/if}-->
		<ul>
			<!--{if $sendurl}-->
				<li class="mli"><input type="email" class="px" autocomplete="off" value="" name="{$_G['setting']['reginput']['email']}" placeholder="{lang registeremail}" fwin="login"></li>
			<!--{else}-->
				<!--{if empty($invite) && $_G['setting']['regstatus'] == 2 && !$invitestatus}-->
					<li class="mli">
						<input type="text" class="px" autocomplete="off" value="" name="invitecode" placeholder="{lang invite_code}" fwin="login">
						<!--{if $this->setting['inviteconfig']['buyinvitecode'] && $this->setting['inviteconfig']['invitecodeprice'] && payment::enable()}-->
						<a href="misc.php?mod=buyinvitecode" class="input-append">{lang register_buyinvitecode}</a>
						<!--{/if}-->
					</li>
				<!--{/if}-->
				<li class="mli"><input type="text" class="px" autocomplete="off" value="" name="{$_G['setting']['reginput']['username']}" placeholder="{lang registerinputtip}" fwin="login"></li>
				<li class="mli"><input type="password" class="px" value="" name="{$_G['setting']['reginput']['password']}" placeholder="{lang login_password}" fwin="login"></li>
				<li class="mli"><input type="password" class="px" value="" name="{$_G['setting']['reginput']['password2']}" placeholder="{lang registerpassword2}" fwin="login"></li>
				<li class="mli"><input type="email" class="px" autocomplete="off" value="$hash[0]" name="{$_G['setting']['reginput']['email']}" placeholder="{lang registeremail}" fwin="login"></li>
				<!--{if $_G['setting']['regverify'] == 2}-->
					<li class="mli"><input type="text" class="px" autocomplete="off" value="{lang register_message}" name="regmessage" placeholder="{lang register_message}" fwin="login"></li>
				<!--{/if}-->
				<!--{if empty($invite) && $_G['setting']['regstatus'] == 3}-->
					<li class="mli"><input type="text" class="px" autocomplete="off" value="" name="invitecode" placeholder="{lang invite_code}" fwin="login"></li>
				<!--{/if}-->
				<!--{loop $_G['cache']['fields_register'] $field}-->
					<!--{if $htmls[$field['fieldid']]}-->
						<!--{if $field['fieldid'] == 'gender'}-->
						<li class="flex-box mli"><div class="flex xg1">{$field['title']}</div><div class="flex-3">$htmls[$field['fieldid']]</div></li>
						<!--{elseif $field['fieldid'] == 'birthday'}-->
						<li class="flex-box mli"><div class="flex xg1">{$field['title']}</div><div class="flex-3 multisel">$htmls[$field['fieldid']]</div></li>
						<!--{else}-->
						<li class="mli">$htmls[$field['fieldid']]</li>
						<!--{/if}-->
					<!--{/if}-->
				<!--{/loop}-->
			<!--{/if}-->
			<!--{hook/register_input_mobile}-->
		</ul>
		<!--{if $secqaacheck || $seccodecheck}-->
			<!--{subtemplate common/seccheck}-->
		<!--{/if}-->
		<!--{if $bbrules}-->
			<label for="agreebbrule"><li class="mli"><input type="checkbox" class="pc" name="agreebbrule" value="{$bbrulehash}" id="agreebbrule" checked="checked" />{lang agree}<a href="javascript:;" onclick="showBBRule()">{lang rulemessage}</a></li></label>
			<div id="layer_bbruletxt" popup="true" class="tip login_pop" style="display:none;">
				<div style="height:200px;display:block;overflow-y:scroll;">
					<div class="log_tit"><!--{echo addslashes($this->setting['bbname']);}--> {lang rulemessage}</div>
					<div class="p15">{$bbrulestxt}</div>
				</div>
			</div>
		<!--{/if}-->
	</div>
	<div class="btn_register"><button value="true" name="regsubmit" type="submit" class="formdialog pn">{lang quickregister}</button></div>
	<div class="reg_link"><a href="member.php?mod=logging&action=login&referer={echo rawurlencode($dreferer)}" class="login_now">{lang login_now}</a></div>
	</form>
</div>
<div id="mask" style="display:none;"></div>
<!--{hook/register_bottom_mobile}-->
<script type="text/javascript">
<!--{if $sendurl}-->
	function succeedhandle_registerform(url, message, extra) {
		popup.open(message, 'confirm', url)
	}
<!--{/if}-->
<!--{if $bbrules && $bbrulesforce}-->
	showBBRule();
<!--{/if}-->
<!--{if $this->showregisterform}-->
	function showBBRule() {
		var bbruletxt = getID("layer_bbruletxt").innerHTML;
		popup.open(bbruletxt, 'alert');
	}
<!--{/if}-->
</script>
<!--{eval updatesession();}-->
<!--{template common/footer}-->