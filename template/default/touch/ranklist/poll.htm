<!--{template common/header}-->

<div class="header cl">
	<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div>
	<h2>{lang ranklist_poll}</h2>
	<div class="my"><a href="index.php"><i class="dm-house"></i></a></div>
</div>

<!--{subtemplate ranklist/side_top}-->

<div class="dhnv flex-box cl" style="border-top: 0;">
	<a href="misc.php?mod=ranklist&type=poll&view=heats&orderby=$orderby" class="flex{if $_GET[view] == 'heats'} mon{/if}">{lang ranklist_hot}</a>
	<a href="misc.php?mod=ranklist&type=poll&view=favtimes&orderby=$orderby" class="flex{if $_GET[view] == 'favtimes'} mon{/if}">{lang ranklist_favorite}</a>
	<a href="misc.php?mod=ranklist&type=poll&view=sharetimes&orderby=$orderby" class="flex{if $_GET[view] == 'sharetimes'} mon{/if}">{lang ranklist_share}</a>
</div>

<div id="ct" class="bodybox p10 cl" style="padding-top: 0 !important;">
	<p id="before" class="tbmu">
		<a href="misc.php?mod=ranklist&type=poll&view=$_GET[view]&orderby=thisweek" id="604800" {if $orderby == 'thisweek'}class="a"{/if} />{lang ranklist_week}</a><span class="pipe">|</span>
		<a href="misc.php?mod=ranklist&type=poll&view=$_GET[view]&orderby=thismonth" id="2592000" {if $orderby == 'thismonth'}class="a"{/if} />{lang ranklist_month}</a><span class="pipe">|</span>
		<a href="misc.php?mod=ranklist&type=poll&view=$_GET[view]&orderby=today" id="86400" {if $orderby == 'today'}class="a"{/if} />{lang ranklist_today}</a><span class="pipe">|</span>
		<a href="misc.php?mod=ranklist&type=poll&view=$_GET[view]&orderby=all" id="all" {if $orderby == 'all'}class="a"{/if} />{lang all}</a>
	</p>
	<!--{if $polllist}-->
		<ul class="el pll">
		<!--{loop $polllist $poll}-->
			<li>
				<div class="t"><span class="ranks{if $poll['rank'] <= 3} ranks_$poll['rank']{/if}">$poll['rank']</span></div>
				<div class="cl">
					<div class="u z">
						<a href="home.php?mod=space&uid=$poll['authorid']" class="avt" target="_blank"><!--{avatar($poll['authorid'], 'middle')}--></a>
						<p class="mt10"><a href="home.php?mod=space&uid=$poll['authorid']" target="_blank">$poll['author']</a></p>
					</div>
					<div class="s y">
						<a href="forum.php?mod=viewthread&tid=$poll['tid']" class="joins" target="_blank">
							<span>$poll['voters']</span>{lang people_join}
						</a>
						<a href="forum.php?mod=viewthread&tid=$poll['tid']" class="go" target="_blank">{lang to_poll}</a>
					</div>
					<div class="c">
						<h4 class="h"><a href="forum.php?mod=viewthread&tid=$poll['tid']" target="_blank">$poll['subject']</a></h4>
						<ol>
							<!--{loop $poll['pollpreview'] $item}-->
							<li>$item</li>
							<!--{/loop}-->
							<li style="list-style-type: none;">...</li>
						</ol>
						<div class="mt10 xg1">
							<!--{if $_GET[view] == 'favtimes'}-->{lang ranklist_thread_favorite} $poll['favtimes']
							<!--{elseif $_GET[view] == 'sharetimes'}-->{lang ranklist_thread_share} $poll['sharetimes']
							<!--{else}-->{lang hot} $poll['heats']<!--{/if}-->
							<span class="pipe">|</span>
							$poll['dateline']
						</div>
					</div>
				</div>
			</li>
		<!--{/loop}-->
		</ul>
		<div class="pgs cl mt10">$multi</div>
	<!--{else}-->
		<div class="emp">{lang none_data}</div>
	<!--{/if}-->
	<div class="notice">{lang ranklist_update}</div>
</div>


<!--{template common/footer}-->