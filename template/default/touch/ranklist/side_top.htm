<div class="dhnav_box">
	<div id="dhnav">
		<div id="dhnav_li">
			<ul class="swiper-wrapper">
				<li class="swiper-slide{if $_GET['type'] == 'index' || !$_GET['type']} mon{/if}"><a href="misc.php?mod=ranklist">{lang all}</a></li>
				<!--{if $ranklist_setting['member']['available']}-->
				<li class="swiper-slide{if $_GET['type'] == 'member'} mon{/if}"><a href="misc.php?mod=ranklist&type=member">{lang user}</a></li>
				<!--{/if}-->
				<!--{if helper_access::check_module('forum') && $ranklist_setting['thread']['available']}-->
				<li class="swiper-slide{if $_GET['type'] == 'thread'} mon{/if}"><a href="misc.php?mod=ranklist&type=thread&view=replies&orderby=thisweek">{lang posts}</a></li>
				<!--{/if}-->
				<!--{if helper_access::check_module('blog') && $ranklist_setting['blog']['available']}-->
				<li class="swiper-slide{if $_GET['type'] == 'blog'} mon{/if}"><a href="misc.php?mod=ranklist&type=blog&view=heats&orderby=thisweek">{lang blogs}</a></li>
				<!--{/if}-->
				<!--{if helper_access::check_module('forum') && $ranklist_setting['poll']['available']}-->
				<li class="swiper-slide{if $_GET['type'] == 'poll'} mon{/if}"><a href="misc.php?mod=ranklist&type=poll&view=heats&orderby=thisweek">{lang poll}</a></li>
				<!--{/if}-->
				<!--{if helper_access::check_module('forum') && $ranklist_setting['activity']['available']}-->
				<li class="swiper-slide{if $_GET['type'] == 'activity'} mon{/if}"><a href="misc.php?mod=ranklist&type=activity&view=heats&orderby=thismonth">{lang activity}</a></li>
				<!--{/if}-->
				<!--{if helper_access::check_module('album') && $ranklist_setting['picture']['available']}-->
				<li class="swiper-slide{if $_GET['type'] == 'picture'} mon{/if}"><a href="misc.php?mod=ranklist&type=picture&view=hot&orderby=thismonth">{lang pics}</a></li>
				<!--{/if}-->
				<!--{if helper_access::check_module('forum') && $ranklist_setting['forum']['available']}-->
				<li class="swiper-slide{if $_GET['type'] == 'forum'} mon{/if}"><a href="misc.php?mod=ranklist&type=forum&view=threads">{lang forum}</a></li>
				<!--{/if}-->
				<!--{if helper_access::check_module('group') && $ranklist_setting['group']['available']}-->
				<li class="swiper-slide{if $_GET['type'] == 'group'} mon{/if}"><a href="misc.php?mod=ranklist&type=group&view=credit">{lang group}</a></li>
				<!--{/if}-->
			</ul>
			<!--{hook/ranklist_nav_extra}-->
		</div>
	</div>
</div>

<script>if($("#dhnav_li .mon").length>0){var discuz_nav=$("#dhnav_li .mon").offset().left+$("#dhnav_li .mon").width()>=$(window).width()?$("#dhnav_li .mon").index():0}else{var discuz_nav=0}new Swiper('#dhnav_li',{freeMode:true,slidesPerView:'auto',initialSlide:discuz_nav,onTouchMove:function(swiper){Discuz_Touch_on=0},onTouchEnd:function(swiper){Discuz_Touch_on=1},});</script>
