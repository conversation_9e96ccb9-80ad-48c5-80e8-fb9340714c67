<!--{template common/header}-->

<div class="header cl">
	<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div>
	<h2>{lang ranklist_thread}</h2>
	<div class="my"><a href="index.php"><i class="dm-house"></i></a></div>
</div>

<!--{subtemplate ranklist/side_top}-->

<div class="dhnv flex-box cl" style="border-top: 0;">
	<a href="misc.php?mod=ranklist&type=thread&view=replies&orderby=$orderby" class="flex{if $_GET[view] == 'replies'} mon{/if}">{lang ranklist_reply}</a>
	<a href="misc.php?mod=ranklist&type=thread&view=views&orderby=$orderby" class="flex{if $_GET[view] == 'views'} mon{/if}">{lang visit_ranklist}</a>
	<a href="misc.php?mod=ranklist&type=thread&view=sharetimes&orderby=$orderby" class="flex{if $_GET[view] == 'sharetimes'} mon{/if}">{lang ranklist_share}</a>
	<a href="misc.php?mod=ranklist&type=thread&view=favtimes&orderby=$orderby" class="flex{if $_GET[view] == 'favtimes'} mon{/if}">{lang ranklist_favorite}</a>
	<a href="misc.php?mod=ranklist&type=thread&view=heats&orderby=$orderby" class="flex{if $_GET[view] == 'heats'} mon{/if}">{lang ranklist_hot}</a>
</div>

<div id="ct" class="bodybox p10 cl" style="padding-top: 0 !important;">
	<p id="before" class="tbmu{if $threadlist} bw0{/if}">
		<a href="misc.php?mod=ranklist&type=thread&view=$_GET[view]&orderby=thisweek" id="604800" {if $orderby == 'thisweek'}class="a"{/if} />{lang ranklist_week}</a><span class="pipe">|</span>
		<a href="misc.php?mod=ranklist&type=thread&view=$_GET[view]&orderby=thismonth" id="2592000" {if $orderby == 'thismonth'}class="a"{/if} />{lang ranklist_month}</a><span class="pipe">|</span>
		<a href="misc.php?mod=ranklist&type=thread&view=$_GET[view]&orderby=today" id="86400" {if $orderby == 'today'}class="a"{/if} />{lang ranklist_today}</a><span class="pipe">|</span>
		<a href="misc.php?mod=ranklist&type=thread&view=$_GET[view]&orderby=all" id="all" {if $orderby == 'all'}class="a"{/if} />{lang all}</a>
	</p>
	<!--{if $threadlist}-->
		<div class="tl">
			<table cellspacing="0" cellpadding="0">
				<tbody>
					<tr class="th">
						<td class="icn">&nbsp;</td>
						<th>{lang thread}</th>
						<td class="by">{lang author}</td>
					</tr>
				</tbody>
				<!--{loop $threadlist $thread}-->
					<tr>
						<td class="icn"><span class="ranks{if $thread['rank'] <= 3} ranks_$thread['rank']{/if}">$thread['rank']</span></td>
						<th><a href="forum.php?mod=viewthread&tid={$thread['tid']}" target="_blank">$thread['subject']</a></th>
						<td class="by">
							<cite><a href="home.php?mod=space&uid={$thread['authorid']}" target="_blank">$thread['author']</a></cite>
							<em>$thread['dateline']</em>
						</td>
					</tr>
				<!--{/loop}-->
			</table>
		</div>
	<!--{else}-->
		<div class="emp">{lang none_data}</div>
	<!--{/if}-->
	<div class="notice">{lang ranklist_update}</div>
</div>


<!--{template common/footer}-->