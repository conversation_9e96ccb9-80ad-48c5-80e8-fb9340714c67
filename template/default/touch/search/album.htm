<!--{template common/header}-->
<div class="header cl">
	<div class="mz"><a href="javascript:history.back();"><i class="dm-c-left"></i></a></div>
	<h2>{lang album}{lang search}</h2>
	<div class="my"><a href="home.php?mod=space&do=album"><i class="dm-house"></i></a></div>
</div>
<form class="searchform" method="post" autocomplete="off" action="search.php?mod=album">
	<input type="hidden" name="formhash" value="{FORMHASH}" />
	<!--{subtemplate search/pubsearch}-->
	<!--{hook/album_top}-->
	<!--{eval $policymsgs = $p = '';}-->
	<!--{loop $_G['setting']['creditspolicy']['search'] $id $policy}-->
	<!--{block policymsg}--><!--{if $_G['setting']['extcredits'][$id]['img']}-->$_G['setting']['extcredits'][$id]['img'] <!--{/if}-->$_G['setting']['extcredits'][$id]['title'] $policy $_G['setting']['extcredits'][$id]['unit']<!--{/block}-->
	<!--{eval $policymsgs .= $p.$policymsg;$p = ', ';}-->
	<!--{/loop}-->
	<!--{if $policymsgs}--><p>{lang search_credit_msg}</p><!--{/if}-->
</form>
<!--{if !empty($searchid) && submitcheck('searchsubmit', 1)}-->
	<!--{subtemplate search/album_list}-->
<!--{/if}-->
<!--{hook/album_bottom}-->
<!--{template common/footer}-->