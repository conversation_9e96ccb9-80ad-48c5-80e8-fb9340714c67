<div class="threadlist_box">
	<h2><!--{if $keyword && empty($searchstring[2])}-->{lang search_group_result_keyword}  <!--{if empty($viewgroup) && !empty($grouplist)}--><a href="search.php?mod=group&searchid=$searchid&orderby=$orderby&ascdesc=$ascdesc&searchsubmit=yes&viewgroup=1" target="_blank">{lang search_group_viewgroup}</a><!--{/if}--><!--{elseif $viewgroup}-->{lang search_group_result}<!--{else}-->{lang search_result}<!--{/if}--></h2>
	<!--{ad/search/y mtw}-->
	<!--{if $viewgroup}-->
	<!--{if empty($grouplist)}-->
	<h4>{lang search_nomatch}</h4>
	<!--{else}-->
	<div class="threadlist cl">
		<ul>
			<!--{loop $grouplist $group}-->
			<li class="list">
				<div class="threadlist_top cl">
					<a href="forum.php?mod=group&fid={$group['fid']}" class="mimg"><img src="{$group['icon']}"></a>
					<div class="muser">
						<h3><a href="forum.php?mod=group&fid={$group['fid']}" class="mmc">{$group['name']}<!--{if $group['gviewperm'] == 1}--> ({lang public})<!--{/if}--></a></h3>
						<span class="mtime">{$group['dateline']}</span>
					</div>
				</div>
				<div class="threadlist_foot cl">
					<ul>
						<li><i class="dm-heart-fill"></i>{$group['threads']}</li>
						<li><i class="dm-eye-fill"></i>{$group['membernum']}</li>
						<li><i class="dm-chat-s-fill"></i>{$group['commoncredits']}</li>
					</ul>
				</div>
			</li>
			<!--{/loop}-->
		</ul>
	</div>
	<!--{/if}-->
	<!--{if !empty($multipage)}--><div class="pgs cl mbm">$multipage</div><!--{/if}-->
	<!--{else}-->
	<!--{if !empty($grouplist) && $page < 2}-->
	<div class="threadlist cl">
		<ul>
			<!--{loop $grouplist $group}-->
			<li class="list">
				<div class="threadlist_top cl">
					<a href="forum.php?mod=group&fid={$group['fid']}" class="mimg"><img src="{$group['icon']}"></a>
					<div class="muser">
						<h3><a href="forum.php?mod=group&fid={$group['fid']}" class="mmc">{$group['name']}<!--{if $group['gviewperm'] == 1}--> ({lang public})<!--{/if}--></a></h3>
						<span class="mtime">{$group['dateline']}</span>
					</div>
				</div>
				<div class="threadlist_foot cl">
					<ul>
						<li><i class="dm-heart-fill"></i>{$group['threads']}</li>
						<li><i class="dm-eye-fill"></i>{$group['membernum']}</li>
						<li><i class="dm-chat-s-fill"></i>{$group['commoncredits']}</li>
					</ul>
				</div>
			</li>
			<!--{/loop}-->
		</ul>
	</div>
	<!--{/if}-->
	<!--{if empty($threadlist) && empty($grouplist)}-->
	<h4>{lang search_nomatch}</h4>
	<!--{else}-->
	<div class="threadlist cl">
		<ul>
			<!--{loop $threadlist $thread}-->
			<li class="list">
				<div class="threadlist_top cl">
					<a href="home.php?mod=space&uid={$thread['authorid']}" class="mimg"><img src="<!--{avatar($thread['authorid'], 'middle', true)}-->"></a>
					<div class="muser">
						<h3><a href="home.php?mod=space&uid={$thread['authorid']}" class="mmc">{$thread['author']}</a></h3>
						<span class="mtime">{$thread['dateline']}</span>
					</div>
				</div>
				<a href="forum.php?mod=viewthread&tid=$thread['tid']&extra=$extra&highlight=$index['keywords']">
				<div class="threadlist_tit cl">
					<!--{if $thread['folder'] == 'lock'}-->
						<span class="micon lock">{lang closed_thread}</span>
					<!--{elseif $thread['special'] == 1}-->
						<span class="micon">{lang thread_poll}</span>
					<!--{elseif $thread['special'] == 2}-->
						<span class="micon">{lang thread_trade}</span>
					<!--{elseif $thread['special'] == 3}-->
						<span class="micon">{lang thread_reward}</span>
					<!--{elseif $thread['special'] == 4}-->
						<span class="micon">{lang thread_activity}</span>
					<!--{elseif $thread['special'] == 5}-->
						<span class="micon">{lang thread_debate}</span>
					<!--{/if}-->
					<!--{if $thread['attachment'] == 2 && $_G['setting']['mobile']['mobilesimpletype'] == 1}-->
						<span class="micon">{lang mobtu}</span>
					<!--{/if}-->
					<!--{if in_array($thread['displayorder'], array(1, 2, 3, 4))}-->
						<span class="micon top">{lang thread_sticky}</span>
					<!--{/if}-->
					<!--{if $thread['digest'] > 0}-->
						<span class="micon digest">{lang thread_digest}</span>
					<!--{/if}-->
					<em $thread['highlight']>{$thread['subject']}</em>					
				</div>
				</a>
				<a href="forum.php?mod=viewthread&tid=$thread['tid']&extra=$extra&highlight=$index['keywords']"><div class="threadlist_mes cl">{$thread['message']}</div></a>
				<div class="threadlist_foot cl">
					<ul>
						<li class="mr"><a href="forum.php?mod=forumdisplay&fid={$thread['fid']}">#{$thread['forumname']}</a></li>
						<li><i class="dm-eye-fill"></i>{$thread['views']}</li>
						<li><i class="dm-chat-s-fill"></i>{$thread['replies']}</li>
					</ul>
				</div>
			</li>
			<!--{/loop}-->
		</ul>
	</div>
	<!--{/if}-->
	<!--{if !empty($multipage)}--><div class="pgs cl mbm">$multipage</div><!--{/if}-->
</div>
<!--{/if}-->