<?php 
define('UC_DBHOST', '127.0.0.1');
define('UC_DBUSER', 'root');
define('UC_DBPW', 'root');
define('UC_DBNAME', 'dx35utf8new');
define('UC_DBCHARSET', 'utf8mb4');
define('UC_DBTABLEPRE', 'pre_ucenter_');
define('UC_COOKIEPATH', '/');
define('UC_COOKIEDOMAIN', '');
define('UC_DBCONNECT', 0);
define('UC_CHARSET', 'utf-8');
define('UC_FOUNDERPW', '$2y$10$KDiYjeMg/MOb38pjIi2HF.Y392JYGhZXYMSnHgnoKSVyZQlwn8MGO');
define('UC_FOUNDERSALT', '');
define('UC_KEY', 'iel0faq8x6v0QfP29bPdRec7b382rfB2GcwaS9b989jfNfD3z1X4X4l7H4Hcdbua');
define('UC_SITEID', 'weP00aW8z6q0ffe20bUdzew7m3e2Zf92EcAaf9X929Zflf73K1Y4v4l724ecBb7a');
define('UC_MYKEY', 'beC0Lak886Z0Yfz29bedxeT733r22fK2WcIa09N9r9cfJf23t1u4L4i724McXb8a');
define('UC_DEBUG', false);
define('UC_PPP', 20);
define('UC_ONLYREMOTEADDR', 1);
define('UC_IPGETTER', 'header');
// define('UC_IPGETTER_HEADER', serialize(array('header' => 'HTTP_X_FORWARDED_FOR')));
