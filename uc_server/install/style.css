/*
(C) 2001-2099 Comsenz Inc.
*/

/* common */
*{ word-wrap:break-word; }
body{ padding:5px 0; background:#FFF; text-align:center; }
body, td, input, textarea, select, button{ color:#666; font:12px <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif; }
ul, dl, dd, p, h1, h2, h3, h4, h5, h6, form, fieldset { margin:0; padding:0; }
h1, h2, h3, h4, h5, h6{ font-size:12px; }
a{ color:#2366A8; text-decoration:none; }
	a:hover { text-decoration:underline; }
	a img{ border:none; }
em, cite, strong, th{ font-style:normal; font-weight:normal; }
table{ border-collapse:collapse; }

/* box */
.container{ overflow:hidden; margin:0 auto; width:700px; height:auto !important;text-align:left; border:1px solid #B5CFD9; }
.header{ height:194px; background:url(images/bg_repx.gif) repeat-x; }
	.header h1{ text-indent:-9999px; width:270px; height:48px; background:url(images/bg_repno.gif) no-repeat 26px 22px; }
	.header span { float: right; padding-right: 10px; }
.main{ padding:20px 20px 0; background:#F7FBFE url(images/bg_repx.gif) repeat-x 0 -194px; }
	.main h3{ margin:10px auto; width:75%; color:#6CA1B4; font-weight:700; }
.desc{ margin:0 auto; width:82.6%; line-height:180%; }
	.desc ul{ margin-left:20px; }
.desc1{ margin:10px 0; width:100%; }
	.desc1 ul{ margin-left:25px; }
	.desc1 li{ margin:3px 0; }
.tb, .tb2{ margin:15px 0 15px 67px; }
	.tb th{ padding:5px; color:#6CA1B4; font-weight:700; }
	.tb td{ padding:3px 5px; }
		.tb .w, .tb .nw, .tb .padleft{ padding-left:45px; }
		.tb .pdleft1{ padding-left:67px; }
.tb2{}
	.tb2 th, .tb2 td{ padding:3px 5px; }
	.tbopt{ width:120px; }
.btnbox{ text-align:center; }
	.btnbox input{ margin:0 2px; }
	.btnbox textarea{ margin-bottom:10px; height:150px; }
.btn{ margin-top:10px; }
.footer{ line-height:40px; text-align:center; background:url(images/bg_footer.gif) repeat-x; font-size:11px; }

/* form */
.txt{ width:200px; }

/* step num */
.setup{ margin:46px 0 0 200px; padding-left:70px; }
	.setup h2{ padding-top:0; *padding-top:6px; color:#6CA1B4; font-size:36px; }
	.setup p{ margin-left:6px; color:#999; }
.step1{ background:url(images/stepnum.gif) no-repeat 0 8px; }
.step2{ background:url(images/stepnum.gif) no-repeat 0 -92px;  }
.step3{ background:url(images/stepnum.gif) no-repeat 0 -192px; }
.step4{ background:url(images/stepnum.gif) no-repeat 0 -292px; }

/* step status */
.stepstat{ position:relative; bottom:-15px; left:80px; *width:593px; height:30px; }
	.stepstatbg{ position:absolute; top:18px; z-index:90; width:535px; height:9px; overflow:hidden; background:#81C6F0 url(images/bg_stepstatus.gif) no-repeat; }
	.stepstat ul{ position:absolute; top:3px; left:-2px; z-index:100; list-style:none; width:600px; white-space:nowrap; overflow:hidden; }
		.stepstat li{ float:left; text-indent:-9999px; width:175px; height:30px; background:url(images/bg_repno.gif) no-repeat 0 -38px; }
		.stepstat .current{ background:url(images/bg_repno.gif) no-repeat 0 -71px; }
		.stepstat .unactivated{ background:url(images/bg_repno.gif) no-repeat 0 -103px; }
		.stepstat .last{ width:20px; }
.stepstat1{ background-position:-750px 0; }
.stepstat2{ background-position:-570px 0; }
.stepstat3{ background-position:-390px 0; }
.stepstat4{ background-position:-210px 0; }

/* file status */
.w{ background:url(images/bg_repno.gif) no-repeat 45px -148px;  }
.nw{ background:url(images/bg_repno.gif) no-repeat 45px -197px; }

/* space */
.marginbot{ margin-bottom:20px; }
.margintop{ margin-top:20px; }
.red{ color:red; }

.licenseblock{ margin-bottom:15px; padding:8px; height:280px; border:1px solid #EEE; background:#FFF; overflow:scroll; overflow-x:hidden; }
.license{}
	.license h1{ padding-bottom:10px; font-size:14px; text-align:center; }
	.license h3{ margin:0; color:#666; }
	.license p{ line-height:150%; margin:10px 0; text-indent:25px; }
	.license li{ line-height:150%; margin:5px 0; }
.title{ margin:5px 0 -15px 60px; }
