{template header}

<script src="js/common.js" type="text/javascript"></script>
<script type="text/javascript">
	function switchbtn(btn) {
		$('srchuserdiv').style.display = btn == 'srch' ? '' : 'none';
		$('srchuserdiv').className = btn == 'srch' ? 'tabcontentcur' : '' ;
		$('srchuserbtn').className = btn == 'srch' ? 'tabcurrent' : '';
		$('adduserdiv').style.display = btn == 'srch' ? 'none' : '';
		$('adduserdiv').className = btn == 'srch' ? '' : 'tabcontentcur';
		$('adduserbtn').className = btn == 'srch' ? '' : 'tabcurrent';
		$('tmenu').style.height = btn == 'srch' ? '80'+'px' : '280'+'px';
	}
</script>
<div class="container">
	<!--{if $status}-->
		<div class="correctmsg"><p>{if $status == 2}{lang badword_list_updated}{elseif $status == 1}{lang badword_add_succeed}{/if}</p></div>
	<!--{/if}-->
	<div id="tmenu" class="hastabmenu">
		<ul class="tabmenu">
			<li id="srchuserbtn" class="tabcurrent"><a href="#" onclick="switchbtn('srch');">{lang badword_add}</a></li>
			<li id="adduserbtn"><a href="#" onclick="switchbtn('add');">{lang badword_multi_add}</a></li>
		</ul>
		<div id="adduserdiv" class="tabcontent" style="display:none;">
			<form action="{UC_ADMINSCRIPT}?m=badword&a=ls" method="post">
				<ul class="tiplist">
					{lang badword_multi_add_comment}
				</ul>
				<textarea name="badwords" class="bigarea"></textarea>
				<ul class="optlist">
					<li><input type="radio" name="type" value="2" id="badwordsopt2" class="radio" checked="checked" /><label for="badwordsopt2">{lang badword_skip}</label></li>
					<li><input type="radio" name="type" value="1" id="badwordsopt1" class="radio" /><label for="badwordsopt1">{lang badword_overwrite}</label></li>
					<li><input type="radio" name="type" value="0" id="badwordsopt0" class="radio" /><label for="badwordsopt0">{lang badword_truncate}</label></li>
				</ul>
				<input type="submit" name="multisubmit" value="{lang submit}" class="btn" />
			</form>

		</div>
		<div id="srchuserdiv" class="tabcontentcur">
			<form action="{UC_ADMINSCRIPT}?m=badword&a=ls" method="post">
			<input type="hidden" name="formhash" value="{FORMHASH}">
			<table>
				<tr>
					<td>{lang badword_keyword}:</td>
					<td><input type="text" name="findnew" class="txt" /></td>
					<td>{lang badword_replace}:</td>
					<td><input type="text" name="replacementnew" class="txt" /></td>
					<td><input type="submit" value="{lang submit}"  class="btn" /></td>
				</tr>
			</table>
			</form>
		</div>
	</div>
	<br />
	<h3>{lang badword_list}</h3>
	<div class="mainbox">
		<!--{if $badwordlist}-->
			<form action="{UC_ADMINSCRIPT}?m=badword&a=ls" method="post">
				<table class="datalist fixwidth">
					<tr>
						<th><input type="checkbox" name="chkall" id="chkall" onclick="checkall('delete[]')" class="checkbox" /><label for="chkall">{lang badword_delete}</label></th>
						<th style="text-align:right;padding-right:11px;">{lang badword_keyword}</th>
						<th></th>
						<th>{lang badword_replace}</th>
						<th>{lang badword_admin}</th>
					</tr>
					<!--{loop $badwordlist $badword}-->
						<tr>
							<td class="option"><input type="checkbox" name="delete[]" value="$badword[id]" class="checkbox" /></td>
							<td class="tdinput"><input type="text" name="find[{$badword[id]}]" value="$badword[find]" title="{lang shortcut_tips}" class="txtnobd" onblur="this.className='txtnobd'" onfocus="this.className='txt'" /></td>
							<td class="tdarrow">&gt;</td>
							<td class="tdinput"><input type="text" name="replacement[{$badword[id]}]" value="$badword[replacement]" title="{lang shortcut_tips}" class="txtnobd"  onblur="this.className='txtnobd'" onfocus="this.className='txt'" style="text-align:left;" /></td>
							<td>$badword[admin]</td>
						</tr>
					<!--{/loop}-->
					<tr class="nobg">
						<td><input type="submit" value="{lang submit}" class="btn" /></td>
						<td class="tdpage" colspan="4">$multipage</td>
					</tr>
				</table>
			</form>
		<!--{else}-->
			<div class="note">
				<p class="i">{lang list_empty}</p>
			</div>
		<!--{/if}-->
	</div>
</div>

{template footer}