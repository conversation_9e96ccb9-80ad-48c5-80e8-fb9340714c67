{template header}
<script type="text/javascript">
function $(id) {
	return document.getElementById(id);
}
</script>

<div class="container">
	<form action="{UC_ADMINSCRIPT}?m=user&a=login" method="post" id="loginform" {if $iframe}target="_self"{else}target="_top"{/if}>
		<input type="hidden" name="formhash" value="{FORMHASH}" />
		<input type="hidden" name="seccodehidden" value="$seccodeinit" />
		<input type="hidden" name="iframe" value="{$iframe}" />
		<table class="mainbox">
			<tr>
				<td class="loginbox">
					<h1>UCenter</h1>
					<p>{lang login_tips}</p>
				</td>
				<td class="login">
					<!--{if $errorcode == UC_LOGIN_ERROR_FOUNDER_PW}--><div class="errormsg loginmsg"><p>{lang login_founder_incorrect}</p></div>
					<!--{elseif $errorcode == UC_LOGIN_ERROR_ADMIN_PW}--><div class="errormsg loginmsg"><p>{lang login_incorrect}</p></div>
					<!--{elseif $errorcode == UC_LOGIN_ERROR_ADMIN_NOT_EXISTS}--><div class="errormsg loginmsg"><p>{lang login_admin_noexists}</p></div>
					<!--{elseif $errorcode == UC_LOGIN_ERROR_SECCODE}--><div class="errormsg loginmsg"><p>{lang login_seccode_error}</p></div>
					<!--{elseif $errorcode == UC_LOGIN_ERROR_FAILEDLOGIN}--><div class="errormsg loginmsg"><p>{lang login_failedlogin}</p></div>
					<!--{/if}-->
					<p>
						<input type="radio" name="isfounder" value="1" class="radio" {if (isset($_POST['isfounder']) && $isfounder) || !isset($_POST['isfounder'])}checked="checked"{/if} onclick="changeuser('founder')" id="founder" /><label for="founder">{lang founder}</label>
						<input type="radio" name="isfounder" value="0" class="radio" {if (isset($_POST['isfounder']) && !$isfounder)}checked="checked"{/if} onclick="changeuser('manager')" id="admin" /><label for="admin">{lang admin_admin}</label>
					</p>
					<p id="usernamediv">{lang login_username}: <input type="text" name="username" class="txt" id="username" value="$username" /></p>
					<p>{lang login_password}: <input type="password" name="password" class="txt" id="password" value="$password" /></p>
					<p>{lang login_seccode}: <input type="text" name="seccode" class="txt seccode" id="seccode" value="" /><img width="70" height="21" src="{UC_ADMINSCRIPT}?m=seccode&seccodeauth=$seccodeinit&{eval echo rand();}" class="checkcode" /></p>
					<p class="loginbtn"><input type="submit" name="submit" value="{lang login_submit}" class="btn" /></p>
				</td>
			</tr>
		</table>
	</form>
</div>
<script type="text/javascript">
{if (isset($_POST['isfounder']) && $isfounder) || !isset($_POST['isfounder'])}
	$('username').value='UCenter Administrator';
	$('username').disabled = true;
	$('username').readOnly = true;
	$('password').focus();
{else}
	$('username').disabled = false;
	$('username').readOnly = false;
	$('username').focus();
{/if}

function changeuser(user) {
	if(user == 'founder') {
		$('username').value='UCenter Administrator';
		$('username').readOnly = true;
		$('username').disabled = true;
		$('password').focus();
	} else if(user == 'manager') {
		$('username').value='';
		$('username').readOnly = false;
		$('username').disabled = false;
		$('username').focus();
	}
}
</script>
<div class="footer">Powered by UCenter {UC_SERVER_VERSION} {lang copyright}</div>
{template footer}