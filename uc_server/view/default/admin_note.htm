{template header}

<script src="js/common.js" type="text/javascript"></script>
<div class="container">
	<h3 class="marginbot">
		<a href="{UC_ADMINSCRIPT}?m=feed&a=ls" class="sgbtn">{lang feed_list}</a>
		{lang note_list}
		<!--{if $user['isfounder'] || $user['allowadminlog']}--><a href="{UC_ADMINSCRIPT}?m=log&a=ls" class="sgbtn">{lang menu_log}</a><!--{/if}-->
		<a href="{UC_ADMINSCRIPT}?m=mail&a=ls" class="sgbtn">{lang mail_queue}</a>
	</h3>
	<!--{if $status == 2}-->
		<div class="correctmsg"><p>{lang note_list_updated}</p></div>
	<!--{/if}-->
	<div class="mainbox">
		<!--{if $notelist}-->
			<form action="{UC_ADMINSCRIPT}?m=note&a=ls" method="post">
			<input type="hidden" name="formhash" value="{FORMHASH}">
			<table class="datalist" onmouseover="addMouseEvent(this);" style="table-layout:fixed">
				<tr>
					<th width="60"><input type="checkbox" name="chkall" id="chkall" onclick="checkall('delete[]')" class="checkbox" /><label for="chkall">{lang note_delete}</label></th>
					<th width="130">{lang note_operation}</th>
					<th width="60">{lang note_times}</th>
					<th width="50">{lang note_param}</th>
					<th width="140">{lang note_last_note_time}</th>
					<!--{loop $applist $app}-->
						<!--{if $app['recvnote']}-->
							<th width="100">$app[name]</th>
						<!--{/if}-->
					<!--{/loop}-->
				</tr>
				<!--{loop $notelist $note}-->
					{eval $debuginfo = dhtmlspecialchars(str_replace(array("\n", "\r", "'"), array('', '', "\'"), $note['getdata'].$note['postdata2'])); }
					<tr>
						<td><input type="checkbox" name="delete[]" value="$note[noteid]" class="checkbox" /></td>
						<td><strong>$note[operation]</strong></td>
						<td>$note[totalnum]</td>
						<td><a href="###" onclick="alert('$debuginfo');">{lang note_view}</a></td>
						<td>$note[dateline]</td>
						<!--{loop $applist $appid $app}-->
							<!--{if $app['recvnote']}-->
								<td>$note[status][$appid]</td>
							<!--{/if}-->
						<!--{/loop}-->
					</tr>
				<!--{/loop}-->
				<tr class="nobg">
					<td><input type="submit" value="{lang submit}" class="btn" /></td>
					<td class="tdpage" colspan="{eval echo count($applist) + 4;}">$multipage</td>
				</tr>
			</table>
			</form>
		<!--{else}-->
			<div class="note">
				<p class="i">{lang list_empty}</p>
			</div>
		<!--{/if}-->
	</div>
</div>

{template footer}