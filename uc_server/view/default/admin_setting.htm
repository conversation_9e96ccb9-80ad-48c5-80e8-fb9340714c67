{template header}

<script src="js/common.js" type="text/javascript"></script>

<div class="container">
	<!--{if $updated}-->
		<div class="correctmsg"><p>{lang update_succeed}</p></div>
	<!--{elseif $a == 'register'}-->
		<div class="note fixwidthdec"><p class="i">{lang setting_register_tips}</p></div>
	<!--{/if}-->
	<!--{if $a == 'ls'}-->
		<div class="mainbox nomargin">
			<form action="{UC_ADMINSCRIPT}?m=setting&a=ls" method="post">
				<input type="hidden" name="formhash" value="{FORMHASH}">
				<table class="opt">
					<tr>
						<th colspan="2">{lang setting_dateformat}:</th>
					</tr>
					<tr>
						<td><input type="text" class="txt" name="dateformat" value="$dateformat" /></td>
						<td>{lang setting_dateformat_comment}</td>
					</tr>
					<tr>
						<th colspan="2">{lang setting_timeformat}:</th>
					</tr>
					<td>
						<input type="radio" id="hr24" class="radio" name="timeformat" value="1" $timeformat[1] /><label for="hr24">{lang setting_timeformat_hr24}</label>
						<input type="radio" id="hr12" class="radio" name="timeformat" value="0" $timeformat[0] /><label for="hr12">{lang setting_timeformat_hr12}</label>
					</td>
					</tr>
					<tr>
						<th colspan="2">{lang setting_timeoffset}:</th>
					</tr>
					<tr>
						<td>
							<select name="timeoffset">
								<option value="-12" $checkarray['012']>(GMT -12:00) Eniwetok, Kwajalein</option>
								<option value="-11" $checkarray['011']>(GMT -11:00) Midway Island, Samoa</option>
								<option value="-10" $checkarray['010']>(GMT -10:00) Hawaii</option>
								<option value="-9" $checkarray['09']>(GMT -09:00) Alaska</option>
								<option value="-8" $checkarray['08']>(GMT -08:00) Pacific Time (US &amp; Canada), Tijuana</option>
								<option value="-7" $checkarray['07']>(GMT -07:00) Mountain Time (US &amp; Canada), Arizona</option>
								<option value="-6" $checkarray['06']>(GMT -06:00) Central Time (US &amp; Canada), Mexico City</option>
								<option value="-5" $checkarray['05']>(GMT -05:00) Eastern Time (US &amp; Canada), Bogota, Lima, Quito</option>
								<option value="-4" $checkarray['04']>(GMT -04:00) Atlantic Time (Canada), Caracas, La Paz</option>
								<option value="-3.5" $checkarray['03.5']>(GMT -03:30) Newfoundland</option>
								<option value="-3" $checkarray['03']>(GMT -03:00) Brassila, Buenos Aires, Georgetown, Falkland Is</option>
								<option value="-2" $checkarray['02']>(GMT -02:00) Mid-Atlantic, Ascension Is., St. Helena</option>
								<option value="-1" $checkarray['01']>(GMT -01:00) Azores, Cape Verde Islands</option>
								<option value="0" $checkarray['0']>(GMT) Casablanca, Dublin, Edinburgh, London, Lisbon, Monrovia</option>
								<option value="1" $checkarray['1']>(GMT +01:00) Amsterdam, Berlin, Brussels, Madrid, Paris, Rome</option>
								<option value="2" $checkarray['2']>(GMT +02:00) Cairo, Helsinki, Kaliningrad, South Africa</option>
								<option value="3" $checkarray['3']>(GMT +03:00) Baghdad, Riyadh, Moscow, Nairobi</option>
								<option value="3.5" $checkarray['3.5']>(GMT +03:30) Tehran</option>
								<option value="4" $checkarray['4']>(GMT +04:00) Abu Dhabi, Baku, Muscat, Tbilisi</option>
								<option value="4.5" $checkarray['4.5']>(GMT +04:30) Kabul</option>
								<option value="5" $checkarray['5']>(GMT +05:00) Ekaterinburg, Islamabad, Karachi, Tashkent</option>
								<option value="5.5" $checkarray['5.5']>(GMT +05:30) Bombay, Calcutta, Madras, New Delhi</option>
								<option value="5.75" $checkarray['5.75']>(GMT +05:45) Katmandu</option>
								<option value="6" $checkarray['6']>(GMT +06:00) Almaty, Colombo, Dhaka, Novosibirsk</option>
								<option value="6.5" $checkarray['6.5']>(GMT +06:30) Rangoon</option>
								<option value="7" $checkarray['7']>(GMT +07:00) Bangkok, Hanoi, Jakarta</option>
								<option value="8" $checkarray['8']>(GMT +08:00) &#x5317;&#x4eac;(Beijing), Hong Kong, Perth, Singapore, Taipei</option>
								<option value="9" $checkarray['9']>(GMT +09:00) Osaka, Sapporo, Seoul, Tokyo, Yakutsk</option>
								<option value="9.5" $checkarray['9.5']>(GMT +09:30) Adelaide, Darwin</option>
								<option value="10" $checkarray['10']>(GMT +10:00) Canberra, Guam, Melbourne, Sydney, Vladivostok</option>
								<option value="11" $checkarray['11']>(GMT +11:00) Magadan, New Caledonia, Solomon Islands</option>
								<option value="12" $checkarray['12']>(GMT +12:00) Auckland, Wellington, Fiji, Marshall Island</option>
							</select>
						</td>
						<td>{lang setting_timeoffset_comment}</td>
					</tr>
					<tr>
						<th colspan="2">{lang setting_user_failedtime}:</th>
					</tr>
					<tr>
						<td><input type="text" class="txt" name="login_failedtime" value="$login_failedtime" /></td>
						<td>{lang setting_user_failedtime_comment}</td>
					</tr>
					<tr>
						<th colspan="2">{lang setting_pmsendregdays}:</th>
					</tr>
					<tr>
						<td><input type="text" class="txt" name="pmsendregdays" value="$pmsendregdays" /></td>
						<td>{lang setting_pmsendregdays_comment}</td>
					</tr>
					<tr>
						<th colspan="2">{lang setting_privatepmthreadlimit}:</th>
					</tr>
					<tr>
						<td><input type="text" class="txt" name="privatepmthreadlimit" value="$privatepmthreadlimit" /></td>
						<td>{lang setting_privatepmthreadlimit_comment}</td>
					</tr>
					<tr>
						<th colspan="2">{lang setting_chatpmthreadlimit}:</th>
					</tr>
					<tr>
						<td><input type="text" class="txt" name="chatpmthreadlimit" value="$chatpmthreadlimit" /></td>
						<td>{lang setting_chatpmthreadlimit_comment}</td>
					</tr>
					<tr>
						<th colspan="2">{lang setting_chatpmmemberlimit}:</th>
					</tr>
					<tr>
						<td><input type="text" class="txt" name="chatpmmemberlimit" value="$chatpmmemberlimit" /></td>
						<td>{lang setting_chatpmmemberlimit_comment}</td>
					</tr>
					<tr>
						<th colspan="2">{lang setting_pmfloodctrl}:</th>
					</tr>
					<tr>
						<td><input type="text" class="txt" name="pmfloodctrl" value="$pmfloodctrl" /></td>
						<td>{lang setting_pmfloodctrl_comment}</td>
					</tr>

					<tr>
						<th colspan="2">{lang setting_pmcenter}:</th>
					</tr>
					<tr>
					<td>
						<input type="radio" id="pmcenteryes" class="radio" name="pmcenter" value="1" $pmcenter[1] onclick="$('hidden1').style.display=''"  /><label for="pmcenteryes">{lang yes}</label>
						<input type="radio" id="pmcenterno" class="radio" name="pmcenter" value="0" $pmcenter[0] onclick="$('hidden1').style.display='none'" /><label for="pmcenterno">{lang no}</label>
					</td>
					<td>{lang setting_pmcenter_comment}</td>
					</tr>
					<tbody id="hidden1" $pmcenter[display]>
					<tr>
						<th colspan="2">{lang setting_sendpmseccode}:</th>
					</tr>
					<tr>
						<td>
							<input type="radio" id="sendpmseccodeyes" class="radio" name="sendpmseccode" value="1" $sendpmseccode[1] /><label for="sendpmseccodeyes">{lang yes}</label>
							<input type="radio" id="sendpmseccodeno" class="radio" name="sendpmseccode" value="0" $sendpmseccode[0] /><label for="sendpmseccodeno">{lang no}</label>
						</td>
						<td>{lang setting_sendpmseccode_comment}</td>
					</tr>
					</tbody>
					<tr>
						<th colspan="2">{lang setting_addappbyurl}:</th>
					</tr>
					<tr>
					<td>
						<input type="radio" id="addappbyurlyes" class="radio" name="addappbyurl" value="1" $addappbyurl[1]  /><label for="addappbyurlyes">{lang yes}</label>
						<input type="radio" id="addappbyurlno" class="radio" name="addappbyurl" value="0" $addappbyurl[0] /><label for="addappbyurlno">{lang no}</label>
					</td>
					<td>{lang setting_addappbyurl_comment}</td>
					</tr>
					<tr>
						<th colspan="2">{lang setting_insecureoperation}:</th>
					</tr>
					<tr>
					<td>
						<input type="radio" id="insecureoperationyes" class="radio" name="insecureoperation" value="1" $insecureoperation[1]  /><label for="insecureoperationyes">{lang yes}</label>
						<input type="radio" id="insecureoperationno" class="radio" name="insecureoperation" value="0" $insecureoperation[0] /><label for="insecureoperationno">{lang no}</label>
					</td>
					<td>{lang setting_insecureoperation_comment}</td>
					</tr>
					<tr>
						<th colspan="2">{lang setting_passwordalgo}:</th>
					</tr>
					<tr>
						<td><input type="text" class="txt" name="passwordalgo" value="$passwordalgo" /></td>
						<td>{lang setting_passwordalgo_comment}</td>
					</tr>

					<tr>
						<th colspan="2">{lang setting_passwordoptions}:</th>
					</tr>
					<tr>
						<td><input type="text" class="txt" name="passwordoptions" value="$passwordoptions" /></td>
						<td>{lang setting_passwordoptions_comment}</td>
					</tr>
				</table>
				<div class="opt"><input type="submit" name="submit" value=" {lang submit} " class="btn" /></div>
			</form>
		</div>
	<!--{elseif $a == 'register'}-->
		<div class="mainbox nomargin">
			<form action="{UC_ADMINSCRIPT}?m=setting&a=register" method="post">
				<input type="hidden" name="formhash" value="{FORMHASH}">
				<table class="opt">
					<tr>
						<th colspan="2">{lang setting_register_doublee}:</th>
					</tr>
					<tr>
						<td>
							<input type="radio" id="yes" class="radio" name="doublee" value="1" $doublee[1] /><label for="yes">{lang yes}</label>
							<input type="radio" id="no" class="radio" name="doublee" value="0" $doublee[0] /><label for="no">{lang no}</label>
						</td>
					</tr>
					<tr>
						<th colspan="2">{lang setting_register_accessemail}:</th>
					</tr>
					<tr>
						<td><textarea class="area" name="accessemail">$accessemail</textarea></td>
						<td valign="top">{lang setting_register_accessemail_comment}</td>
					</tr>
					<tr>
						<th colspan="2">{lang setting_register_censoremail}:</th>
					</tr>
					<tr>
						<td><textarea class="area" name="censoremail">$censoremail</textarea></td>
						<td valign="top">{lang setting_register_censoremail_comment}</td>
					</tr>
					<tr>
						<th colspan="2">{lang setting_forbidden_username}:</th>
					</tr>
					<tr>
						<td><textarea class="area" name="censorusername">$censorusername</textarea></td>
						<td valign="top">{lang setting_ceonsor_comment}</td>
					</tr>
				</table>
				<div class="opt"><input type="submit" name="submit" value=" {lang submit} " class="btn" /></div>
			</form>
		</div>
	<!--{else}-->
		<div class="mainbox nomargin">
			<form action="{UC_ADMINSCRIPT}?m=setting&a=mail" method="post">
				<input type="hidden" name="formhash" value="{FORMHASH}">
				<table class="opt">
					<tr>
						<th colspan="2">{lang settings_mail_settings_emailfrom}:</th>
					</tr>
					<tr>
						<td><input name="maildefault" value="{$maildefault}" type="text"></td>
						<td>{lang settings_mail_settings_emailfrom_comment}</td>
					<tr>
						<th colspan="2">{lang settings_mail_settings_send}:</th>
					</tr>
					<tr>
						<td colspan="2">
							<label><input class="radio" name="mailsend" value="1"{if $mailsend == 1} checked="checked"{/if} onclick="$('hidden1').style.display = 'none';$('hidden2').style.display = 'none';" type="radio"> {lang settings_mail_settings_send_1}</label><br />
							<label><input class="radio" name="mailsend" value="2"{if $mailsend == 2} checked="checked"{/if} onclick="$('hidden1').style.display = '';$('hidden2').style.display = '';" type="radio"> {lang settings_mail_settings_send_2}</label><br />
							<label><input class="radio" name="mailsend" value="3"{if $mailsend == 3} checked="checked"{/if} onclick="$('hidden1').style.display = '';$('hidden2').style.display = 'none';" type="radio"> {lang settings_mail_settings_send_3}</label>
						</td>
					</tr>
					<tbody id="hidden1"{if $mailsend == 1} style="display:none"{/if}>
					<tr>
						<td colspan="2">{lang settings_mail_settings_server}:</td>
					</tr>
					<tr>
						<td>
							<input name="mailserver" value="{$mailserver}" class="txt" type="text">
						</td>
						<td valign="top">{lang settings_mail_settings_server_comment}</td>
					</tr>
					<tr>
						<td colspan="2">{lang settings_mail_settings_port}:</td>
					</tr>
					<tr>
						<td>
							<input name="mailport" value="{$mailport}" type="text">
						</td>
						<td valign="top">{lang settings_mail_settings_port_comment}</td>
					</tr>
					<tr>
						<td colspan="2">{lang settings_mail_settings_timeout}:</td>
					</tr>
					<tr>
						<td>
							<input name="mailtimeout" value="{$mailtimeout}" type="text">
						</td>
						<td valign="top">{lang settings_mail_settings_timeout_comment}</td>
					</tr>
					</tbody>
					<tbody id="hidden2"{if $mailsend == 1 || $mailsend == 3} style="display:none"{/if}>
					<tr>
						<td colspan="2">{lang settings_mail_settings_auth}:</td>
					</tr>
					<tr>
						<td>
							<label><input type="radio" class="radio" name="mailauth"{if $mailauth == 1} checked="checked"{/if} value="1" />{lang yes}</label>
							<label><input type="radio" class="radio" name="mailauth"{if $mailauth == 0} checked="checked"{/if} value="0" />{lang no}</label>
						</td>
						<td valign="top">{lang settings_mail_settings_auth_comment}</td>
					</tr>
					<tr>
						<td colspan="2">{lang settings_mail_settings_from}:</td>
					</tr>
					<tr>
						<td>
							<input name="mailfrom" value="{$mailfrom}" class="txt" type="text">
						</td>
						<td valign="top">{lang settings_mail_settings_from_comment}</td>
					</tr>
					<tr>
						<td colspan="2">{lang settings_mail_settings_username}:</td>
					</tr>
					<tr>
						<td>
							<input name="mailauth_username" value="{$mailauth_username}" type="text">
						</td>
						<td valign="top"></td>
					</tr>
					<tr>
						<td colspan="2">{lang settings_mail_settings_password}:</td>
					</tr>
					<tr>
						<td>
							<input name="mailauth_password" value="{$mailauth_password}" type="text">
						</td>
						<td valign="top"></td>
					</tr>
					</tbody>
					<tr>
						<th colspan="2">{lang settings_mail_settings_delimiter}:</th>
					</tr>
					<tr>
						<td>
							<label><input class="radio" name="maildelimiter"{if $maildelimiter == 1} checked="checked"{/if} value="1" type="radio"> {lang settings_mail_settings_delimiter_crlf}</label><br />
							<label><input class="radio" name="maildelimiter"{if $maildelimiter == 0} checked="checked"{/if} value="0" type="radio"> {lang settings_mail_settings_delimiter_lf}</label><br />
							<label><input class="radio" name="maildelimiter"{if $maildelimiter == 2} checked="checked"{/if} value="2" type="radio"> {lang settings_mail_settings_delimiter_cr}</label>
						</td>
						<td>
							{lang settings_mail_settings_delimiter_comment}
						</td>
					</tr>
					<tr>
						<th colspan="2">{lang settings_mail_settings_includeuser}:</th>
					</tr>
					<tr>
						<td>
							<label><input type="radio" class="radio" name="mailusername"{if $mailusername == 1} checked="checked"{/if} value="1" />{lang yes}</label>
							<label><input type="radio" class="radio" name="mailusername"{if $mailusername == 0} checked="checked"{/if} value="0" />{lang no}</label>
						</td>
						<td valign="top">{lang settings_mail_settings_includeuser_comment}</td>
					</tr>
					<tr>
						<th colspan="2">{lang settings_mail_settings_silent}:</th>
					</tr>
					<tr>
						<td>
							<label><input type="radio" class="radio" name="mailsilent"{if $mailsilent == 1} checked="checked"{/if} value="1" />{lang yes}</label>
							<label><input type="radio" class="radio" name="mailsilent"{if $mailsilent == 0} checked="checked"{/if} value="0" />{lang no}</label>
						</td>
						<td valign="top">&nbsp;</td>
					</tr>
				</table>
				<div class="opt"><input type="submit" name="submit" value=" {lang submit} " class="btn" /></div>
			</form>
		</div>
	<!--{/if}-->
</div>

{template footer}