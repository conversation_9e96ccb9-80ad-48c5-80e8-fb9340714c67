<!DOCTYPE html>
<html>
<head>
<meta charset="{UC_CHARSET}" />
<meta name="renderer" content="webkit" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<title>UCenter</title>
<meta name="keywords" content="">
<meta name="description" content="">
<meta name="MSSmartTagsPreventParsing" content="TRUE">
<meta http-equiv="MSThemeCompatible" content="Yes">
<link rel="stylesheet" type="text/css" href="{UC_API}/images/avatar/avatar.css" />
</head>

<body>
<form id="avatarform" enctype="multipart/form-data" method="post" autocomplete="off">
	<div id="avatardesigner">
		<div id="avatarfileselector">
			<input type="file" name="Filedata" id="avatarfile" accept="image/*" />
		</div>
		<div id="avataradjuster">
			<img id="avatarimage" style="visibility: hidden;" onload="forceSelectorInsideAvatar();" />
			<canvas id="avatarcanvas" style="position: absolute; top: 0px; left: 0px;"></canvas>
			<div id="widgetparent" style="position: absolute; left: 0px; top: 0px;">
				<div id="selector" class="ui-widget-content" style="position: absolute; width: 120px; height: 120px; overflow:hidden; cursor: move; border: 1px solid lightgrey; background-color: transparent; background-image: none;">
				</div>
			</div>
			<div class="backfileselectiondiv">
				<input type="button" name="backfileselection" class="backfileselection" value="Select File" onclick="showAvatarFileSelector();" />
			</div>
			<div id="slider" style="height: 0px; position: absolute; right: 9px; top: 105px; width: 100px;"></div>
			<div class="saveAvatardiv">
				<input type="submit" name="confirm" value="{lang confirms}" class="saveAvatar" onclick="saveAvatar();" />
			</div>
			<input type="hidden" id="avatar1" name="avatar1" />
			<input type="hidden" id="avatar2" name="avatar2" />
			<input type="hidden" id="avatar3" name="avatar3" />
		</div>
		<div id="avatardisplayer">
			<canvas id="avatardisplaycanvas"></canvas>
			<div class="finishbuttondiv">
				<input type="button" value="{lang finished}" class="finishbutton" onclick="parent.window.location.reload();" />
			</div>
		</div>
	</div>
	<script src="js/avatar/jquery.min.js"></script>
	<script src="js/avatar/jquery-ui.min.js"></script>
	<script src="js/avatar/avatar.js"></script>
	<iframe name="uploadframe" id="uploadframe" style="display: none;"></iframe>
	<iframe name="rectframe" id="rectframe" style="display: none;"></iframe>
</form>
</body>
</html>