<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset={UC_CHARSET}" />
<title>UCenter Administrator's Control Panel</title>
<link rel="stylesheet" href="images/admincp.css" type="text/css" media="all" />
<meta content="Comsenz Inc." name="Copyright" />
</head>
<body><div id="append"></div>
<!--{if !empty($iframe) && !empty($user)}-->
	<a class="othersoff" style="float:right;text-align:center" id="header_menu" onclick="headermenu(this)">{lang menu}</a>
	<ul id="header_menu_menu" style="display: none">
		<li><a href="{UC_ADMINSCRIPT}?m=frame&a=main&iframe=1" target="main" class="tabon">{lang menu_index}</a></li>
		<!--{if $user['allowadminsetting'] || $user['isfounder']}--><li><a href="{UC_ADMINSCRIPT}?m=setting&a=ls&iframe=1" target="main">{lang menu_basic_setting}</a></li><!--{/if}-->
		<!--{if $user['allowadminsetting'] || $user['isfounder']}--><li><a href="{UC_ADMINSCRIPT}?m=setting&a=register&iframe=1" target="main">{lang menu_register_setting}</a></li><!--{/if}-->
		<!--{if $user['allowadminsetting'] || $user['isfounder']}--><li><a href="{UC_ADMINSCRIPT}?m=setting&a=mail&iframe=1" target="main">{lang menu_mail_setting}</a></li><!--{/if}-->
		<!--{if $user['allowadminapp'] || $user['isfounder']}--><li><a href="{UC_ADMINSCRIPT}?m=app&a=ls&iframe=1" target="main">{lang menu_application}</a></li><!--{/if}-->
		<!--{if $user['allowadminuser'] || $user['isfounder']}--><li><a href="{UC_ADMINSCRIPT}?m=user&a=ls&iframe=1" target="main">{lang menu_manager_user}</a></li><!--{/if}-->
		<!--{if $user['isfounder']}--><li><a href="{UC_ADMINSCRIPT}?m=admin&a=ls&iframe=1" target="main">{lang menu_admin_user}</a></li><!--{/if}-->
		<!--{if $user['allowadminpm'] || $user['isfounder']}--><li><a href="{UC_ADMINSCRIPT}?m=pm&a=ls&iframe=1" target="main">{lang menu_pm}</a></li><!--{/if}-->
		<!--{if $user['allowadmincredits'] || $user['isfounder']}--><li><a href="{UC_ADMINSCRIPT}?m=credit&a=ls&iframe=1" target="main">{lang menu_credit_exchange}</a></li><!--{/if}-->
		<!--{if $user['allowadminbadword'] || $user['isfounder']}--><li><a href="{UC_ADMINSCRIPT}?m=badword&a=ls&iframe=1" target="main">{lang menu_censor_word}</a></li><!--{/if}-->
		<!--{if $user['allowadmindomain'] || $user['isfounder']}--><li><a href="{UC_ADMINSCRIPT}?m=domain&a=ls&iframe=1" target="main">{lang menu_domain_list}</a></li><!--{/if}-->
		<!--{if $user['allowadmindb'] || $user['isfounder']}--><li><a href="{UC_ADMINSCRIPT}?m=db&a=ls&iframe=1" target="main">{lang menu_db}</a></li><!--{/if}-->
		<!--{if $user['isfounder']}--><li><a href="{UC_ADMINSCRIPT}?m=feed&a=ls&iframe=1" target="main">{lang menu_data_list}</a></li><!--{/if}-->
		<!--{if $user['allowadmincache'] || $user['isfounder']}--><li><a href="{UC_ADMINSCRIPT}?m=cache&a=update&iframe=1" target="main">{lang menu_update_cache}</a></li><!--{/if}-->
		<!--{if $user['isfounder']}--><li><a href="{UC_ADMINSCRIPT}?m=plugin&a=filecheck&iframe=1" target="main">{lang plugin}</a></li><!--{/if}-->
		<a href="{UC_ADMINSCRIPT}?m=user&a=logout" target="main">{lang menu_logout}</a>
	</ul>
<!--{/if}-->
<script type="text/javascript">
	function headermenu(ctrl) {
		ctrl.className = ctrl.className == 'otherson' ? 'othersoff' : 'otherson';
		var menu = document.getElementById('header_menu_body');
		if(!menu) {
			menu = document.createElement('div');
			menu.id = 'header_menu_body';
			menu.innerHTML = '<ul>' + document.getElementById('header_menu_menu').innerHTML + '</ul>';
			var obj = ctrl;
			var x = ctrl.offsetLeft;
			var y = ctrl.offsetTop;
			while((obj = obj.offsetParent) != null) {
				x += obj.offsetLeft;
				y += obj.offsetTop;
			}
			menu.style.left = x + 'px';
			menu.style.top = y + ctrl.offsetHeight + 'px';
			menu.className = 'togglemenu';
			menu.style.display = '';
			document.body.appendChild(menu);
		} else {
			menu.style.display = menu.style.display == 'none' ? '' : 'none';
		}
	}
</script>
