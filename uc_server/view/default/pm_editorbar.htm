<script src="js/pm_editor.js" type="text/javascript"></script>
<script type="text/javascript">
lang['pm_prompt_list'] = '{lang pm_prompt_list}';
lang['pm_prompt_img'] = '{lang pm_prompt_img}';
lang['pm_prompt_url'] = '{lang pm_prompt_url}';
lang['pm_prompt_email'] = '{lang pm_prompt_email}';
</script>

<div class="editor">
<a id="editor_b" href="###" onclick="insertunit('[b]', '[/b]')" title="{lang editor_b}"></a>
<a id="editor_i" href="###" onclick="insertunit('[i]', '[/i]')" title="{lang editor_i}"></a>
<a id="editor_u" href="###" onclick="insertunit('[u]', '[/u]')" title="{lang editor_u}"></a>
<a id="editor_color" href="###" onmouseover="showmenu(event, this.id)" title="{lang editor_color}"></a>
<a id="editor_alignleft" href="###" onclick="insertunit('[align=left]', '[/align]')" title="{lang editor_alignleft}"></a>
<a id="editor_aligncenter" href="###" onclick="insertunit('[align=center]', '[/align]')" title="{lang editor_aligncenter}"></a>
<a id="editor_alignright" href="###" onclick="insertunit('[align=right]', '[/align]')" title="{lang editor_alignright}"></a>
<a id="editor_url" href="###" onclick="inserttag('url', 1)" title="{lang editor_url}"></a>
<a id="editor_email" href="###" onclick="inserttag('email', 1)" title="{lang editor_email}"></a>
<a id="editor_img" href="###" onclick="inserttag('img')" title="{lang editor_img}"></a>
<a id="editor_list1" href="###" onclick="insertlist('1')" title="{lang editor_list1}"></a>
<a id="editor_lista" href="###" onclick="insertlist()" title="{lang editor_lista}"></a>
<a id="editor_indent" href="###" onclick="insertunit('[indent]', '[/indent]')" title="{lang editor_indent}"></a>
<a id="editor_floatleft" href="###" onclick="insertunit('[float=left]', '[/float]')" title="{lang editor_floatleft}"></a>
<a id="editor_floatright" href="###" onclick="insertunit('[float=right]', '[/float]')" title="{lang editor_floatright}"></a>
<a id="editor_quote" href="###" onclick="insertunit('[quote]', '[/quote]')" title="{lang editor_quote}"></a>
<a id="editor_code" href="###" onclick="insertunit('[code]', '[/code]')" title="{lang editor_code}"></a>
</div>
<!--{eval $coloroptions = array('Black', 'Sienna', 'DarkOliveGreen', 'DarkGreen', 'DarkSlateBlue', 'Navy', 'Indigo', 'DarkSlateGray', 'DarkRed', 'DarkOrange', 'Olive', 'Green', 'Teal', 'Blue', 'SlateGray', 'DimGray', 'Red', 'SandyBrown', 'YellowGreen','SeaGreen', 'MediumTurquoise', 'RoyalBlue', 'Purple', 'Gray', 'Magenta', 'Orange', 'Yellow', 'Lime', 'Cyan', 'DeepSkyBlue', 'DarkOrchid', 'Silver', 'Pink', 'Wheat', 'LemonChiffon', 'PaleGreen', 'PaleTurquoise', 'LightBlue', 'Plum', 'White')}-->
<div id="editor_color_menu" style="display: none;">
<!--{loop $coloroptions $key $colorname}-->
	<input type="button" style="background-color: $colorname" onclick="insertunit('[color=$colorname]', '[/color]')" />
<!--{/loop}-->
</div>