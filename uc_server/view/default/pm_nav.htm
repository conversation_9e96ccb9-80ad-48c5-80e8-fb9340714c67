<script type="text/javascript">
function checkall(form, prefix, checkall) {
	var checkall = checkall ? checkall : 'chkall';
	for(var i = 0; i < form.elements.length; i++) {
		var e = form.elements[i];
		if(e.name && e.name != checkall && (!prefix || (prefix && e.name.match(prefix)))) {
			e.checked = form.elements[checkall].checked;
		}
	}
}

function toggle_collapse(objname, ctrlobj) {
	var obj = document.getElementById(objname);
	if(obj.style.display == '') {
		obj.style.display = 'none';
		ctrlobj.innerHTML = '<img src="images/default/spread.gif" />';
	} else {
		obj.style.display = '';
		ctrlobj.innerHTML = '<img src="images/default/shrink.gif" />';
	}
}

function ctlent(event) {
	if((event.ctrlKey && event.keyCode == 13) || (event.altKey && event.keyCode == 83)) {
		document.getElementById('postpmform').saveoutbox.value = 0;
		document.getElementById('postpmform').submit();
	}
}
</script>

<div class="ucnav">
<a <!--{if $filter == 'newpm'}-->class="ucontype" <!--{/if}-->href="index.php?m=pm_client&a=ls&filter=newpm">{lang new_pm}<!--{if $unreadpmnum}--><strong>[{$unreadpmnum}]</strong><!--{/if}--></a>
<a <!--{if $filter == 'privatepm'}-->class="ucontype" <!--{/if}-->href="index.php?m=pm_client&a=ls&filter=privatepm">{lang private_pm}<!--{if $pmnum_private}--><strong>[{$pmnum_private}]</strong><!--{/if}--></a>
<a <!--{if $folder == 'blackls'}-->class="ucontype" <!--{/if}-->href="index.php?m=pm_client&a=ls&folder=blackls">{lang pm_ignorelist}</a>
<a <!--{if $folder == 'send'}-->class="ucontype sendpmontype" <!--{else}--> class="sendpm" <!--{/if}-->href="index.php?m=pm_client&a=send&folder=send">{lang pm_send}</a>

<!--{if $unreadpmnum}-->
	<span class="navinfo">
		<img src="images/default/newpm.gif" />
		<!--{if $unreadpmnum}--><strong>$unreadpmnum</strong> <a <!--{if $filter == 'newpm'}-->class="ontype" <!--{/if}-->href="index.php?m=pm_client&a=ls&filter=newpm">{lang pm_unread}</a><!--{/if}-->
	</span>
<!--{/if}-->
</div>